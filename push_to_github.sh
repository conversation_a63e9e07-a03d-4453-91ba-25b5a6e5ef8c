#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
# Treat unset variables as an error when substituting.
# Prevent errors in a pipeline from being masked.
set -euo pipefail

echo ">>> Checking Git status..."

# Check if the working directory is clean (no uncommitted changes or untracked files)
if [[ -n $(git status --porcelain) ]]; then
  echo "--------------------------------------------------" >&2
  echo "ERROR: Working directory is not clean." >&2
  echo "Please commit, stash, or clean your working directory before pushing." >&2
  echo "--------------------------------------------------" >&2
  git status --short >&2
  echo "--------------------------------------------------" >&2
  exit 1
else
  echo "Working directory is clean."
fi

# Get the current branch name
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
echo ">>> Current branch is '$CURRENT_BRANCH'."

# Fetch the latest changes from the remote 'origin'
echo ">>> Fetching latest changes from origin..."
git fetch origin

# Check if the local branch is behind the remote - optional but recommended
# LOCAL=$(git rev-parse @)
# REMOTE=$(git rev-parse @{u}) # @{u} is short for the upstream branch
# BASE=$(git merge-base @ @{u})

# if [ $LOCAL = $REMOTE ]; then
#     echo "Local branch is up-to-date with remote."
# elif [ $LOCAL = $BASE ]; then
#     echo "Need to pull, remote is ahead. Run 'git pull' first."
#     # exit 1 # Uncomment this to enforce pulling before pushing
# elif [ $REMOTE = $BASE ]; then
#     echo "Local branch is ahead of remote. Ready to push."
# else
#     echo "Branches have diverged. Consider pulling/rebasing."
# fi
# Note: The above check requires the upstream branch to be set (e.g., via git push -u)
# Keeping it simpler for now, git push itself will fail if behind (by default).

# Push the current branch to the remote 'origin'
echo ">>> Pushing branch '$CURRENT_BRANCH' to origin..."
git push origin "$CURRENT_BRANCH"

echo "--------------------------------------------------"
echo ">>> Successfully pushed branch '$CURRENT_BRANCH' to origin."
echo "--------------------------------------------------"

exit 0
### Specification: GUI-Based YouTube Video Downloader in Go

#### Project Name
**GoTube Downloader**

#### Overview
GoTube Downloader is a desktop application built in Go with a sleek, modern GUI. It allows users to download YouTube videos in video or audio formats, with selectable quality options, while preserving metadata (e.g., title, description, thumbnail). The app supports batch downloading, custom download paths, a progress bar, and a download history stored in JSON. It is designed to be robust, user-friendly, and production-ready.

#### Features
1. **Video and Audio Downloading**
   - Download YouTube videos as video files (e.g., MP4) or audio files (e.g., MP3, M4A).
   - Support selectable formats and quality options (e.g., 720p, 1080p for video; 128kbps, 320kbps for audio).
   - Preserve metadata such as title, author, and description in the downloaded file.

2. **GUI Requirements (see image as reference)**
   - Modern, clean, and visually appealing design with a dark theme option.
   - Input field for YouTube URL(s).
   - Dropdown menus for selecting format (video/audio) and quality.
   - File explorer dialog to choose the download directory.
   - Progress bar for each download with percentage completion and estimated time remaining.
   - Button to start/cancel downloads.
   - Batch download interface (e.g., a list or text area to input multiple URLs).

3. **Batch Downloading**
   - Allow users to input multiple YouTube URLs (via copy-paste or file import).
   - Process downloads concurrently with configurable limits (e.g., max 3 simultaneous downloads).

4. **Download History**
   - Store history of completed downloads in a JSON file.
   - Fields in JSON: URL, title, format, quality, download path, timestamp.
   - Display history in the GUI with options to filter, sort, or clear it.

5. **Robustness**
   - Handle errors gracefully (e.g., invalid URLs, network issues, storage limits).
   - Retry failed downloads automatically (configurable retry count).
   - Log errors to a file for debugging.

6. **Cross-Platform**
   - Compatible with Windows, macOS, and Linux.

#### Tech Stack
- **Language**: Go (Golang)
- **GUI Framework**: Fyne (lightweight, cross-platform GUI toolkit for Go)
- **YouTube API**: Use `yt-dlp` (via Go subprocess) or a Go library like `github.com/kkdai/youtube` for downloading and metadata extraction.
- **File Handling**: Standard Go `os` and `ioutil` packages.
- **JSON**: Go’s built-in `encoding/json` for download history.
- **Concurrency**: Go goroutines and channels for batch downloading.
- **Logging**: `log` package or `github.com/sirupsen/logrus` for error logging.

#### Architecture
1. **Main Components**
   - **GUI Layer**: Built with Fyne, responsible for user interaction and displaying progress/history.
   - **Downloader Engine**: Handles URL parsing, format selection, and downloading logic.
   - **History Manager**: Manages JSON storage and retrieval of download history.
   - **Error Handler**: Centralizes error logging and user notifications.

2. **Directory Structure**
   ```
   gotube-downloader/
   ├── cmd/                  # Entry point
   │   └── main.go          # Main application file
   ├── internal/            # Internal packages
   │   ├── gui/            # GUI components (windows, widgets)
   │   ├── downloader/     # Download logic and yt-dlp integration
   │   ├── history/        # JSON history management
   │   └── logger/         # Logging utilities
   ├── assets/              # Icons, styles, and themes
   ├── config/              # Configuration files (e.g., default paths)
   └── downloads/           # Default download directory
   ```

#### Detailed Requirements
1. **GUI Layout**
   - **Main Window**:
     - Top: Input field for URLs (supports multiple lines for batch downloading).
     - Middle: Format dropdown (e.g., MP4, MP3), Quality dropdown (e.g., 720p, 320kbps), Path selector button.
     - Bottom: Start/Cancel buttons, progress bar(s) for active downloads.
   - **History Tab**: Table or list showing past downloads with clickable paths to open files.
   - **Settings**: Toggle dark mode, set max concurrent downloads, configure retry attempts.

2. **Downloader Engine**
   - Use `yt-dlp` (preferred for robustness) via `exec.Command` to fetch videos/audio.
   - Parse available formats and qualities dynamically from YouTube.
   - Embed metadata using `yt-dlp` flags (e.g., `--add-metadata`).
   - Support cancellation mid-download via goroutine termination.

3. **Batch Downloading**
   - Queue URLs in a slice and process them with a worker pool (goroutines).
   - Update progress bars in real-time using channels to communicate with the GUI.

4. **History Management**
   - JSON file stored in `config/history.json`.
   - Example JSON structure:
     ```json
     [
       {
         "url": "https://youtube.com/watch?v=abc123",
         "title": "Sample Video",
         "format": "mp4",
         "quality": "1080p",
         "path": "/downloads/sample.mp4",
         "timestamp": "2025-03-17T10:00:00Z"
       }
     ]
     ```
   - Load history on app start and save after each successful download.

5. **Error Handling**
   - Display user-friendly error messages in the GUI (e.g., "Invalid URL" or "No space left on device").
   - Log detailed errors to `logs/error.log`.

6. **Styling**
   - Use a dark theme by default with a toggle for light mode.
   - Include a custom app icon and smooth animations for progress bars.

#### Production-Ready Considerations
- **Packaging**: Use `fyne package` to create standalone binaries for each OS.
- **Dependencies**: Bundle `yt-dlp` binary with the app. 
- **License**: MIT License for open-source distribution.
- **Documentation**: README with installation, usage, and troubleshooting guides.

#### Example Usage
1. User opens the app, pastes "https://youtube.com/watch?v=abc123" into the input field.
2. Selects "MP3" format and "320kbps" quality, chooses `/music` as the download path.
3. Clicks "Start" and watches the progress bar reach 100%.
4. Sees the file in `/music` with metadata intact and history updated in the GUI.

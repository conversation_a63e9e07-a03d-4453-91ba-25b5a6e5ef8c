package main

import (
	"os"
	"testing"
)

// TestMain tests the main function
func TestMain(t *testing.T) {
	// Skip this test if running in CI environment
	if os.Getenv("CI") != "" {
		t.<PERSON>p("Skipping test in CI environment")
	}

	// Set a flag to indicate we're testing
	os.Setenv("GOTUBE_TEST", "1")

	// We can't actually call main() because it would start the GUI
	// and block the test, but we can test that the package imports correctly
	// and that the main function exists
	t.Log("Main package imports correctly")
}

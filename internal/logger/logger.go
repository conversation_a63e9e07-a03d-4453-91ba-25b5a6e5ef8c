package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// <PERSON><PERSON> handles application logging
type Logger struct {
	logFile    *os.File
	logPath    string
	mutex      sync.Mutex
	forceDebug bool // Set to true to force debug output to terminal
}

// NewLogger creates a new logger instance
func NewLogger(logDir string) (*Logger, error) {
	// Force create the log directory with full permissions
	os.MkdirAll(logDir, 0777)

	// Create log file path with just the date (not timestamp)
	timeStr := time.Now().Format("2006-01-02")
	logPath := filepath.Join(logDir, fmt.Sprintf("gotube_%s.log", timeStr))

	// Try to create the log file
	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0666)
	if err != nil {
		// If we can't create the log file, return a dummy logger
		// Don't try to use other directories - stick with the project directory
		return &Logger{forceDebug: true}, nil
	}

	// Create the logger
	logger := &Logger{
		logFile: logFile,
		logPath: logPath,
	}

	// Write a test message to verify logging works
	logger.writeToFile(fmt.Sprintf("[%s] %s: %s\n",
		time.Now().Format("2006-01-02 15:04:05"),
		"INFO",
		"Logger initialized successfully"))

	return logger, nil
}

// writeToFile writes directly to the log file with error handling
func (l *Logger) writeToFile(message string) {
	if l.logFile == nil {
		if l.forceDebug {
			fmt.Fprintf(os.Stderr, "DEBUG: Logger has no file: %s\n", message)
		}
		return
	}

	l.mutex.Lock()
	defer l.mutex.Unlock()

	_, err := l.logFile.WriteString(message)
	if err != nil {
		if l.forceDebug {
			fmt.Fprintf(os.Stderr, "DEBUG: Failed to write to log file: %v\n", err)
		}
	}

	// Force flush to disk
	l.logFile.Sync()
}

// log writes a message to the log file with the specified level
func (l *Logger) log(level, format string, args ...interface{}) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	msg := fmt.Sprintf(format, args...)
	logEntry := fmt.Sprintf("[%s] %s: %s\n", timestamp, level, msg)

	l.writeToFile(logEntry)
}

// Info logs an informational message
func (l *Logger) Info(format string, args ...interface{}) {
	l.log("INFO", format, args...)
}

// Error logs an error message
func (l *Logger) Error(format string, args ...interface{}) {
	l.log("ERROR", format, args...)
}

// Warn logs a warning message
func (l *Logger) Warn(format string, args ...interface{}) {
	l.log("WARN", format, args...)
}

// Fatal logs a fatal error message
func (l *Logger) Fatal(format string, args ...interface{}) {
	l.log("FATAL", format, args...)
}

// Debug logs a debug message (only if forceDebug is true)
func (l *Logger) Debug(format string, args ...interface{}) {
	if l.forceDebug {
		fmt.Printf(format+"\n", args...)
	}
	l.log("DEBUG", format, args...)
}

// GetLogPath returns the current log file path
func (l *Logger) GetLogPath() string {
	return l.logPath
}

// Close closes the log file
func (l *Logger) Close() error {
	if l.logFile != nil {
		l.Info("Logger closing")
		l.logFile.Sync() // Force flush before closing
		err := l.logFile.Close()
		l.logFile = nil
		return err
	}
	return nil
}

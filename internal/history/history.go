package history

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
)

type DownloadRecord struct {
	URL         string    `json:"url"`
	Title       string    `json:"title"`
	Format      string    `json:"format"`
	Quality     string    `json:"quality"`
	Path        string    `json:"path"`
	Timestamp   time.Time `json:"timestamp"`
	Description string    `json:"description,omitempty"`
	Thumbnail   string    `json:"thumbnail,omitempty"`
	Duration    int       `json:"duration,omitempty"`
	Author      string    `json:"author,omitempty"`
}

type HistoryManager struct {
	records []DownloadRecord
	db      *database.DB // Database is required
	loaded  bool         // Flag to track if data has been loaded
}

// NewHistoryManager creates a new history manager with a database connection.
// It defers loading data if the database is encrypted.
func NewHistoryManager(db *database.DB) (*HistoryManager, error) {
	if db == nil {
		return nil, fmt.Errorf("database connection is required")
	}

	hm := &HistoryManager{
		records: make([]DownloadRecord, 0),
		db:      db,
		loaded:  false, // Initially not loaded
	}

	// --- Defer loading if DB is encrypted ---
	// We attempt loading only if the DB is not encrypted on initialization.
	// If it IS encrypted, Load() must be called later after unlocking.
	if !db.IsEncrypted() {
		// Attempt initial load/migration only for non-encrypted or new DBs
		if err := hm.Load(); err != nil {
			// Non-critical error, log and continue with empty history
			fmt.Printf("Warning: Initial history load/migration failed (DB not encrypted): %v\n", err)
			// Ensure loaded is false if load fails
			hm.loaded = false
		}
	} else {
		fmt.Println("HistoryManager: Database is encrypted, deferring initial load.")
	}

	return hm, nil
}

// Load attempts to load history data from JSON (migration) or the database.
// This should be called after the DB is confirmed to be open and unlocked.
func (hm *HistoryManager) Load() error {
	if hm.loaded {
		fmt.Println("HistoryManager: Already loaded.")
		return nil // Avoid reloading unnecessarily
	}
	if hm.db == nil {
		return fmt.Errorf("cannot load history, database is nil")
	}

	// Check for existing JSON history file for migration first
	workDir, err := os.Getwd()
	if err != nil {
		fmt.Printf("Warning: Failed to get working directory for history migration check: %v\n", err)
	} else {
		historyPath := filepath.Join(workDir, "history.json")
		if _, err := os.Stat(historyPath); err == nil {
			// JSON history exists, migrate it to the database
			fmt.Println("HistoryManager: Migrating history from JSON...")
			err := hm.migrateFromJSON(historyPath)
			if err != nil {
				// Log the error but potentially try loading from DB anyway? Or return error?
				// Let's return the migration error for now.
				hm.loaded = false // Mark as not successfully loaded
				return fmt.Errorf("failed to migrate history from JSON: %w", err)
			}
			hm.loaded = true
			fmt.Println("HistoryManager: Migration from JSON successful.")
			// Optionally delete the JSON file after successful migration
			// os.Remove(historyPath)
			return nil // Migration successful, don't load from DB
		}
	}

	// No JSON file found or checked, load from database
	fmt.Println("HistoryManager: Loading history from database...")
	err = hm.loadFromDatabase()
	if err != nil {
		hm.loaded = false // Mark as not successfully loaded
		return fmt.Errorf("failed to load history from database: %w", err)
	}

	hm.loaded = true
	fmt.Println("HistoryManager: Load from database successful.")
	return nil
}

// migrateFromJSON loads history from a JSON file and saves it to the database
func (hm *HistoryManager) migrateFromJSON(path string) error {
	fmt.Printf("Migrating history from JSON: %s\n", path)

	// Read the JSON file
	data, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("failed to read JSON history: %w", err)
	}

	// Parse the JSON data
	var records []DownloadRecord
	if err := json.Unmarshal(data, &records); err != nil {
		return fmt.Errorf("failed to parse JSON history: %w", err)
	}

	// Use a transaction for migration
	return hm.db.Transaction(func(tx *sql.Tx) error {
		// Clear existing history in DB before migration (optional, depends on desired behavior)
		// _, err = tx.Exec("DELETE FROM history")
		// if err != nil {
		// 	return fmt.Errorf("failed to clear existing history during migration: %w", err)
		// }

		// Migrate each record to the database
		for _, record := range records {
			entry := database.HistoryEntry{
				URL:       record.URL,
				Title:     record.Title,
				Format:    record.Format,
				Quality:   record.Quality,
				Path:      record.Path,
				Timestamp: record.Timestamp,
			}
			if record.Description != "" {
				entry.Description = sql.NullString{String: record.Description, Valid: true}
			}
			if record.Thumbnail != "" {
				entry.Thumbnail = sql.NullString{String: record.Thumbnail, Valid: true}
			}
			if record.Duration != 0 {
				entry.Duration = sql.NullInt64{Int64: int64(record.Duration), Valid: true}
			}
			if record.Author != "" {
				entry.Author = sql.NullString{String: record.Author, Valid: true}
			}

			// Add using the transaction
			_, err := hm.db.AddHistoryTx(tx, entry) // Assuming AddHistoryTx exists
			if err != nil {
				return fmt.Errorf("failed to add history entry to database during migration: %w", err)
			}
		}
		return nil
	})
	// Update in-memory records after successful transaction
	// hm.records = records // Loading from DB after migration is safer
}

// loadFromDatabase loads the history from the database
func (hm *HistoryManager) loadFromDatabase() error {
	// Get all history entries
	entries, err := hm.db.GetHistory() // This performs the DB query
	if err != nil {
		return err // Return the original error (e.g., "database is closed")
	}

	// Convert to download records
	hm.records = make([]DownloadRecord, len(entries))
	for i, entry := range entries {
		record := DownloadRecord{
			URL:       entry.URL,
			Title:     entry.Title,
			Format:    entry.Format,
			Quality:   entry.Quality,
			Path:      entry.Path,
			Timestamp: entry.Timestamp,
		}
		if entry.Description.Valid {
			record.Description = entry.Description.String
		}
		if entry.Thumbnail.Valid {
			record.Thumbnail = entry.Thumbnail.String
		}
		if entry.Duration.Valid {
			record.Duration = int(entry.Duration.Int64)
		}
		if entry.Author.Valid {
			record.Author = entry.Author.String
		}
		hm.records[i] = record
	}

	return nil
}

// AddRecord adds a download record to the history
func (hm *HistoryManager) AddRecord(record DownloadRecord) error {
	if hm.db == nil {
		return fmt.Errorf("cannot add record, database is nil")
	}
	record.Timestamp = time.Now()

	// Add to database
	entry := database.HistoryEntry{
		URL:       record.URL,
		Title:     record.Title,
		Format:    record.Format,
		Quality:   record.Quality,
		Path:      record.Path,
		Timestamp: record.Timestamp,
	}
	if record.Description != "" {
		entry.Description = sql.NullString{String: record.Description, Valid: true}
	}
	if record.Thumbnail != "" {
		entry.Thumbnail = sql.NullString{String: record.Thumbnail, Valid: true}
	}
	if record.Duration != 0 {
		entry.Duration = sql.NullInt64{Int64: int64(record.Duration), Valid: true}
	}
	if record.Author != "" {
		entry.Author = sql.NullString{String: record.Author, Valid: true}
	}

	_, err := hm.db.AddHistory(entry)
	if err != nil {
		return fmt.Errorf("failed to add history to database: %w", err)
	}

	// Update in-memory records only if loaded, otherwise rely on next GetRecords call
	if hm.loaded {
		hm.records = append(hm.records, record)
	}

	return nil
}

// GetRecords returns all download records
func (hm *HistoryManager) GetRecords() []DownloadRecord {
	// Ensure data is loaded if it hasn't been already
	if !hm.loaded {
		fmt.Println("HistoryManager: GetRecords called before initial load, attempting load...")
		err := hm.Load()
		if err != nil {
			fmt.Printf("Warning: Failed to load history in GetRecords: %v\n", err)
			// Return empty or potentially stale data if load failed
			return []DownloadRecord{}
		}
	}
	// Return current in-memory records
	return hm.records
}

// ClearHistory clears all download records
func (hm *HistoryManager) ClearHistory() error {
	if hm.db == nil {
		return fmt.Errorf("cannot clear history, database is nil")
	}
	// Clear database
	if err := hm.db.ClearHistory(); err != nil {
		return fmt.Errorf("failed to clear history from database: %w", err)
	}

	// Clear in-memory records
	hm.records = make([]DownloadRecord, 0)
	hm.loaded = true // Mark as loaded (with empty data) after clearing

	return nil
}

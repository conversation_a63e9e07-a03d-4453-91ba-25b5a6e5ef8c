// File: internal/i18n/translations.go

package i18n

// Language defines the structure for supported languages.
type Language struct {
	Code string
	Name string
}

var (
	// SupportedLanguages lists the languages available in the UI dropdown.
	SupportedLanguages = []Language{
		{Code: "en", Name: "English"},
		{Code: "de", Name: "<PERSON><PERSON><PERSON>"},
		// Add other supported languages here
	}

	// translations map holds all translation strings [languageCode][translationKey]
	translations = map[string]map[string]interface{}{
		"en": {
			"app_title":                            "GoTube Video Downloader",
			"format":                               "Format",
			"format_placeholder":                   "Select Format",
			"quality":                              "Quality",
			"quality_placeholder":                  "Select Quality",
			"select_path":                          "Select Path",
			"button_browse":                        "Browse",
			"button_start":                         "Start",
			"button_cancel":                        "Cancel",
			"cancelling":                           "Cancelling",
			"canceled":                             "Cancelled",
			"button_fetch_duration":                "Fetch Duration",
			"checkbox_sponsorblock":                "SponsorBlock",
			"label_start_time":                     "Start Time",
			"label_end_time":                       "End Time",
			"label_duration":                       "Duration",
			"enter_urls":                           "Enter YouTube URLs, one per line",
			"enter_urls_label":                     "YouTube URLs",
			"enter_start_time":                     "Enter start time",
			"enter_end_time":                       "Enter end time",
			"download_path_label":                  "Download Path",
			"path_updated":                         "Download path updated successfully",
			"status_idle":                          "Ready",
			"language":                             "Language",
			"window":                               "Window",
			"toggle_resize":                        "Toggle Resize",
			"remove_sponsor_segments":              "Remove Sponsor Segments",
			"trim_video":                           "Trim Video",
			"apply_trim":                           "Apply Trim",
			"invalid_time_format":                  "Invalid time format. Use MM:SS or HH:MM:SS",
			"invalid_time_format_short":            "Invalid time format",
			"time_cannot_be_negative":              "Time cannot be negative",
			"time_exceeds_duration_format":         "Time cannot exceed video duration (%s)",
			"start_time_must_be_before_end":        "Start time must be before end time",
			"end_time_must_be_after_start":         "End time must be after start time",
			"trim_settings_applied":                "Trim settings applied",
			"use_light_theme":                      "Use Light Theme",
			"settings":                             "Settings",
			"select_videos":                        "Select Videos",
			"select_all":                           "Select All",
			"download_selected":                    "Download Selected",
			"playlist_detected":                    "Playlist Detected",
			"extract_audio":                        "Extract Audio",
			"audio_format":                         "Audio Format",
			"estimated_size":                       "Estimated Size",
			"preview":                              "Preview",
			"fetch_info":                           "Fetch Info",
			"custom_filename":                      "Custom Filename",
			"filename_template":                    "Filename Template",
			"download_queue":                       "Download Queue",
			"pause":                                "Pause",
			"resume":                               "Resume",
			"move_up":                              "Move Up",
			"move_down":                            "Move Down",
			"remove":                               "Remove",
			"info":                                 "Information",
			"success":                              "Success",
			"no_videos_selected":                   "No videos selected",
			"duration":                             "Duration",
			"author":                               "Author",
			"filesize":                             "Filesize",
			"enter_at_least_one_url":               "Please enter at least one YouTube URL",
			"enter_single_url_first":               "Please enter a single YouTube URL first",
			"select_format_quality":                "Please select a format and quality",
			"error_empty_quality":                  "Please select a quality for video formats",
			"error_empty_format":                   "Please select a format",
			"error_empty_url":                      "Please enter a YouTube URL",
			"error_clearing_history":               "Failed to clear history",
			"history_cleared":                      "History cleared",
			"confirm_clear_history_title":          "Confirm Clear History",
			"confirm_clear_history_message":        "Are you sure you want to clear the download history?",
			"select_history_item":                  "Please select an item from the history",
			"selection":                            "Selection",
			"status_download_complete":             "Download of %s completed",
			"status_playlist_complete":             "Playlist download of %d videos completed",
			"dialog_download_title":                "Download Complete",
			"dialog_download_success":              "Download of %s completed",
			"dialog_playlist_title":                "Playlist Download Complete",
			"dialog_playlist_success":              "Playlist download of %d videos completed",
			"error_download_failed_dialog_title":   "Download Failed",
			"error_download_failed_dialog_message": "Failed to download '%s'.\n\nReason: %v",
			"status_initializing":                  "Initializing download...",
			"status_fetching_metadata":             "Fetching metadata...",
			"status_metadata_fetched_for":          "Metadata fetched for: %s",
			"status_preparing_path":                "Preparing download path...",
			"status_download_target":               "Download target: %s",
			"status_executing_command":             "Executing download command...",
			"status_merging_formats":               "Merging video and audio formats...",
			"status_extracting_audio":              "Extracting audio...",
			"status_embedding_thumbnail":           "Embedding thumbnail...",
			"status_processing_ffmpeg":             "Processing with FFmpeg...",
			"status_deleting_originals":            "Cleaning up original files...",
			"status_file_already_downloaded":       "File already downloaded, skipping.",
			"status_verifying_file":                "Verifying downloaded file...",
			"status_cleaning_up":                   "Cleaning up temporary files...",
			"status_retrying_in":                   "Download failed. Retrying in %v...",
			"status_download_failed_final":         "Download failed after multiple attempts.",
			"status_no_videos_selected":            "No videos selected",
			"status_custom_cookies":                "Custom cookies loaded from %s",
			"status_custom_cookies_set":            "Custom cookie file set: %s (for this session)",
			"status_custom_cookies_saved":          "Custom cookie file set and saved: %s",
			"dialog_custom_cookies_success":        "Custom cookies loaded from %s",
			"status_cookies_extracted":             "Cookies extracted successfully", // Legacy?
			"dialog_cookies_success":               "Cookies extracted successfully", // Legacy?
			"status_extracting_cookies":            "Extracting cookies...",          // Legacy?
			"error_extract_cookies":                "Failed to extract cookies: %v",  // Legacy?
			"error_extract_cookies_dialog":         "Failed to extract cookies: %v",  // Legacy?
			"error_custom_cookies":                 "Failed to load custom cookies: %v",
			"error_custom_cookies_dialog":          "Failed to load custom cookies: %v",
			"dialog_cookies_title":                 "Cookies",
			"menu_file":                            "File",
			"menu_extract_cookies":                 "Extract Cookies", // Legacy?
			"menu_custom_cookies":                  "Load Custom Cookies",
			"menu_import_cookies":                  "Import Cookies from Text",
			"menu_use_browser_cookies":             "Use Browser Cookies",
			"menu_filename_template":               "Filename Template",
			"cookie_import_title":                  "Import Cookies from Text",
			"cookie_import_help":                   "Paste your cookies in Netscape format below. You can get these from browser extensions like 'Get cookies.txt' or 'EditThisCookie'.",
			"import":                               "Import",
			"cookie_import_empty":                  "Cookie text cannot be empty",
			"cookie_import_error":                  "Failed to import cookies",
			"cookie_import_success_title":          "Cookies Imported",
			"cookie_import_success":                "Cookies have been successfully imported and encrypted in the database",
			"browser_cookies_title":                "Browser Cookies Settings",
			"browser_cookies_help":                 "Use cookies directly from your browser. Recommended for age-restricted or private content.",
			"use_browser_cookies":                  "Use browser cookies",
			"select_browser":                       "Select browser:",
			"close":                                "Close",
			"menu_exit":                            "Exit",
			"menu_settings":                        "Settings",
			"menu_language":                        "Language",
			"menu_sponsorblock":                    "SponsorBlock",
			"menu_help":                            "Help",
			"menu_about":                           "About",
			"menu_logs":                            "Logs",
			"menu_cookie_guide":                    "Cookie Guide",
			"dialog_logs_title":                    "Logs",
			"dialog_logs_not_available":            "Logs are not available",
			"error_log_dir_not_found":              "Log directory not found: %s",
			"error_open_log_dir":                   "Failed to open log directory: %v",
			"fetch_to_enable_trim":                 "Enter single URL to enable trim",
			"fetch_duration_first":                 "Fetch duration first to enable trim",
			"single_url_only":                      "Single URL only",
			"tab_download":                         "Download",
			"tab_history":                          "History",
			"sponsorblock_enabled":                 "SponsorBlock enabled",
			"sponsorblock_disabled":                "SponsorBlock disabled",
			"dialog_sponsorblock_title":            "SponsorBlock",
			"dialog_sponsorblock_message":          "SponsorBlock was %s",
			"status_sponsorblock":                  "SponsorBlock %s",
			"status_fetching_duration":             "Fetching duration...",
			"fetching":                             "Fetching...",
			"waiting_for_fetch":                    "Waiting for fetch...",
			"error_fetching":                       "Error fetching duration",
			"metadata_fetched_for_format":          "Metadata fetched: %s",
			"error_fetching_metadata_for_format":   "Error fetching metadata: %v",
			"error_fetch_metadata":                 "Error fetching metadata: %v",
			"error_fetch_metadata_dialog":          "Failed to fetch video metadata: %v",
			"status_metadata_fetched":              "Metadata fetched: %s",
			"download_failed_for_format":           "Download failed for %s: %v",
			"fetch_duration":                       "Fetch duration",
			"load_selected":                        "Load Selected URL",
			"clear_history":                        "Clear History",
			"quality_128":                          "128kbps",
			"quality_highest":                      "Highest",
			"select_language":                      "Select Language",
			"language_settings":                    "Language Settings",
			"save":                                 "Save",
			"cancel":                               "Cancel",
			"language_updated_title":               "Language Updated",
			"language_updated_message":             "The application language has been updated.",
			"enabled":                              "Enabled",
			"disabled":                             "Disabled",
			"dialog_close":                         "Close",
			"about_title":                          "About GoTube Video Downloader",
			"about_text":                           "GoTube Video Downloader is a simple, cross-platform YouTube video downloader built with Go and Fyne.\n\nVersion: 1.0.0\nGo Version: %s\n\nThis application uses yt-dlp for downloading videos and ffmpeg for processing them.\n\nDeveloped as an open-source project.",
			"open_in_download_tab":                 "Open in Download Tab",
			"status_url_loaded":                    "URL loaded: %s",
			"all_downloads_completed":              "All downloads completed",
			"downloads_finished_summary_format":    "Download of %d of %d videos completed",
			"status_browser_cookies_toggled":       "Browser cookies %s",
			"status_browser_set":                   "Browser for cookies set to: %s",
			"error_downloader_not_ready":           "Error: Downloader not ready",
			"status_multiple_urls":                 "%d URLs loaded, showing first.",
			"360p":                                 "360p",
			"720p":                                 "720p",
			"1080p":                                "1080p",
			"start":                                "Start",
			"end":                                  "End",
			"date":                                 "Date",
			"url":                                  "URL",
			"title":                                "Title",

			// Install Dependency Keys
			"install_dependency_title":          "Install Dependency",
			"install_dependency_confirm_button": "Install",
			"install_dependency_message_format": "The Python module '%s' is required to use browser cookies but it was not found.\n\nInstall it now using 'python3 -m pip install --user %s'?",
			"installing_dependency_title":       "Installing Dependency",
			"installing_dependency_message":     "Installing %s...",
			"install_success_title":             "Installation Successful",
			"install_success_message_format":    "%s installed successfully.\n\nPlease restart the application and re-enable 'Use Browser Cookies' if needed.",
			"install_error_title":               "Installation Failed",
			"install_error_message_format":      "Failed to install %s.\n\nError:\n%v\n\nOutput:\n%s",
			"status_checking_dependency":        "Checking for Python module '%s'...",
			"status_dependency_missing_prompt":  "Python module '%s' not found. Prompting user to install.",
			"status_dependency_found":           "Python module '%s' found.",

			// Database encryption translations
			"menu_encryption":                     "Encryption",
			"menu_change_password":                "Change Password",
			"menu_enable_encryption":              "Enable Encryption",
			"menu_disable_encryption":             "Disable Encryption",
			"encryption_password_title":           "Database Password",
			"encryption_password_prompt":          "Enter a password to encrypt your database. This will protect your cookies and other sensitive data.",
			"encryption_password_existing_prompt": "This database is encrypted. Please enter the password to unlock it.",
			"encryption_password_new":             "New Password",
			"encryption_password_confirm":         "Confirm Password",
			"encryption_password_current":         "Current Password",
			"encryption_password_mismatch":        "Passwords do not match",
			"encryption_password_empty":           "Password cannot be empty",
			"encryption_password_incorrect":       "Incorrect password.",
			"encryption_already_enabled":          "Encryption is already enabled.",
			"encryption_already_disabled":         "Encryption is already disabled.",
			"encryption_disable_warning":          "Warning: Disabling encryption will store sensitive data like cookies unencrypted.",
			"encryption_enabled":                  "Database encryption enabled",
			"encryption_disabled":                 "Database encryption disabled",
			"encryption_password_changed":         "Database password changed successfully",
			"encryption_error":                    "Encryption error: %v",
			"encryption_skip":                     "Continue without encryption",
			"encryption_skipped":                  "Continuing without encryption. Your data will not be protected.",
			"encryption_enable":                   "Enable Encryption",
			"encryption_disable":                  "Disable Encryption",
			"encryption_password":                 "Password",
			"unlock":                              "Unlock",
			"ok":                                  "OK",
			"path":                                "Path",
			"status_checking_url":                 "Checking URL...",
			"error_check_playlist":                "Error checking if URL is a playlist: %v",
			"status_already_downloading":          "Another download is already in progress.",
			"dialog_path_title":                   "Select Download Path",
			"status_path_set":                     "Download path set to: %s",
			"starting_download":                   "Starting download",
		},
		"de": {
			// ... German translations matching the English keys ...
			"app_title":                            "GoTube Video Downloader",
			"format":                               "Format",
			"format_placeholder":                   "Format auswählen",
			"quality":                              "Qualität",
			"quality_placeholder":                  "Qualität auswählen",
			"select_path":                          "Pfad auswählen",
			"button_browse":                        "Durchsuchen",
			"button_start":                         "Start",
			"button_cancel":                        "Abbrechen",
			"cancelling":                           "Wird abgebrochen",
			"canceled":                             "Abgebrochen",
			"button_fetch_duration":                "Dauer abrufen",
			"checkbox_sponsorblock":                "SponsorBlock",
			"label_start_time":                     "Startzeit",
			"label_end_time":                       "Endzeit",
			"label_duration":                       "Dauer",
			"enter_urls":                           "YouTube-URLs eingeben, eine pro Zeile",
			"enter_urls_label":                     "YouTube-URLs",
			"enter_start_time":                     "Startzeit eingeben",
			"enter_end_time":                       "Endzeit eingeben",
			"download_path_label":                  "Download-Pfad",
			"path_updated":                         "Download-Pfad erfolgreich aktualisiert",
			"status_idle":                          "Bereit",
			"language":                             "Sprache",
			"window":                               "Fenster",
			"toggle_resize":                        "Größenänderung umschalten",
			"remove_sponsor_segments":              "Sponsor-Segmente entfernen",
			"trim_video":                           "Video zuschneiden",
			"apply_trim":                           "Zuschneiden anwenden",
			"invalid_time_format":                  "Ungültiges Zeitformat. Verwende MM:SS oder HH:MM:SS",
			"invalid_time_format_short":            "Ungültiges Zeitformat",
			"time_cannot_be_negative":              "Zeit darf nicht negativ sein",
			"time_exceeds_duration_format":         "Zeit überschreitet Videodauer (%s)",
			"start_time_must_be_before_end":        "Startzeit muss vor Endzeit liegen",
			"end_time_must_be_after_start":         "Endzeit muss nach der Startzeit liegen",
			"trim_settings_applied":                "Zuschneideinstellungen angewendet",
			"use_light_theme":                      "Helles Design verwenden",
			"settings":                             "Einstellungen",
			"select_videos":                        "Videos auswählen",
			"select_all":                           "Alle auswählen",
			"download_selected":                    "Ausgewählte herunterladen",
			"playlist_detected":                    "Playlist erkannt",
			"extract_audio":                        "Audio extrahieren",
			"audio_format":                         "Audioformat",
			"estimated_size":                       "Geschätzte Größe",
			"preview":                              "Vorschau",
			"fetch_info":                           "Info abrufen",
			"custom_filename":                      "Benutzerdefinierter Dateiname",
			"filename_template":                    "Dateinamen-Vorlage",
			"download_queue":                       "Download-Warteschlange",
			"pause":                                "Pause",
			"resume":                               "Fortsetzen",
			"move_up":                              "Nach oben",
			"move_down":                            "Nach unten",
			"remove":                               "Entfernen",
			"info":                                 "Information",
			"success":                              "Erfolg",
			"no_videos_selected":                   "Keine Videos ausgewählt",
			"duration":                             "Dauer",
			"author":                               "Autor",
			"filesize":                             "Dateigröße",
			"enter_at_least_one_url":               "Bitte geben Sie mindestens eine YouTube-URL ein",
			"enter_single_url_first":               "Bitte zuerst eine einzelne YouTube-URL eingeben",
			"select_format_quality":                "Bitte wählen Sie ein Format und eine Qualität aus",
			"error_empty_quality":                  "Bitte wählen Sie eine Qualität für Videoformate aus",
			"error_empty_format":                   "Bitte wählen Sie ein Format aus",
			"error_empty_url":                      "Bitte geben Sie eine YouTube-URL ein",
			"error_clearing_history":               "Verlauf konnte nicht gelöscht werden",
			"history_cleared":                      "Verlauf gelöscht",
			"confirm_clear_history_title":          "Verlauf löschen bestätigen",
			"confirm_clear_history_message":        "Möchten Sie den Download-Verlauf wirklich löschen?",
			"select_history_item":                  "Bitte wählen Sie ein Element aus dem Verlauf aus",
			"selection":                            "Auswahl",
			"status_download_complete":             "Download von %s abgeschlossen",
			"status_playlist_complete":             "Playlist-Download von %d Videos abgeschlossen",
			"dialog_download_title":                "Download abgeschlossen",
			"dialog_download_success":              "Download von %s abgeschlossen",
			"dialog_playlist_title":                "Playlist-Download abgeschlossen",
			"dialog_playlist_success":              "Playlist-Download von %d Videos abgeschlossen",
			"error_download_failed_dialog_title":   "Download fehlgeschlagen",
			"error_download_failed_dialog_message": "Herunterladen von '%s' fehlgeschlagen.\n\nGrund: %v",
			"status_initializing":                  "Download wird initialisiert...",
			"status_fetching_metadata":             "Metadaten werden abgerufen...",
			"status_metadata_fetched_for":          "Metadaten für %s abgerufen",
			"status_preparing_path":                "Download-Pfad wird vorbereitet...",
			"status_download_target":               "Download-Ziel: %s",
			"status_executing_command":             "Download-Befehl wird ausgeführt...",
			"status_merging_formats":               "Video- und Audioformate werden zusammengefügt...",
			"status_extracting_audio":              "Audio wird extrahiert...",
			"status_embedding_thumbnail":           "Vorschaubild wird eingebettet...",
			"status_processing_ffmpeg":             "Verarbeitung mit FFmpeg...",
			"status_deleting_originals":            "Originaldateien werden bereinigt...",
			"status_file_already_downloaded":       "Datei bereits heruntergeladen, wird übersprungen.",
			"status_verifying_file":                "Heruntergeladene Datei wird überprüft...",
			"status_cleaning_up":                   "Temporäre Dateien werden bereinigt...",
			"status_retrying_in":                   "Download fehlgeschlagen. Erneuter Versuch in %v...",
			"status_download_failed_final":         "Download nach mehreren Versuchen fehlgeschlagen.",
			"status_no_videos_selected":            "Keine Videos ausgewählt",
			"status_custom_cookies":                "Benutzerdefinierte Cookies von %s geladen",
			"status_custom_cookies_set":            "Benutzerdefinierte Cookie-Datei gesetzt: %s (für diese Sitzung)",
			"status_custom_cookies_saved":          "Benutzerdefinierte Cookie-Datei gesetzt und gespeichert: %s",
			"dialog_custom_cookies_success":        "Benutzerdefinierte Cookies von %s geladen",
			"status_cookies_extracted":             "Cookies erfolgreich extrahiert",
			"dialog_cookies_success":               "Cookies erfolgreich extrahiert",
			"status_extracting_cookies":            "Cookies werden extrahiert...",
			"error_extract_cookies":                "Cookies konnten nicht extrahiert werden: %v",
			"error_extract_cookies_dialog":         "Cookies konnten nicht extrahiert werden: %v",
			"error_custom_cookies":                 "Benutzerdefinierte Cookies konnten nicht geladen werden: %v",
			"error_custom_cookies_dialog":          "Benutzerdefinierte Cookies konnten nicht geladen werden: %v",
			"dialog_cookies_title":                 "Cookies",
			"menu_file":                            "Datei",
			"menu_extract_cookies":                 "Cookies extrahieren",
			"menu_custom_cookies":                  "Benutzerdefinierte Cookies laden",
			"menu_import_cookies":                  "Cookies aus Text importieren",
			"menu_use_browser_cookies":             "Browser-Cookies verwenden",
			"menu_filename_template":               "Dateinamen-Vorlage",
			"cookie_import_title":                  "Cookies aus Text importieren",
			"cookie_import_help":                   "Fügen Sie Ihre Cookies im Netscape-Format unten ein. Sie können diese von Browser-Erweiterungen wie 'Get cookies.txt' oder 'EditThisCookie' erhalten.",
			"import":                               "Importieren",
			"cookie_import_empty":                  "Cookie-Text darf nicht leer sein",
			"cookie_import_error":                  "Cookies konnten nicht importiert werden",
			"cookie_import_success_title":          "Cookies importiert",
			"cookie_import_success":                "Cookies wurden erfolgreich importiert und in der Datenbank verschlüsselt",
			"browser_cookies_title":                "Browser-Cookies Einstellungen",
			"browser_cookies_help":                 "Verwenden Sie Cookies direkt aus Ihrem Browser. Empfohlen für altersbeschränkte oder private Inhalte.",
			"use_browser_cookies":                  "Browser-Cookies verwenden",
			"select_browser":                       "Browser auswählen:",
			"close":                                "Schließen",
			"menu_exit":                            "Beenden",
			"menu_settings":                        "Einstellungen",
			"menu_language":                        "Sprache",
			"menu_sponsorblock":                    "SponsorBlock",
			"menu_help":                            "Hilfe",
			"menu_about":                           "Über",
			"menu_logs":                            "Protokolle",
			"menu_cookie_guide":                    "Cookie-Anleitung",
			"dialog_logs_title":                    "Protokolle",
			"dialog_logs_not_available":            "Protokolle sind nicht verfügbar",
			"error_log_dir_not_found":              "Protokollverzeichnis nicht gefunden: %s",
			"error_open_log_dir":                   "Protokollverzeichnis konnte nicht geöffnet werden: %v",
			"fetch_to_enable_trim":                 "Einzel-URL eingeben um Trim zu aktivieren",
			"fetch_duration_first":                 "Zuerst Dauer abrufen um Trim zu aktivieren",
			"single_url_only":                      "Nur eine URL",
			"tab_download":                         "Download",
			"tab_history":                          "Verlauf",
			"sponsorblock_enabled":                 "SponsorBlock aktiviert",
			"sponsorblock_disabled":                "SponsorBlock deaktiviert",
			"dialog_sponsorblock_title":            "SponsorBlock",
			"dialog_sponsorblock_message":          "SponsorBlock wurde %s",
			"status_sponsorblock":                  "SponsorBlock %s",
			"status_fetching_duration":             "Dauer wird abgerufen...",
			"fetching":                             "Wird abgerufen...",
			"waiting_for_fetch":                    "Warte auf Abruf...",
			"error_fetching":                       "Fehler beim Abruf der Dauer",
			"metadata_fetched_for_format":          "Metadaten abgerufen: %s",
			"error_fetching_metadata_for_format":   "Fehler beim Abrufen der Metadaten: %v",
			"error_fetch_metadata":                 "Fehler beim Abrufen der Metadaten: %v",
			"error_fetch_metadata_dialog":          "Abrufen der Video-Metadaten fehlgeschlagen: %v",
			"status_metadata_fetched":              "Metadaten abgerufen: %s",
			"download_failed_for_format":           "Download von %s fehlgeschlagen: %v",
			"fetch_duration":                       "Dauer abrufen",
			"load_selected":                        "Ausgewählte URL laden",
			"clear_history":                        "Verlauf löschen",
			"quality_128":                          "128kbps",
			"quality_highest":                      "Höchste Qualität",
			"select_language":                      "Sprache auswählen",
			"language_settings":                    "Spracheinstellungen",
			"save":                                 "Speichern",
			"cancel":                               "Abbrechen",
			"language_updated_title":               "Sprache aktualisiert",
			"language_updated_message":             "Die Anwendungssprache wurde aktualisiert.",
			"enabled":                              "aktiviert",
			"disabled":                             "deaktiviert",
			"dialog_close":                         "Schließen",
			"about_title":                          "Über GoTube Video Downloader",
			"about_text":                           "GoTube Video Downloader ist ein einfacher, plattformübergreifender YouTube-Video-Downloader, der mit Go und Fyne erstellt wurde.\n\nVersion: 1.0.0\nGo Version: %s\n\nDiese Anwendung verwendet yt-dlp zum Herunterladen von Videos und ffmpeg zur Verarbeitung.\n\nEntwickelt als Open-Source-Projekt.",
			"open_in_download_tab":                 "Im Download-Tab öffnen",
			"status_url_loaded":                    "URL geladen: %s",
			"all_downloads_completed":              "Alle Downloads abgeschlossen",
			"downloads_finished_summary_format":    "Download von %d von %d Videos abgeschlossen",
			"status_browser_cookies_toggled":       "Browser-Cookies %s",
			"status_browser_set":                   "Browser für Cookies gesetzt auf: %s",
			"error_downloader_not_ready":           "Fehler: Downloader nicht bereit",
			"status_multiple_urls":                 "%d URLs geladen, erste wird angezeigt.",
			"360p":                                 "360p",
			"720p":                                 "720p",
			"1080p":                                "1080p",
			"start":                                "Start",
			"end":                                  "Ende",
			"date":                                 "Datum",
			"url":                                  "URL",
			"title":                                "Titel",

			// Install Dependency Keys
			"install_dependency_title":          "Abhängigkeit installieren",
			"install_dependency_confirm_button": "Installieren",
			"install_dependency_message_format": "Das Python-Modul '%s' wird für die Verwendung von Browser-Cookies benötigt, wurde aber nicht gefunden.\n\nJetzt mit 'python3 -m pip install --user %s' installieren?",
			"installing_dependency_title":       "Installiere Abhängigkeit",
			"installing_dependency_message":     "Installiere %s...",
			"install_success_title":             "Installation erfolgreich",
			"install_success_message_format":    "%s erfolgreich installiert.\n\nBitte starten Sie die Anwendung neu und aktivieren Sie 'Browser-Cookies verwenden' erneut, falls nötig.",
			"install_error_title":               "Installation fehlgeschlagen",
			"install_error_message_format":      "Installation von %s fehlgeschlagen.\n\nFehler:\n%v\n\nAusgabe:\n%s",
			"status_checking_dependency":        "Prüfe auf Python-Modul '%s'...",
			"status_dependency_missing_prompt":  "Python-Modul '%s' nicht gefunden. Frage Benutzer zur Installation.",
			"status_dependency_found":           "Python-Modul '%s' gefunden.",

			// Database encryption translations
			"menu_encryption":                     "Verschlüsselung",
			"menu_change_password":                "Passwort ändern",
			"menu_enable_encryption":              "Verschlüsselung aktivieren",
			"menu_disable_encryption":             "Verschlüsselung deaktivieren",
			"encryption_password_title":           "Datenbank-Passwort",
			"encryption_password_prompt":          "Geben Sie ein Passwort ein, um Ihre Datenbank zu verschlüsseln. Dies schützt Ihre Cookies und andere sensible Daten.",
			"encryption_password_existing_prompt": "Diese Datenbank ist verschlüsselt. Bitte geben Sie das Passwort ein, um sie zu entsperren.",
			"encryption_password_new":             "Neues Passwort",
			"encryption_password_confirm":         "Passwort bestätigen",
			"encryption_password_current":         "Aktuelles Passwort",
			"encryption_password_mismatch":        "Passwörter stimmen nicht überein",
			"encryption_password_empty":           "Passwort darf nicht leer sein",
			"encryption_password_incorrect":       "Falsches Passwort.",
			"encryption_already_enabled":          "Verschlüsselung ist bereits aktiviert.",
			"encryption_already_disabled":         "Verschlüsselung ist bereits deaktiviert.",
			"encryption_disable_warning":          "Warnung: Das Deaktivieren der Verschlüsselung speichert sensible Daten wie Cookies unverschlüsselt.",
			"encryption_enabled":                  "Datenbankverschlüsselung aktiviert",
			"encryption_disabled":                 "Datenbankverschlüsselung deaktiviert",
			"encryption_password_changed":         "Datenbank-Passwort erfolgreich geändert",
			"encryption_error":                    "Verschlüsselungsfehler: %v",
			"encryption_skip":                     "Ohne Verschlüsselung fortfahren",
			"encryption_skipped":                  "Fortfahren ohne Verschlüsselung. Ihre Daten werden nicht geschützt.",
			"encryption_enable":                   "Verschlüsselung aktivieren",
			"encryption_disable":                  "Verschlüsselung deaktivieren",
			"encryption_password":                 "Passwort",
			"unlock":                              "Entsperren",
			"ok":                                  "OK",
			"path":                                "Pfad",
			"status_checking_url":                 "Prüfe URL...",
			"error_check_playlist":                "Fehler beim Prüfen, ob URL eine Playlist ist: %v",
			"status_already_downloading":          "Ein anderer Download läuft bereits.",
			"dialog_path_title":                   "Download-Pfad auswählen",
			"status_path_set":                     "Download-Pfad gesetzt auf: %s",
			"starting_download":                   "Starte Download",
		},
	}
)

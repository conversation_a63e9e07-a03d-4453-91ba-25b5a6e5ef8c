// File: internal/i18n/i18n.go

package i18n

import (
	"fmt"
	"sync"
)

var (
	currentLanguage = "en" // Default language
	mutex           sync.RWMutex
)

// SetLanguage sets the current language
func SetLanguage(lang string) {
	mutex.Lock()
	defer mutex.Unlock()

	// Check if language is supported
	found := false
	for _, l := range SupportedLanguages {
		if l.Code == lang {
			currentLanguage = lang
			found = true
			break
		}
	}

	// Fallback to English if language is not supported
	if !found {
		fmt.Printf("Warning: Language '%s' not explicitly supported. Falling back to 'en'.\n", lang)
		currentLanguage = "en"
	}
}

// Get returns the translation for the given key
func Get(key string) string {
	mutex.RLock()
	lang := currentLanguage
	mutex.RUnlock()

	// Get translation for current language
	if langMap, ok := translations[lang]; ok {
		if val, ok := langMap[key]; ok {
			if str, ok := val.(string); ok {
				return str
			}
			fmt.Printf("Warning: Translation key '%s' for language '%s' is not a string (type: %T).\n", key, lang, val)
		}
	}

	// Fallback to English if not found in current language
	if lang != "en" {
		if langMap, ok := translations["en"]; ok {
			if val, ok := langMap[key]; ok {
				if str, ok := val.(string); ok {
					return str
				}
				fmt.Printf("Warning: Translation key '%s' for fallback language 'en' is not a string (type: %T).\n", key, val)
			}
		}
	}

	// Return key if no translation found anywhere
	fmt.Printf("Warning: Translation key '%s' not found for language '%s' or fallback 'en'.\n", key, lang)
	return key // Return the key itself as a last resort
}

// GetAvailableLanguages returns a map of language codes to names
func GetAvailableLanguages() map[string]string {
	result := make(map[string]string)
	for _, lang := range SupportedLanguages {
		result[lang.Code] = lang.Name
	}
	return result
}

// GetCurrentLanguage returns the current language code
func GetCurrentLanguage() string {
	mutex.RLock()
	defer mutex.RUnlock()
	return currentLanguage
}

// Format returns a formatted translation using fmt.Sprintf
func Format(key string, args ...interface{}) string {
	return fmt.Sprintf(Get(key), args...)
}

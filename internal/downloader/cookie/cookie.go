// File: internal/downloader/cookie/cookie.go

package cookie

import (
	"bufio"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
)

type CookieManager struct {
	YtDlpPath      string
	UserCookiePath string       // Stores the path as provided by the user via the UI (for display only)
	db             *database.DB // Database is required
	mutex          sync.RWMutex
}

// NewCookieManager creates a new cookie manager with a database connection
func NewCookieManager(ytDlpPath string, db *database.DB) (*CookieManager, error) {
	if db == nil {
		return nil, fmt.Errorf("database connection is required")
	}

	return &CookieManager{
		YtDlpPath: ytDlpPath,
		db:        db,
	}, nil
}

// verifyYouTubeCookies checks if a cookie file seems valid for YouTube access (Netscape format).
// This is still useful for validating user-provided files.
func (c *CookieManager) verifyYouTubeCookies(cookieFilePath string) bool {
	fileInfo, err := os.Stat(cookieFilePath)
	if err != nil {
		fmt.Printf("Verification failed: Cookie file %s does not exist or error stating: %v\n", cookieFilePath, err)
		return false
	}
	if fileInfo.Size() == 0 {
		fmt.Printf("Verification failed: Cookie file %s is empty.\n", cookieFilePath)
		return false
	}

	file, err := os.Open(cookieFilePath)
	if err != nil {
		fmt.Printf("Verification failed: Could not open %s: %v\n", cookieFilePath, err)
		return false
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	linesRead := 0
	foundYoutubeNetscape := false
	for scanner.Scan() && linesRead < 200 {
		line := scanner.Text()
		linesRead++

		if strings.HasPrefix(line, "#") || strings.TrimSpace(line) == "" {
			continue
		}

		parts := strings.Split(line, "\t")
		if len(parts) >= 1 {
			domain := parts[0]
			if strings.HasSuffix(domain, "youtube.com") {
				if len(parts) >= 6 {
					cookieName := strings.TrimSpace(parts[5])
					if cookieName == "LOGIN_INFO" || cookieName == "SID" || cookieName == "HSID" || cookieName == "SSID" || cookieName == "APISID" || cookieName == "SAPISID" || cookieName == "PREF" || strings.HasPrefix(cookieName, "__Secure-") {
						fmt.Printf("Verification: Found potential YouTube login cookie '%s' in %s\n", cookieName, cookieFilePath)
						foundYoutubeNetscape = true
						break
					}
				}
			}
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("Verification warning: Error reading cookie file %s: %v\n", cookieFilePath, err)
	}

	if !foundYoutubeNetscape {
		fmt.Printf("Verification failed: No key YouTube cookies found in first %d lines of %s.\n", linesRead, cookieFilePath)
		return false
	}

	fmt.Printf("Verification passed: Found relevant YouTube cookies in %s.\n", cookieFilePath)
	return true
}

// SetCustomCookies imports cookies from a file and stores them in the database.
func (c *CookieManager) SetCustomCookies(path string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	expandedPath := ExpandHomeDir(path)
	if _, err := os.Stat(expandedPath); err != nil {
		return fmt.Errorf("cookie file not found at '%s' (expanded from '%s'): %w", expandedPath, path, err)
	}

	fmt.Printf("Debug: Verifying cookies in file: %s\n", expandedPath)
	if !c.verifyYouTubeCookies(expandedPath) {
		fmt.Printf("Warning: Custom cookie file '%s' exists but may not contain valid YouTube cookies or is not in Netscape format.\n", expandedPath)
		// You might want to return an error here if strict validation is desired:
		// return fmt.Errorf("custom cookie file '%s' failed validation (Netscape format/YouTube cookies not found)", expandedPath)
	}

	// Store the original path for display purposes
	c.UserCookiePath = path
	fmt.Printf("Custom cookie path set to: %s (resolved: %s)\n", path, expandedPath)

	// Debug: Count cookies in file
	file, err := os.Open(expandedPath)
	if err == nil {
		defer file.Close()
		scanner := bufio.NewScanner(file)
		cookieCount := 0
		youtubeCookies := 0
		for scanner.Scan() {
			line := scanner.Text()
			if !strings.HasPrefix(line, "#") && strings.TrimSpace(line) != "" {
				cookieCount++
				if strings.Contains(line, "youtube.com") {
					youtubeCookies++
					// Print cookie name (but not value)
					parts := strings.Split(line, "\t")
					if len(parts) >= 6 {
						fmt.Printf("Debug: Found YouTube cookie: %s (domain=%s)\n", parts[5], parts[0])
					}
				}
			}
		}
		fmt.Printf("Debug: Found %d cookies in file, %d YouTube cookies\n", cookieCount, youtubeCookies)
	}

	// Import cookies from file to database
	fmt.Printf("Debug: Importing cookies from file to database\n")
	err = c.db.ImportCookiesFromNetscape(expandedPath)
	if err != nil {
		return fmt.Errorf("failed to import cookies from '%s': %w", expandedPath, err)
	}

	return nil
}

// SetCookiesFromText imports cookies from a text string in Netscape format
func (c *CookieManager) SetCookiesFromText(cookieText string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Create a temporary file
	tempFile, err := os.CreateTemp("", "gotube-cookies-*.txt")
	if err != nil {
		return fmt.Errorf("failed to create temporary cookie file: %w", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// Write the cookie text to the file
	_, err = tempFile.WriteString(cookieText)
	if err != nil {
		return fmt.Errorf("failed to write cookies to temporary file: %w", err)
	}
	tempFile.Close()

	// Import cookies from the temporary file to database
	err = c.db.ImportCookiesFromNetscape(tempFile.Name())
	if err != nil {
		return fmt.Errorf("failed to import cookies from text: %w", err)
	}

	return nil
}

// EffectiveCookiePath exports cookies from the database to a temporary file and returns the path.
func (c *CookieManager) EffectiveCookiePath() string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// Create a temporary file
	tempFile, err := os.CreateTemp("", "gotube-cookies-*.txt")
	if err != nil {
		fmt.Printf("Warning: Failed to create temporary cookie file: %v\n", err)
		return ""
	}
	tempFile.Close()

	// Export cookies to the temporary file
	err = c.db.ExportCookiesToNetscape(tempFile.Name())
	if err != nil {
		fmt.Printf("Warning: Failed to export cookies to temporary file: %v\n", err)
		os.Remove(tempFile.Name())
		return ""
	}

	// Debug: Check if the cookie file has content
	fileInfo, err := os.Stat(tempFile.Name())
	if err != nil {
		fmt.Printf("Warning: Failed to stat cookie file: %v\n", err)
	} else {
		fmt.Printf("Debug: Cookie file size: %d bytes\n", fileInfo.Size())
		if fileInfo.Size() > 0 {
			// Count the number of cookies
			file, err := os.Open(tempFile.Name())
			if err == nil {
				defer file.Close()
				scanner := bufio.NewScanner(file)
				cookieCount := 0
				youtubeCookies := 0
				for scanner.Scan() {
					line := scanner.Text()
					if !strings.HasPrefix(line, "#") && strings.TrimSpace(line) != "" {
						cookieCount++
						if strings.Contains(line, "youtube.com") {
							youtubeCookies++
						}
					}
				}
				fmt.Printf("Debug: Found %d cookies, %d YouTube cookies\n", cookieCount, youtubeCookies)
			}
		}
	}

	// Return the temporary file path
	return tempFile.Name()
}

// AddCookie adds a cookie to the database
func (c *CookieManager) AddCookie(domain, path, name, value string, secure, httpOnly bool, expires time.Time) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Create cookie entry
	cookie := database.CookieEntry{
		Domain:   domain,
		Path:     path,
		Name:     name,
		Value:    value,
		Secure:   secure,
		HttpOnly: httpOnly,
	}

	// Set expiration if not zero
	if !expires.IsZero() {
		cookie.Expires = sql.NullTime{Time: expires, Valid: true}
	}

	// Add to database
	_, err := c.db.AddCookie(cookie)
	return err
}

// ClearCookies clears all cookies from the database
func (c *CookieManager) ClearCookies() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	return c.db.ClearCookies()
}

// ExpandHomeDir expands ~ or $HOME to the user's home directory.
func ExpandHomeDir(path string) string {
	if path == "" {
		return ""
	}
	if path[0] == '~' {
		home, err := os.UserHomeDir()
		if err != nil {
			fmt.Printf("Warning: Could not get user home directory to expand '%s': %v\n", path, err)
			return path
		}
		if len(path) == 1 {
			return home
		} else if path[1] == filepath.Separator || path[1] == '/' {
			return filepath.Join(home, path[2:])
		}
		fmt.Printf("Warning: Path format '%s' is not supported for home directory expansion\n", path)
		return path
	}
	return os.ExpandEnv(path)
}

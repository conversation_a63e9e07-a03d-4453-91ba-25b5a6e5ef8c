package downloader

import (
	"testing"
)

// TestCookieArguments tests the CookieArguments type
func TestCookieArguments(t *testing.T) {
	// Test empty arguments
	args := CookieArguments{}
	if len(args) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected empty CookieArguments to have length 0, got %d", len(args))
	}

	// Test browser cookie arguments
	args = CookieArguments{"--cookies-from-browser", "chrome"}
	if len(args) != 2 {
		t.<PERSON>("Expected CookieArguments to have length 2, got %d", len(args))
	}
	if args[0] != "--cookies-from-browser" {
		t.<PERSON><PERSON>("Expected first arg to be '--cookies-from-browser', got %s", args[0])
	}
	if args[1] != "chrome" {
		t.<PERSON><PERSON><PERSON>("Expected second arg to be 'chrome', got %s", args[1])
	}

	// Test file cookie arguments
	args = CookieArguments{"--cookies", "/path/to/cookies.txt"}
	if len(args) != 2 {
		t.<PERSON><PERSON>("Expected CookieArguments to have length 2, got %d", len(args))
	}
	if args[0] != "--cookies" {
		t.Errorf("Expected first arg to be '--cookies', got %s", args[0])
	}
	if args[1] != "/path/to/cookies.txt" {
		t.Errorf("Expected second arg to be '/path/to/cookies.txt', got %s", args[1])
	}
}

// TestCookieSource tests the cookie source constants
func TestCookieSource(t *testing.T) {
	if CookieSourceNone != 0 {
		t.Errorf("Expected CookieSourceNone to be 0, got %d", CookieSourceNone)
	}
	if CookieSourceBrowser != 1 {
		t.Errorf("Expected CookieSourceBrowser to be 1, got %d", CookieSourceBrowser)
	}
	if CookieSourceFile != 2 {
		t.Errorf("Expected CookieSourceFile to be 2, got %d", CookieSourceFile)
	}
}

// TestCookieSourceInfo tests the cookie source information structure
func TestCookieSourceInfo(t *testing.T) {
	info := CookieSourceInfo{
		Source:      CookieSourceBrowser,
		BrowserName: "chrome",
		Description: "Browser cookies from chrome",
		Available:   true,
	}

	if info.Source != CookieSourceBrowser {
		t.Errorf("Expected CookieSourceBrowser, got %v", info.Source)
	}

	if info.BrowserName != "chrome" {
		t.Errorf("Expected chrome, got %v", info.BrowserName)
	}
}

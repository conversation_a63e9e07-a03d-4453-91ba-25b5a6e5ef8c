// File: internal/downloader/retry_handler.go

package downloader

import (
	"context"
	"fmt"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/fileutil"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// handleRetryOrCancel waits for retry delay or cancellation signal
func handleRetryOrCancel(ctx context.Context, statusCb func(string), attempt int) (shouldRetry bool, cancelErr error) {
	if attempt >= maxRetries {
		return false, nil // Max retries reached
	}
	waitDuration := retryDelay
	statusCb(fmt.Sprintf(i18n.Get("status_retrying_in"), waitDuration))
	select {
	case <-time.After(waitDuration):
		return true, nil // Wait completed, should retry
	case <-ctx.Done():
		statusCb("Download cancelled during retry wait.")
		return false, context.Canceled // Cancelled during wait
	}
}

// handleDownloadFailure handles failure after all retries
func handleDownloadFailure(lastErr error, absPath, baseFilename string, statusCb func(string), progressCb func(float64)) error {
	if lastErr == nil {
		// Should not happen if loop completed without success, but set a generic error
		lastErr = fmt.Errorf("download process completed without success after %d attempts", maxRetries)
	}

	// Log failure and clean up
	statusCb(i18n.Get("status_download_failed_final"))
	if absPath != "" && baseFilename != "" {
		statusCb(i18n.Get("status_cleaning_up"))
		fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
	}

	// Reset progress and return error
	progressCb(0.0)
	return fmt.Errorf("download failed after %d retries: %w", maxRetries, lastErr)
}

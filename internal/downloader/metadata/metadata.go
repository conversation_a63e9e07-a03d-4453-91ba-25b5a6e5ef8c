// File: internal/downloader/metadata/metadata.go

package metadata

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"regexp"
	"runtime" // Import runtime
	"strings"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/cookie"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/resources" // Import resources
)

// Format represents a single format option available for a video
type Format struct {
	FormatID   string  `json:"format_id"`   // Unique identifier for the format
	Extension  string  `json:"ext"`         // File extension (mp4, webm, m4a, etc.)
	Resolution string  `json:"resolution"`  // Resolution like "720p", "1080p", or "audio only"
	FormatNote string  `json:"format_note"` // Descriptive text about the format
	Filesize   int64   `json:"filesize"`    // Size in bytes (optional)
	Vcodec     string  `json:"vcodec"`      // Video codec (or "none" for audio-only)
	Acodec     string  `json:"acodec"`      // Audio codec (or "none" for video-only)
	Height     int     `json:"height"`      // Video height in pixels
	Width      int     `json:"width"`       // Video width in pixels
	FPS        float64 `json:"fps"`         // Frames per second
	VBR        float64 `json:"vbr"`         // Video bitrate
	ABR        float64 `json:"abr"`         // Audio bitrate
}

// VideoMetadata struct holds information about a single video
type VideoMetadata struct {
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Duration    int      `json:"duration"` // Duration in seconds
	Thumbnail   string   `json:"thumbnail"`
	Author      string   `json:"channel"`         // Use 'channel' as yt-dlp often provides this
	UploadDate  string   `json:"upload_date"`     // Format YYYYMMDD
	ID          string   `json:"id"`              // Video ID
	URL         string   `json:"webpage_url"`     // Full video URL
	Filesize    int64    `json:"filesize_approx"` // Approximate filesize if available
	ViewCount   int64    `json:"view_count"`
	LikeCount   int64    `json:"like_count"`
	Formats     []Format `json:"formats"` // Available formats for this video
}

// PlaylistMetadata struct holds information about a playlist and its videos
type PlaylistMetadata struct {
	Title    string          `json:"title"`
	ID       string          `json:"id"`
	Uploader string          `json:"uploader"`
	Entries  []VideoMetadata `json:"entries"` // Videos in the playlist
}

// MetadataProvider defines the interface for fetching metadata
type MetadataProvider interface {
	Get(url string) (*VideoMetadata, error)
	IsPlaylist(url string) (bool, error)
	GetPlaylistMetadata(url string) (*PlaylistMetadata, error)
	GetFormats(url string) ([]Format, error) // Get available formats for a video
	SetUseBrowserCookies(enabled bool)       // Keep these setters
	SetBrowserName(browser string)
}

// YtDlpMetadataProvider implements MetadataProvider using yt-dlp
type YtDlpMetadataProvider struct {
	ytDlpPath             string
	ffmpegPath            string
	tempPythonModulesPath string // <<< Added path for extracted python modules (Windows only)
	cookieManager         *cookie.CookieManager
	useBrowserCookies     bool
	browserName           string
}

// NewYtDlpMetadataProvider creates a new YtDlpMetadataProvider instance
func NewYtDlpMetadataProvider(ytDlpPath, ffmpegPath, tempPythonModulesPath string, cookieMgr *cookie.CookieManager) *YtDlpMetadataProvider { // <<< Accept tempPythonModulesPath
	// Quick version check (setting PYTHONPATH here too for the check)
	cmd := exec.Command(ytDlpPath, "--version")
	if tempPythonModulesPath != "" && runtime.GOOS == "windows" {
		cmd.Env = append(os.Environ(), fmt.Sprintf("PYTHONPATH=%s", tempPythonModulesPath))
	}
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Warning: yt-dlp version check failed: %v\n", err)
	} else {
		fmt.Printf("Using yt-dlp version: %s\n", strings.TrimSpace(string(output)))
	}

	return &YtDlpMetadataProvider{
		ytDlpPath:             ytDlpPath,
		ffmpegPath:            ffmpegPath,
		tempPythonModulesPath: tempPythonModulesPath, // <<< Store the path
		cookieManager:         cookieMgr,
		useBrowserCookies:     false,    // Default to false
		browserName:           "chrome", // Default browser
	}
}

// addCommonArgs adds arguments common to most yt-dlp metadata calls
func (p *YtDlpMetadataProvider) addCommonArgs(args []string) []string {
	baseArgs := []string{
		"--no-warnings",
		"--ignore-errors", // Try to get partial data if possible
		"--force-ipv4",
		"--no-check-certificate",
		// "--geo-bypass", // Uncomment if needed
	}
	args = append(baseArgs, args...)

	// Add ffmpeg location if known
	if p.ffmpegPath != "" {
		args = append(args, "--ffmpeg-location", p.ffmpegPath)
	}

	// Add cookies conditionally
	canUseBrowserCookies := true
	if p.useBrowserCookies {
		if runtime.GOOS != "windows" { // Linux/Other check
			if !resources.IsSecretStorageInstalled() {
				canUseBrowserCookies = false
				fmt.Println("MetadataProvider Warning: 'secretstorage' module not found. Browser cookies disabled for this call.")
			}
		} else { // Windows check
			if p.tempPythonModulesPath == "" { // Check if module extraction worked
				canUseBrowserCookies = false
				fmt.Println("MetadataProvider Warning: Embedded Python modules unavailable. Browser cookies disabled for this call.")
			}
			// We assume secretstorage IS installed within the extracted modules if tempPythonModulesPath is set
		}

		if canUseBrowserCookies {
			// Add browser cookie args
			// Handle Vivaldi Flatpak specifically
			if p.browserName == "vivaldi" {
				vivaldiCookiePath := os.ExpandEnv("${HOME}/.var/app/com.vivaldi.Vivaldi/config/vivaldi/Default/Cookies")
				if _, err := os.Stat(vivaldiCookiePath); err == nil {
					vivaldiArg := fmt.Sprintf("vivaldi:%s", vivaldiCookiePath)
					args = append(args, "--cookies-from-browser", vivaldiArg)
					fmt.Println("Metadata fetch using Vivaldi Flatpak cookies (specific path)")
				} else {
					args = append(args, "--cookies-from-browser", "vivaldi")
					fmt.Println("Metadata fetch using Vivaldi cookies (default detection - Flatpak path not found)")
				}
			} else {
				args = append(args, "--cookies-from-browser", p.browserName)
				fmt.Printf("Metadata fetch using browser cookies from: %s (yt-dlp will attempt detection)\n", p.browserName)
			}
		}
	} else {
		// Use cookies from file if available
		effectiveCookiePath := p.cookieManager.EffectiveCookiePath()
		if effectiveCookiePath != "" {
			args = append(args, "--cookies", effectiveCookiePath)
			fmt.Printf("Metadata fetch using cookies file: %s\n", effectiveCookiePath)
			// Ensure temp cookie file from DB is removed *after* the command runs
			// Since this function only builds args, the removal needs to happen in the calling function
			// The `defer os.Remove(effectiveCookiePath)` should be in the calling function's scope.
		} else {
			fmt.Println("Metadata fetch without cookies.")
		}
	}

	return args
}

// Get fetches video metadata using yt-dlp
func (p *YtDlpMetadataProvider) Get(url string) (*VideoMetadata, error) {
	if url == "" {
		return nil, fmt.Errorf("URL cannot be empty")
	}

	fmt.Printf("Fetching metadata for %s\n", url)
	args := []string{
		"--dump-json",     // Get detailed JSON output
		"--no-playlist",   // Ensure we only get info for the single video URL
		"--skip-download", // Don't actually download
	}

	// Add common args (cookies, ffmpeg-loc, etc.)
	args = p.addCommonArgs(args)

	// Defer removal of temporary cookie file IF one was created by addCommonArgs
	// This is tricky because addCommonArgs doesn't return the path.
	// Let's handle the defer in the Downloader.Download method instead where the path is known.

	// Add the URL *last* after all options
	args = append(args, url)

	// Execute command
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cmd := exec.CommandContext(ctx, p.ytDlpPath, args...)

	// **WINDOWS ONLY**: Set PYTHONPATH if modules were extracted and browser cookies are attempted
	if runtime.GOOS == "windows" && p.tempPythonModulesPath != "" && p.useBrowserCookies {
		// Check if we actually decided to use browser cookies based on the logic in addCommonArgs
		canUseBrowserCookies := true // Re-evaluate condition briefly
		if p.tempPythonModulesPath == "" {
			canUseBrowserCookies = false
		}

		if canUseBrowserCookies {
			cmd.Env = os.Environ()
			pythonPathEnv := fmt.Sprintf("PYTHONPATH=%s", p.tempPythonModulesPath)
			foundPythonPath := false
			for i, envVar := range cmd.Env {
				if strings.HasPrefix(envVar, "PYTHONPATH=") {
					cmd.Env[i] = fmt.Sprintf("%s%c%s", envVar, os.PathListSeparator, p.tempPythonModulesPath)
					foundPythonPath = true
					break
				}
			}
			if !foundPythonPath {
				cmd.Env = append(cmd.Env, pythonPathEnv)
			}
			fmt.Println("Setting PYTHONPATH for metadata yt-dlp command:", cmd.Env[len(cmd.Env)-1])
		}
	}

	output, err := cmd.CombinedOutput()

	if ctx.Err() == context.DeadlineExceeded {
		return nil, fmt.Errorf("metadata fetch timed out after 30 seconds for %s", url)
	}
	if err != nil {
		// Handle command execution error
		errMsg := strings.ToLower(string(output))
		needsCookies := strings.Contains(errMsg, "age restricted") || strings.Contains(errMsg, "sign in") || strings.Contains(errMsg, "private video") || strings.Contains(errMsg, "premieres in") || strings.Contains(errMsg, "cookies database")
		lastErr := fmt.Errorf("yt-dlp metadata fetch failed: %w\nOutput: %s", err, string(output))
		fmt.Println(lastErr) // Log the full error regardless
		if needsCookies {
			fmt.Println("Metadata error suggests cookies might be needed or are incorrect/expired. Try using 'Load Custom Cookies' or enabling/re-selecting 'Use Browser Cookies'.")
			// Return a more specific error message if cookie-related
			if strings.Contains(errMsg, "cookies database") {
				return nil, fmt.Errorf("yt-dlp failed to access browser cookies: %w", err)
			}
		}
		return nil, lastErr // Return the general error
	}

	// --- Parse JSON ---
	var meta VideoMetadata
	// Handle cases where yt-dlp might return multiple JSON objects (e.g., info and playlist item)
	dec := json.NewDecoder(strings.NewReader(string(output)))
	for {
		if err := dec.Decode(&meta); err == io.EOF {
			break // End of input
		} else if err != nil {
			// Attempt to handle playlist JSON structure if single video decode failed
			var playlistCheck map[string]interface{}
			if json.Unmarshal(output, &playlistCheck) == nil {
				if t, ok := playlistCheck["_type"].(string); ok && t == "playlist" {
					if entries, ok := playlistCheck["entries"].([]interface{}); ok && len(entries) > 0 {
						firstEntryBytes, _ := json.Marshal(entries[0])
						if errFirst := json.Unmarshal(firstEntryBytes, &meta); errFirst == nil {
							fmt.Println("Warning: Received playlist data despite --no-playlist, using metadata from the first entry.")
							goto PostProcess // Successfully parsed first entry
						} else {
							return nil, fmt.Errorf("expected single video metadata but received playlist data and couldn't parse first entry (URL: %s): %w", url, errFirst)
						}
					} else {
						return nil, fmt.Errorf("expected single video metadata but received empty playlist data (URL: %s)", url)
					}
				}
			}
			// If not playlist or other known structure, return original JSON error
			return nil, fmt.Errorf("failed to parse yt-dlp JSON output: %w\nOutput sample: %s", err, string(output[:min(500, len(output))]))
		}
		// If Decode succeeded, assume it's the video metadata we want (or the first in a stream)
		break
	}

PostProcess: // Label for goto
	// --- Post-processing ---
	// Fill URL from ID if missing
	if meta.URL == "" && meta.ID != "" {
		meta.URL = "https://www.youtube.com/watch?v=" + meta.ID
	} else if meta.URL == "" && meta.ID == "" {
		fmt.Printf("Warning: Fetched metadata missing both webpage_url and id. Title: '%s'\n", meta.Title)
		// Cannot construct URL
	}

	// Use uploader if channel is missing (common case)
	if meta.Author == "" {
		var tempMeta map[string]interface{}
		if json.Unmarshal(output, &tempMeta) == nil {
			if uploader, ok := tempMeta["uploader"].(string); ok {
				meta.Author = uploader
			}
		}
	}

	fmt.Printf("Successfully fetched metadata for: %s\n", meta.Title)
	return &meta, nil
}

// IsPlaylist checks if a URL likely points to a YouTube playlist
func (p *YtDlpMetadataProvider) IsPlaylist(url string) (bool, error) {
	// Quick Regex checks (can catch many cases without calling yt-dlp)
	// Regex for standard youtube.com URLs with list parameter
	re := regexp.MustCompile(`youtube\.com/(?:playlist|watch|course)\?.+list=([a-zA-Z0-9_-]+)`)
	if re.MatchString(url) {
		fmt.Printf("Regex check indicates playlist for: %s\n", url)
		return true, nil
	}
	// Regex for youtu.be short URLs with list parameter
	reShort := regexp.MustCompile(`youtu\.be/.+\?.*list=([a-zA-Z0-9_-]+)`)
	if reShort.MatchString(url) {
		fmt.Printf("Regex check indicates playlist for short URL: %s\n", url)
		return true, nil
	}
	// Regex for youtube.com/playlist/ URL structure
	reDirectPlaylist := regexp.MustCompile(`youtube\.com/playlist/([a-zA-Z0-9_-]+)`)
	if reDirectPlaylist.MatchString(url) {
		fmt.Printf("Regex check indicates direct playlist URL: %s\n", url)
		return true, nil
	}

	fmt.Printf("Regex check inconclusive. Performing yt-dlp --flat-playlist check for: %s\n", url)

	// If regex checks fail, use yt-dlp with --flat-playlist for confirmation
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	args := []string{
		"--flat-playlist",    // Only lists IDs, much faster
		"--dump-single-json", // Get JSON output to check type reliably
	}
	args = p.addCommonArgs(args) // Add common args (cookies, ffmpeg-loc, etc.)
	// Defer removal of temp cookie file if needed in the calling function scope
	args = append(args, url) // Add URL last

	cmd := exec.CommandContext(ctx, p.ytDlpPath, args...)
	// Set PYTHONPATH if needed for this check too
	if runtime.GOOS == "windows" && p.tempPythonModulesPath != "" && p.useBrowserCookies {
		// Only set if actually using browser cookies, similar logic as Get()
		canUseBrowserCookies := true
		if p.tempPythonModulesPath == "" {
			canUseBrowserCookies = false
		}
		if canUseBrowserCookies {
			cmd.Env = os.Environ()
			pythonPathEnv := fmt.Sprintf("PYTHONPATH=%s", p.tempPythonModulesPath)
			// Append/set logic...
			foundPythonPath := false
			for i, envVar := range cmd.Env {
				if strings.HasPrefix(envVar, "PYTHONPATH=") {
					cmd.Env[i] = fmt.Sprintf("%s%c%s", envVar, os.PathListSeparator, p.tempPythonModulesPath)
					foundPythonPath = true
					break
				}
			}
			if !foundPythonPath {
				cmd.Env = append(cmd.Env, pythonPathEnv)
			}
			fmt.Println("Setting PYTHONPATH for playlist check yt-dlp command")
		}
	}

	output, err := cmd.CombinedOutput()

	// Handle timeout/errors
	if ctx.Err() == context.DeadlineExceeded {
		return false, fmt.Errorf("yt-dlp playlist check timed out for %s", url)
	}
	errStr := string(output)
	if err != nil {
		// Check specific yt-dlp output indicating it's NOT a playlist
		// This helps distinguish "not a playlist" from other errors.
		if strings.Contains(errStr, "This URL does not point to a playlist") ||
			strings.Contains(errStr, "confirm that the URL is correct") || // Often indicates single video or invalid URL
			strings.Contains(errStr, "No entries found") || // Empty playlist (treat as non-playlist for download?)
			strings.Contains(errStr, "is not a playlist") {
			fmt.Printf("yt-dlp output indicates URL is likely NOT a playlist or is invalid/empty: %s\n", url)
			return false, nil // Not a playlist, no error returned to caller
		}
		// Otherwise, it's likely a real error (network, permissions, etc.)
		fmt.Printf("yt-dlp playlist check command failed: %v\nOutput: %s\n", err, errStr)
		return false, fmt.Errorf("yt-dlp playlist check command failed for URL '%s': %w", url, err)
	}

	// Parse JSON output to confirm type
	var result map[string]interface{}
	if json.Unmarshal(output, &result) == nil {
		// Check the _type field
		if t, ok := result["_type"].(string); ok && t == "playlist" {
			fmt.Printf("yt-dlp --flat-playlist JSON confirms URL is a playlist: %s\n", url)
			return true, nil
		}
	}

	// If command succeeded but JSON doesn't indicate playlist type (e.g., single video JSON returned)
	fmt.Printf("yt-dlp check succeeded but JSON type is not 'playlist' (or parsing failed), assuming not a playlist: %s\n", url)
	return false, nil
}

// GetPlaylistMetadata fetches metadata for all videos in a playlist
func (p *YtDlpMetadataProvider) GetPlaylistMetadata(url string) (*PlaylistMetadata, error) {
	if url == "" {
		return nil, fmt.Errorf("playlist URL cannot be empty")
	}
	fmt.Printf("Fetching full playlist metadata for: %s\n", url)

	// Use a longer timeout for potentially large playlists
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
	defer cancel()

	args := []string{
		"--dump-single-json", // Get all info in one JSON object
		// "--flat-playlist",    // Avoid this for full metadata
		"--extractor-args", "youtube:flat_list_only=true", // Use extractor arg to prevent downloading playlist items individually
		"--skip-download", // Ensure no download happens
	}
	args = p.addCommonArgs(args) // Add common args (cookies, ffmpeg-loc, etc.)
	// Defer removal of temp cookie file if needed in the calling function scope
	args = append(args, url) // Add URL last

	cmd := exec.CommandContext(ctx, p.ytDlpPath, args...)
	// Set PYTHONPATH if needed for this check too
	if runtime.GOOS == "windows" && p.tempPythonModulesPath != "" && p.useBrowserCookies {
		canUseBrowserCookies := true
		if p.tempPythonModulesPath == "" {
			canUseBrowserCookies = false
		}
		if canUseBrowserCookies {
			cmd.Env = os.Environ()
			pythonPathEnv := fmt.Sprintf("PYTHONPATH=%s", p.tempPythonModulesPath)
			foundPythonPath := false
			for i, envVar := range cmd.Env {
				if strings.HasPrefix(envVar, "PYTHONPATH=") {
					cmd.Env[i] = fmt.Sprintf("%s%c%s", envVar, os.PathListSeparator, p.tempPythonModulesPath)
					foundPythonPath = true
					break
				}
			}
			if !foundPythonPath {
				cmd.Env = append(cmd.Env, pythonPathEnv)
			}
			fmt.Println("Setting PYTHONPATH for playlist metadata yt-dlp command")
		}
	}

	output, err := cmd.CombinedOutput()

	// Handle timeout/errors
	if ctx.Err() == context.DeadlineExceeded {
		return nil, fmt.Errorf("yt-dlp timed out fetching full playlist metadata for %s", url)
	}
	if err != nil {
		errStr := string(output)
		// Check for common non-fatal playlist issues (private/unavailable videos)
		if strings.Contains(errStr, "playlist index ") && (strings.Contains(errStr, "is unavailable") || strings.Contains(errStr, "This video is private")) {
			fmt.Println("Warning: Some videos in the playlist might be unavailable or private...")
			// Continue parsing the JSON, as partial data might still be useful
		} else if strings.Contains(errStr, "confirm that the URL is correct") {
			// This often means the URL resolved to a single video
			return nil, fmt.Errorf("URL does not point to a playlist or is invalid")
		} else {
			// Assume other errors are fatal for metadata fetching
			return nil, fmt.Errorf("failed to fetch playlist metadata from yt-dlp: %w\nOutput: %s", err, errStr)
		}
	}

	// Parse JSON
	var playlist PlaylistMetadata
	if parseErr := json.Unmarshal(output, &playlist); parseErr != nil {
		// Check if it might have returned single video JSON instead
		var singleVideoCheck map[string]interface{}
		if json.Unmarshal(output, &singleVideoCheck) == nil {
			if _, idOK := singleVideoCheck["id"]; idOK {
				if t, typeOK := singleVideoCheck["_type"].(string); !typeOK || t != "playlist" {
					return nil, fmt.Errorf("URL resolved to a single video, not a playlist during metadata fetch")
				}
			}
		}
		// Otherwise, return the original parsing error
		return nil, fmt.Errorf("failed to parse playlist JSON metadata: %w\nOutput sample: %s", parseErr, string(output[:min(1000, len(output))]))
	}

	// Basic Validation
	if playlist.ID == "" && len(playlist.Entries) == 0 {
		// If no ID and no entries, likely not a valid playlist response
		return nil, fmt.Errorf("fetched playlist metadata is invalid or empty (missing playlist ID and entries)")
	}

	// Post-processing Entries
	processedEntries := []VideoMetadata{} // Create a new slice for valid entries
	for _, entry := range playlist.Entries {
		// Fill URL from ID if missing
		if entry.URL == "" && entry.ID != "" {
			entry.URL = "https://www.youtube.com/watch?v=" + entry.ID
		}
		// Use playlist uploader if entry author is missing
		if entry.Author == "" && playlist.Uploader != "" {
			entry.Author = playlist.Uploader
		}
		// Only include entries that have at least an ID or a Title
		if entry.ID != "" || entry.Title != "" {
			processedEntries = append(processedEntries, entry)
		} else {
			fmt.Println("Warning: Skipping playlist entry with missing ID and Title.")
		}
	}
	playlist.Entries = processedEntries // Update entries with processed ones

	fmt.Printf("Fetched metadata for playlist '%s' (%s) with %d valid entries.\n", playlist.Title, playlist.ID, len(playlist.Entries))
	return &playlist, nil
}

// SetUseBrowserCookies enables or disables using browser cookies directly
func (p *YtDlpMetadataProvider) SetUseBrowserCookies(enabled bool) {
	p.useBrowserCookies = enabled
	status := "disabled"
	if enabled {
		status = "enabled"
	}
	fmt.Printf("Metadata provider browser cookies %s (browser: %s)\n", status, p.browserName)
}

// SetBrowserName sets the browser to extract cookies from
func (p *YtDlpMetadataProvider) SetBrowserName(browser string) {
	// Basic validation: use lowercase
	browser = strings.ToLower(browser)
	// Allow any string, yt-dlp handles validation of supported browsers
	if browser == "" {
		browser = "chrome" // Default if empty
	}
	p.browserName = browser
	fmt.Printf("Metadata provider browser for cookies set to: %s\n", p.browserName)
}

// GetFormats fetches available formats for a video using yt-dlp
func (p *YtDlpMetadataProvider) GetFormats(url string) ([]Format, error) {
	if url == "" {
		return nil, fmt.Errorf("URL cannot be empty")
	}

	fmt.Printf("Fetching formats for %s\n", url)
	args := []string{
		"--dump-json",     // Get detailed JSON output including formats
		"--no-playlist",   // Ensure we only get info for the single video URL
		"--skip-download", // Don't actually download
	}

	// Add common args (cookies, ffmpeg-loc, etc.)
	args = p.addCommonArgs(args)

	// Add the URL *last* after all options
	args = append(args, url)

	// Execute command with shorter timeout since we only need formats
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	cmd := exec.CommandContext(ctx, p.ytDlpPath, args...)

	// Set PYTHONPATH if needed for browser cookies
	if runtime.GOOS == "windows" && p.tempPythonModulesPath != "" && p.useBrowserCookies {
		cmd.Env = append(os.Environ(), fmt.Sprintf("PYTHONPATH=%s", p.tempPythonModulesPath))
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		// Handle command execution error
		lastErr := fmt.Errorf("yt-dlp format fetch failed: %w\nOutput: %s", err, string(output))
		fmt.Println(lastErr) // Log the full error
		return nil, lastErr
	}

	// Parse JSON to extract formats
	var result map[string]interface{}
	if err := json.Unmarshal(output, &result); err != nil {
		return nil, fmt.Errorf("failed to parse yt-dlp JSON output: %w", err)
	}

	// Extract formats array
	formatsInterface, ok := result["formats"]
	if !ok {
		return nil, fmt.Errorf("no formats found in video metadata")
	}

	formatsArray, ok := formatsInterface.([]interface{})
	if !ok {
		return nil, fmt.Errorf("formats field is not an array")
	}

	// Parse each format
	var formats []Format
	for _, formatInterface := range formatsArray {
		formatBytes, err := json.Marshal(formatInterface)
		if err != nil {
			continue // Skip invalid formats
		}

		var format Format
		if err := json.Unmarshal(formatBytes, &format); err != nil {
			continue // Skip formats that can't be parsed
		}

		// Only include formats with valid format_id
		if format.FormatID != "" {
			formats = append(formats, format)
		}
	}

	fmt.Printf("Successfully fetched %d formats for video\n", len(formats))
	return formats, nil
}

// min utility function
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// File: internal/downloader/metadata_handler.go

package downloader

import (
	"context"
	"fmt"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/timeutil"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// fetchAndValidateMetadata fetches video metadata and validates trim times
func (d *Downloader) fetchAndValidateMetadata(ctx context.Context, url string, attempt int, statusCb func(string)) (*metadata.VideoMetadata, error) {
	statusCb(i18n.Get("status_fetching_metadata"))

	// Fetch metadata
	metaResult, err := d.MetadataProvider.Get(url)
	if err != nil {
		statusCb(fmt.Sprintf("Metadata error (attempt %d): %v", attempt, err))

		// Check for cancellation
		select {
		case <-ctx.Done():
			return nil, context.Canceled
		default:
		}

		return nil, fmt.Errorf("attempt %d: failed to fetch video metadata: %w", attempt, err)
	}

	// Log successful metadata fetch
	statusCb(fmt.Sprintf(i18n.Get("status_metadata_fetched_for"), metaResult.Title))

	// Validate trim times if needed
	if err := d.validateTrimTimes(metaResult.Duration, statusCb); err != nil {
		return nil, err
	}

	return metaResult, nil
}

// validateTrimTimes validates the trim start and end times against the video duration
func (d *Downloader) validateTrimTimes(duration int, statusCb func(string)) error {
	// If no trim times set, nothing to validate
	d.mutex.Lock()
	ts := d.trimStartTime
	te := d.trimEndTime
	d.mutex.Unlock()

	if ts == "" && te == "" {
		return nil
	}

	// If duration is available, validate the trim times
	if duration > 0 {
		if err := timeutil.ValidateTrimTimes(ts, te, duration); err != nil {
			return fmt.Errorf("invalid trim settings: %w", err) // Fatal error for invalid trim
		}
		statusCb(fmt.Sprintf("Trim times validated: %s to %s",
			ts,
			te))
	} else {
		// Duration unknown, cannot validate trim, proceed with warning
		statusCb("Warning: Could not validate trim times as video duration is unknown.")
	}

	return nil
}

// checkIfPlaylist checks if the URL is a playlist and logs appropriate warnings
func (d *Downloader) checkIfPlaylist(url string, statusCb func(string)) error {
	isPlaylist, err := d.MetadataProvider.IsPlaylist(url)
	if err != nil {
		return fmt.Errorf("error checking if URL is a playlist: %w", err)
	}
	if isPlaylist {
		statusCb("Warning: Playlist URL detected in single download mode. Only the first downloadable video might be processed.")
	}
	return nil
}

// File: internal/downloader/cancellation.go

package downloader

import (
	"context"
	"fmt"
)

// setupCancellation initializes the context and cancellation function for a download
func (d *Downloader) setupCancellation() (context.Context, context.CancelFunc) {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	d.isCancelled = false // Reset cancel flag for new download
	ctx, cancel := context.WithCancel(context.Background())
	d.cancelFunc = cancel
	return ctx, cancel
}

// cleanupCancellation ensures the cancellation function is properly cleaned up
func (d *Downloader) cleanupCancellation(cancel context.CancelFunc) {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	d.cancelFunc = nil // Clear cancel func when download finishes/errors
	cancel()           // Ensure context is cancelled on exit
}

// CancelDownload signals the current download to terminate.
// It relies on the command execution context being cancelled.
func (d *Downloader) CancelDownload() {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	d.isCancelled = true
	if d.cancelFunc != nil {
		fmt.Println("Sending cancellation signal to download process...")
		d.cancelFunc() // This triggers the context cancellation.
	} else {
		fmt.Println("Cancellation requested, but no download is active.")
	}
}

// IsCancelled returns whether the download has been cancelled
func (d *Downloader) IsCancelled() bool {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	return d.isCancelled
}

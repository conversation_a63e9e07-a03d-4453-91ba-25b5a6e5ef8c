// File: internal/downloader/command_handler.go

package downloader

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
)

// buildAndExecuteCommand builds and executes the download command
func (d *Downloader) buildAndExecuteCommand(
	ctx context.Context,
	url, format, quality, absPath string,
	videoMeta *metadata.VideoMetadata,
	settings DownloadSettings,
	cookieInfo CookieInfo,
	progressCb func(float64),
	statusCb func(string),
) (*exec.Cmd, error) {
	// Build command arguments
	cmdArgs := d.commandExecutor.BuildDownloadCommandArgs(
		url, format, quality, absPath, settings.filenameTemplate, videoMeta,
		settings.sponsorBlockEnabled, settings.trimStartTime, settings.trimEndTime, settings.ffmpegPath,
	)

	// Add cookie arguments
	cmdArgs = d.addCookieArguments(cmdArgs, cookieInfo, statusCb)

	// Add URL as final argument
	cmdArgs = append(cmdArgs, "--", url)

	// Create command with context
	cmd := exec.CommandContext(ctx, d.ytDlpPath, cmdArgs...)

	// Set environment variables if needed
	d.setupCommandEnvironment(cmd, settings, cookieInfo)

	// Store command for potential cancellation
	d.mutex.Lock()
	d.currentCmd = cmd
	d.mutex.Unlock()

	// Execute the command
	execErr := d.commandExecutor.ExecuteAndMonitorCommand(ctx, cmd, progressCb, statusCb)

	// Clear command reference
	d.mutex.Lock()
	d.currentCmd = nil
	d.mutex.Unlock()

	return cmd, execErr
}

// addCookieArguments adds cookie-related arguments to the command
func (d *Downloader) addCookieArguments(cmdArgs []string, cookieInfo CookieInfo, statusCb func(string)) []string {
	// Add cookie arguments based on cookie selection
	if cookieInfo.useBrowser && cookieInfo.canUseBrowser {
		// Handle Vivaldi Flatpak specifically
		if cookieInfo.browserName == "vivaldi" {
			vivaldiCookiePath := os.ExpandEnv("${HOME}/.var/app/com.vivaldi.Vivaldi/config/vivaldi/Default/Cookies")
			if _, err := os.Stat(vivaldiCookiePath); err == nil {
				// Pass the specific cookie file path
				vivaldiArg := fmt.Sprintf("vivaldi:%s", vivaldiCookiePath)
				cmdArgs = append(cmdArgs, "--cookies-from-browser", vivaldiArg)
				statusCb("Using cookies from Vivaldi Flatpak (specific path)")
			} else {
				// Fallback to default detection if Flatpak path not found
				cmdArgs = append(cmdArgs, "--cookies-from-browser", "vivaldi")
				statusCb("Using cookies from Vivaldi (default detection - Flatpak path not found)")
			}
		} else {
			// For other browsers, let yt-dlp detect
			cmdArgs = append(cmdArgs, "--cookies-from-browser", cookieInfo.browserName)
			statusCb(fmt.Sprintf("Using cookies directly from browser: %s (yt-dlp will attempt detection)", cookieInfo.browserName))
		}
	} else if cookieInfo.tempFilePath != "" {
		cmdArgs = append(cmdArgs, "--cookies", cookieInfo.tempFilePath)
	}

	return cmdArgs
}

// setupCommandEnvironment sets up the environment variables for the command
func (d *Downloader) setupCommandEnvironment(cmd *exec.Cmd, settings DownloadSettings, cookieInfo CookieInfo) {
	// Set PYTHONPATH if needed (Windows only)
	if runtime.GOOS == "windows" && settings.tempPythonPath != "" && cookieInfo.useBrowser && cookieInfo.canUseBrowser {
		// Inherit existing environment and add/overwrite PYTHONPATH
		cmd.Env = os.Environ()
		pythonPathEnv := fmt.Sprintf("PYTHONPATH=%s", settings.tempPythonPath)

		// Check if PYTHONPATH already exists, append if so, otherwise set
		foundPythonPath := false
		for i, envVar := range cmd.Env {
			if strings.HasPrefix(envVar, "PYTHONPATH=") {
				// Append to existing PYTHONPATH, separated by the OS list separator
				cmd.Env[i] = fmt.Sprintf("%s%c%s", envVar, os.PathListSeparator, settings.tempPythonPath)
				foundPythonPath = true
				break
			}
		}
		if !foundPythonPath {
			cmd.Env = append(cmd.Env, pythonPathEnv) // Add new PYTHONPATH
		}
		fmt.Println("Setting PYTHONPATH for yt-dlp command:", cmd.Env[len(cmd.Env)-1])
	}
}

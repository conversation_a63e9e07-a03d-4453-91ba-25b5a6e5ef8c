// File: internal/downloader/command_handler.go

package downloader

import (
	"context"
	"fmt"
	"os"
	"os/exec"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
)

// buildAndExecuteCommand builds and executes the download command
func (d *Downloader) buildAndExecuteCommand(
	ctx context.Context,
	url, format, quality, absPath string,
	videoMeta *metadata.VideoMetadata,
	settings DownloadSettings,
	cookieArgs CookieArguments,
	progressCb func(float64),
	statusCb func(string),
) (*exec.Cmd, error) {
	// Build command arguments
	cmdArgs := d.commandExecutor.BuildDownloadCommandArgs(
		url, format, quality, absPath, settings.filenameTemplate, videoMeta,
		settings.sponsorBlockEnabled, settings.trimStartTime, settings.trimEndTime, settings.ffmpegPath,
	)

	// Add cookie arguments
	cmdArgs = d.addCookieArguments(cmdArgs, cookieArgs, statusCb)

	// Add URL as final argument
	cmdArgs = append(cmdArgs, "--", url)

	// Create command with context
	cmd := exec.CommandContext(ctx, d.ytDlpPath, cmdArgs...)

	// Store command for potential cancellation
	d.mutex.Lock()
	d.currentCmd = cmd
	d.mutex.Unlock()

	// Execute the command
	execErr := d.commandExecutor.ExecuteAndMonitorCommand(ctx, cmd, progressCb, statusCb)

	// Clear command reference
	d.mutex.Lock()
	d.currentCmd = nil
	d.mutex.Unlock()

	return cmd, execErr
}

// addCookieArguments adds cookie-related arguments to the command
func (d *Downloader) addCookieArguments(cmdArgs []string, cookieArgs CookieArguments, statusCb func(string)) []string {
	// Handle special case for Vivaldi Flatpak path detection
	if len(cookieArgs) == 2 && cookieArgs[0] == "--cookies-from-browser" && cookieArgs[1] == "vivaldi" {
		vivaldiCookiePath := os.ExpandEnv("${HOME}/.var/app/com.vivaldi.Vivaldi/config/vivaldi/Default/Cookies")
		if _, err := os.Stat(vivaldiCookiePath); err == nil {
			// Pass the specific cookie file path
			vivaldiArg := fmt.Sprintf("vivaldi:%s", vivaldiCookiePath)
			cmdArgs = append(cmdArgs, "--cookies-from-browser", vivaldiArg)
			statusCb("Using cookies from Vivaldi Flatpak (specific path)")
			return cmdArgs
		} else {
			// Fallback to default detection if Flatpak path not found
			statusCb("Using cookies from Vivaldi (default detection - Flatpak path not found)")
		}
	}

	// For all other cases, simply append the cookie arguments to the command
	return append(cmdArgs, cookieArgs...)
}

// setupCommandEnvironment sets up the environment variables for the command
func (d *Downloader) setupCommandEnvironment(cmd *exec.Cmd, settings DownloadSettings) {
	// Note: We no longer need complex platform-specific environment setup
	// since yt-dlp handles browser cookie dependencies internally.
	// This function is kept for future environment variable needs.

	// Inherit the current environment
	cmd.Env = os.Environ()
}

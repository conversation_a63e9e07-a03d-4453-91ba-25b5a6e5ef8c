// File: internal/downloader/path_handler.go

package downloader

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/fileutil"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// prepareDownloadPath prepares the download path and filename
func (d *Downloader) prepareDownloadPath(path string, videoMeta *metadata.VideoMetadata, format string, statusCb func(string)) (absPath, baseFilename, ext string, err error) {
	statusCb(i18n.Get("status_preparing_path"))

	// Get filename template
	d.mutex.Lock()
	tmpl := d.filenameTemplate
	d.mutex.Unlock()

	// Prepare download path
	absPath, baseFilename, ext, err = fileutil.PrepareDownloadPath(path, videoMeta, format, tmpl)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to prepare download environment: %w", err)
	}

	// Log the target file path
	targetPath := filepath.Join(absPath, baseFilename+"."+ext)
	statusCb(fmt.Sprintf(i18n.Get("status_download_target"), targetPath))

	// Ensure the directory exists
	if err := os.MkdirAll(absPath, 0755); err != nil {
		return "", "", "", fmt.Errorf("failed to create download directory: %w", err)
	}

	return absPath, baseFilename, ext, nil
}

package playlist

import (
	"fmt"
	"strings"
	"testing"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
)

// MockDownloader is a mock implementation of the Downloader interface
type MockDownloader struct {
	downloadCalls int
	downloadError error
}

// Download is a mock implementation of the Download method
func (m *MockDownloader) Download(url, format, quality, path string, progressCb func(float64), statusCb func(string)) (string, error) {
	m.downloadCalls++
	progressCb(1.0)
	statusCb("Mock download complete")
	return "/path/to/downloaded/file.mp4", m.downloadError
}

// SetDownloadError sets the error to be returned by Download
func (m *MockDownloader) SetDownloadError(err error) {
	m.downloadError = err
}

// GetDownloadCalls returns the number of times Download was called
func (m *MockDownloader) GetDownloadCalls() int {
	return m.downloadCalls
}

// TestPlaylistDownloaderCreation tests creating a playlist downloader
func TestPlaylistDownloaderCreation(t *testing.T) {
	// Create a mock downloader
	mockDownloader := &MockDownloader{}

	// Create a playlist downloader
	playlistDownloader := NewPlaylistDownloader(mockDownloader)

	// Check if playlist downloader was created correctly
	if playlistDownloader == nil {
		t.Fatal("Playlist downloader should not be nil")
	}
}

// TestDownloadPlaylistVideo tests downloading a single video from a playlist
func TestDownloadPlaylistVideo(t *testing.T) {
	// Create a mock downloader
	mockDownloader := &MockDownloader{}

	// Create a playlist downloader
	playlistDownloader := NewPlaylistDownloader(mockDownloader)

	// Test downloading a video
	progressCalled := false
	statusMessages := []string{}

	err := playlistDownloader.DownloadPlaylistVideo(
		"https://www.youtube.com/watch?v=test-id",
		"mp4",
		"720p",
		"/download/path",
		func(progress float64) {
			progressCalled = true
		},
		func(status string) {
			statusMessages = append(statusMessages, status)
		},
	)

	// Check if download was successful
	if err != nil {
		t.Fatalf("Download failed: %v", err)
	}

	// Check if progress callback was called
	if !progressCalled {
		t.Error("Progress callback was not called")
	}

	// Check if status callback was called
	if len(statusMessages) == 0 {
		t.Error("Status callback was not called")
	}

	// Check if Download was called
	if mockDownloader.GetDownloadCalls() != 1 {
		t.Errorf("Download should be called once, got %d", mockDownloader.GetDownloadCalls())
	}
}

// TestDownloadPlaylistVideoWithError tests downloading a video with an error
func TestDownloadPlaylistVideoWithError(t *testing.T) {
	// Create a mock downloader
	mockDownloader := &MockDownloader{}
	mockDownloader.SetDownloadError(fmt.Errorf("mock download error"))

	// Create a playlist downloader
	playlistDownloader := NewPlaylistDownloader(mockDownloader)

	// Test downloading a video
	err := playlistDownloader.DownloadPlaylistVideo(
		"https://www.youtube.com/watch?v=test-id",
		"mp4",
		"720p",
		"/download/path",
		func(progress float64) {},
		func(status string) {},
	)

	// Check if download failed with the expected error
	if err == nil {
		t.Fatal("Download should fail with an error")
	}
	if !strings.Contains(err.Error(), "mock download error") {
		t.Errorf("Error message incorrect: got %v, want to contain 'mock download error'", err)
	}
}

// TestDownloadPlaylistVideos tests downloading multiple videos from a playlist
func TestDownloadPlaylistVideos(t *testing.T) {
	// Create a mock downloader
	mockDownloader := &MockDownloader{}

	// Create a playlist downloader
	playlistDownloader := NewPlaylistDownloader(mockDownloader)

	// Create test videos
	videos := []metadata.VideoMetadata{
		{
			ID:    "test-id-1",
			Title: "Test Video 1",
			URL:   "https://www.youtube.com/watch?v=test-id-1",
		},
		{
			ID:    "test-id-2",
			Title: "Test Video 2",
			URL:   "https://www.youtube.com/watch?v=test-id-2",
		},
		{
			ID:    "test-id-3",
			Title: "Test Video 3",
			URL:   "https://www.youtube.com/watch?v=test-id-3",
		},
	}

	// Test downloading videos
	progressCalls := 0
	statusMessages := []string{}

	err := playlistDownloader.DownloadPlaylistVideos(
		videos,
		"mp4",
		"720p",
		"/download/path",
		func(index int, progress float64) {
			progressCalls++
		},
		func(index int, status string) {
			statusMessages = append(statusMessages, status)
		},
	)

	// Check if download was successful
	if err != nil {
		t.Fatalf("Download failed: %v", err)
	}

	// Check if progress callback was called
	if progressCalls == 0 {
		t.Error("Progress callback was not called")
	}

	// Check if status callback was called
	if len(statusMessages) == 0 {
		t.Error("Status callback was not called")
	}

	// Check if Download was called for each video
	if mockDownloader.GetDownloadCalls() != len(videos) {
		t.Errorf("Download should be called %d times, got %d", len(videos), mockDownloader.GetDownloadCalls())
	}
}

// TestDownloadPlaylistVideosWithError tests downloading multiple videos with an error
func TestDownloadPlaylistVideosWithError(t *testing.T) {
	// Create a mock downloader
	mockDownloader := &MockDownloader{}
	mockDownloader.SetDownloadError(fmt.Errorf("mock download error"))

	// Create a playlist downloader
	playlistDownloader := NewPlaylistDownloader(mockDownloader)

	// Create test videos
	videos := []metadata.VideoMetadata{
		{
			ID:    "test-id-1",
			Title: "Test Video 1",
			URL:   "https://www.youtube.com/watch?v=test-id-1",
		},
		{
			ID:    "test-id-2",
			Title: "Test Video 2",
			URL:   "https://www.youtube.com/watch?v=test-id-2",
		},
	}

	// Test downloading videos
	err := playlistDownloader.DownloadPlaylistVideos(
		videos,
		"mp4",
		"720p",
		"/download/path",
		func(index int, progress float64) {},
		func(index int, status string) {},
	)

	// Check if download failed with the expected error
	if err == nil {
		t.Fatal("Download should fail with an error")
	}
	if !strings.Contains(err.Error(), "mock download error") {
		t.Errorf("Error message incorrect: got %v, want to contain 'mock download error'", err)
	}

	// Check if Download was called for each video
	if mockDownloader.GetDownloadCalls() != len(videos) {
		t.Errorf("Download should be called %d times, got %d", len(videos), mockDownloader.GetDownloadCalls())
	}
}

// TestDownloadPlaylistVideosWithEmptyList tests downloading an empty list of videos
func TestDownloadPlaylistVideosWithEmptyList(t *testing.T) {
	// Create a mock downloader
	mockDownloader := &MockDownloader{}

	// Create a playlist downloader
	playlistDownloader := NewPlaylistDownloader(mockDownloader)

	// Test downloading an empty list of videos
	err := playlistDownloader.DownloadPlaylistVideos(
		[]metadata.VideoMetadata{},
		"mp4",
		"720p",
		"/download/path",
		func(index int, progress float64) {},
		func(index int, status string) {},
	)

	// Check if download failed with the expected error
	if err == nil {
		t.Fatal("Download should fail with an error")
	}
	if !strings.Contains(err.Error(), "no videos selected") {
		t.Errorf("Error message incorrect: got %v, want to contain 'no videos selected'", err)
	}

	// Check if Download was not called
	if mockDownloader.GetDownloadCalls() != 0 {
		t.Errorf("Download should not be called, got %d calls", mockDownloader.GetDownloadCalls())
	}
}

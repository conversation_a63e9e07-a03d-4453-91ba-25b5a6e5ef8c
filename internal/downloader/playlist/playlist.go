// File: internal/downloader/playlist/playlist.go

package playlist

import (
	"fmt"
	"strings"
	"sync"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
)

// Downloader defines the interface for downloading videos
type Downloader interface {
	Download(url, format, quality, path string, progressCb func(float64), statusCb func(string)) (string, error)
}

// PlaylistDownloader handles downloading multiple videos from a playlist
type PlaylistDownloader struct {
	downloader Downloader
}

// NewPlaylistDownloader creates a new PlaylistDownloader instance
func NewPlaylistDownloader(downloader Downloader) *PlaylistDownloader {
	return &PlaylistDownloader{
		downloader: downloader,
	}
}

// DownloadPlaylistVideos downloads multiple videos from a playlist concurrently
func (p *PlaylistDownloader) DownloadPlaylistVideos(videos []metadata.VideoMetadata, format, quality, path string, progressCb func(int, float64), statusCb func(int, string)) error {
	if len(videos) == 0 {
		return fmt.Errorf("no videos selected for download from the playlist")
	}

	totalVideos := len(videos)
	statusCb(-1, fmt.Sprintf("Starting download of %d videos from playlist...", totalVideos)) // Overall status message

	errChan := make(chan error, totalVideos)
	var wg sync.WaitGroup

	for i, video := range videos {
		wg.Add(1)
		go func(index int, v metadata.VideoMetadata) {
			defer wg.Done()

			videoProgressCb := func(progress float64) {
				progressCb(index, progress)
			}
			videoStatusCb := func(status string) {
				// Prefix status with video index/title for clarity
				statusCb(index, fmt.Sprintf("[%d/%d] %s: %s", index+1, totalVideos, v.Title, status))
			}

			videoStatusCb("Starting download...")

			// Check if URL is available
			urlToDownload := v.URL
			if urlToDownload == "" {
				// Fallback to ID if URL is missing
				if v.ID != "" {
					urlToDownload = "https://www.youtube.com/watch?v=" + v.ID
				} else {
					err := fmt.Errorf("cannot download video %d ('%s'): URL and ID missing from metadata", index+1, v.Title)
					errChan <- err
					videoStatusCb(fmt.Sprintf("Error: %v", err))
					return // Stop processing this video
				}
			}

			// Download the video
			err := p.DownloadPlaylistVideo(urlToDownload, format, quality, path, videoProgressCb, videoStatusCb)
			if err != nil {
				errMsg := fmt.Errorf("video %d ('%s'): %w", index+1, v.Title, err)
				errChan <- errMsg
				videoStatusCb(fmt.Sprintf("Error: %v", err))
			} else {
				videoStatusCb("Download completed successfully.")
				videoProgressCb(1.0)
			}
		}(i, video)
	}

	wg.Wait()
	close(errChan)

	var downloadErrors []error
	for err := range errChan {
		downloadErrors = append(downloadErrors, err)
	}

	if len(downloadErrors) > 0 {
		var errMsg strings.Builder
		errMsg.WriteString(fmt.Sprintf("%d of %d playlist downloads failed:\n", len(downloadErrors), totalVideos))
		for _, err := range downloadErrors {
			errMsg.WriteString(fmt.Sprintf("- %v\n", err))
		}
		statusCb(-1, fmt.Sprintf("Playlist download finished with %d errors.", len(downloadErrors)))
		return fmt.Errorf(errMsg.String())
	}

	statusCb(-1, fmt.Sprintf("All %d playlist videos downloaded successfully.", totalVideos))
	return nil
}

// DownloadPlaylistVideo downloads a specific video from a playlist
func (p *PlaylistDownloader) DownloadPlaylistVideo(videoURL, format, quality, path string, progressCb func(float64), statusCb func(string)) error {
	// Use the main Download method
	_, err := p.downloader.Download(videoURL, format, quality, path, progressCb, statusCb)
	if err != nil {
		// Provide context in the error message
		return fmt.Errorf("failed to download playlist video (%s): %w", videoURL, err)
	}
	return nil
}

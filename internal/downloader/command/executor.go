// File: internal/downloader/command/executor.go

package command

import (
	"bufio"
	"context"
	"fmt"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/progress"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/timeutil"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

type CommandExecutor struct {
	ytDlpPath  string
	ffmpegPath string // Store ffmpeg path
}

func NewCommandExecutor(ytDlpPath, ffmpegPath string) *CommandExecutor {
	return &CommandExecutor{
		ytDlpPath:  ytDlpPath,
		ffmpegPath: ffmpegPath, // Store ffmpeg path
	}
}

// BuildDownloadCommandArgs constructs the argument slice for yt-dlp execution.
func (c *CommandExecutor) BuildDownloadCommandArgs(
	url, format, quality, path, filenameTmpl string,
	meta *metadata.VideoMetadata,
	sponsorBlock bool,
	trimStart, trimEnd string,
	ffmpegLocation string, // Accept ffmpeg location parameter
) []string {

	// Determine output path/template string
	outputTemplate := ""
	if filenameTmpl != "" {
		// Ensure the template is relative to the download path 'path'
		// yt-dlp interprets -o relative to CWD unless it's absolute.
		// Here, we expect 'path' to be the absolute directory, and filenameTmpl to be the pattern.
		outputTemplate = filepath.Join(path, filenameTmpl)
		fmt.Printf("Using custom filename template (joined): %s\n", outputTemplate)
	} else {
		// Default template uses only ID and ext relative to the path
		// This is safer than using title which can have problematic chars even after sanitization attempt.
		outputTemplate = filepath.Join(path, "%(id)s.%(ext)s") // Use ID as default base filename
		fmt.Printf("Using default filename template (id): %s\n", outputTemplate)
	}

	// Base arguments common to all downloads
	args := []string{
		"--no-warnings",
		"--output", outputTemplate,
		"--newline",            // Ensures progress updates are on new lines
		"--no-playlist",        // Standard downloads are single videos
		"--restrict-filenames", // Ensure safe filenames (yt-dlp internal sanitization)
		"--no-mtime",           // Don't modify file time to video timestamp
		"--retries", "10",      // Retry parameters
		"--fragment-retries", "10",
		"--geo-bypass",                                                            // Attempt to bypass geo-restrictions
		"--force-ipv4",                                                            // Prefer IPv4
		"--compat-options", "filename-sanitization,no-youtube-unavailable-videos", // Compatibility options
		// "--throttled-rate", "100K", // Uncomment to throttle download speed
	}

	// --- Add FFmpeg Location if provided ---
	effectiveFFmpegPath := ffmpegLocation
	if effectiveFFmpegPath == "" {
		effectiveFFmpegPath = c.ffmpegPath
	}

	if effectiveFFmpegPath != "" {
		args = append(args, "--ffmpeg-location", effectiveFFmpegPath)
		fmt.Printf("Using ffmpeg location: %s\n", effectiveFFmpegPath)
	} else {
		fmt.Println("Warning: ffmpeg location not provided. yt-dlp will rely on system PATH.")
	}
	// --- End FFmpeg Location ---

	// --- Thumbnail/Metadata Embedding ---
	args = append(args, "--convert-thumbnails", "jpg") // Ensure compatible format (jpg often better than webp for embedding)
	args = append(args, "--embed-thumbnail")
	args = append(args, "--embed-metadata")
	// args = append(args, "--embed-chapters") // Optional: embed chapters if available

	// --- Format Specific Arguments ---
	isAudioOnly := format == "MP3" || format == "M4A"
	isVideo := format == "MP4"                 // Add other video formats (webm?) if needed
	isCustomFormat := !isAudioOnly && !isVideo // If not a favorite format, treat as custom format ID

	if isCustomFormat {
		// For custom format IDs, use the format ID directly
		args = append(args, "--format", format)
	} else if isAudioOnly {
		audioFormatLower := strings.ToLower(format)
		args = append(args,
			"--extract-audio",
			"--audio-format", audioFormatLower,
			"--format", "bestaudio/best", // Select best audio stream first, fallback to best overall
		)
		// Determine audio quality setting for yt-dlp
		// yt-dlp quality: 0=best VBR, 5=~128kbps VBR, 9=worst VBR
		audioQuality := "5" // Default to ~128kbps VBR for Opus/AAC
		switch quality {
		case "quality_highest", "Highest":
			audioQuality = "0" // Best quality VBR
		case "quality_128", "128kbps":
			audioQuality = "5" // ~128kbps VBR
			// Add lower qualities if needed
		}
		args = append(args, "--audio-quality", audioQuality)

	} else if isVideo {
		// Determine video quality number (e.g., "1080" from "1080p")
		qualityNumStr := "1080" // Default if parsing fails or quality unknown
		if q, err := strconv.Atoi(strings.TrimSuffix(quality, "p")); err == nil {
			qualityNumStr = strconv.Itoa(q)
		}
		// Construct format specifier string for yt-dlp
		// Prioritize vp9/avc1 video + m4a audio in mp4 container, up to specified height
		// Fallback to best video + best audio up to specified height within mp4 container
		formatSpec := fmt.Sprintf(
			"bestvideo[height<=%s][ext=mp4][vcodec~='^avc1|^vp9']+bestaudio[ext=m4a]/bestvideo[height<=%s][ext=mp4]+bestaudio/best[height<=%s][ext=mp4]/best[height<=%s]",
			qualityNumStr, qualityNumStr, qualityNumStr, qualityNumStr,
		)

		args = append(args,
			"--format", formatSpec,
			"--merge-output-format", "mp4", // Ensure final container is mp4 if merging occurs
			"--format-sort", "res,vcodec:vp9.2,acodec:m4a", // Prefer specific codecs if available at target res
			// "--format-sort-force", // Maybe not needed, can sometimes cause issues
		)
	} else {
		// Fallback for unknown formats (treat as video download request)
		fmt.Printf("Warning: Unknown format requested for download: %s. Attempting best mp4.\n", format)
		qualityNumStr := "1080" // Default quality for fallback
		if q, err := strconv.Atoi(strings.TrimSuffix(quality, "p")); err == nil {
			qualityNumStr = strconv.Itoa(q)
		}
		args = append(args,
			"--format", fmt.Sprintf("bestvideo[height<=%s][ext=mp4]+bestaudio[ext=m4a]/best[height<=%s][ext=mp4]/best[height<=%s]", qualityNumStr, qualityNumStr, qualityNumStr), // Best mp4 attempt
			"--merge-output-format", "mp4",
		)
	}

	// --- SponsorBlock ---
	if sponsorBlock {
		args = append(args,
			"--sponsorblock-remove", "all", // Remove all known segment types
			// "--sponsorblock-api", "https://sponsor.ajay.app/api/skipSegments", // Optional: specify API endpoint
		)
	}

	// --- Trimming (Post-processing with FFmpeg) ---
	isTrimming := trimStart != "" || trimEnd != ""
	if isTrimming {
		if effectiveFFmpegPath == "" {
			fmt.Println("Warning: Trimming requested but ffmpeg path is unknown. Trimming will be skipped.")
		} else {
			ffmpegArgs := []string{}
			var startSec, endSec int = -1, -1
			var parseErr error

			// Parse start time
			if trimStart != "" {
				startSec, parseErr = timeutil.ParseTimeToSeconds(trimStart)
				if parseErr != nil {
					fmt.Printf("Warning: Invalid trim start time '%s' ignored: %v\n", trimStart, parseErr)
					startSec = -1
				}
			}

			// Parse end time
			if trimEnd != "" {
				endSec, parseErr = timeutil.ParseTimeToSeconds(trimEnd)
				if parseErr != nil {
					fmt.Printf("Warning: Invalid trim end time '%s' ignored: %v\n", trimEnd, parseErr)
					endSec = -1
				}
			}

			// Validate time range logic
			if startSec != -1 && endSec != -1 && startSec >= endSec {
				fmt.Printf("Warning: Trim start time %ds is not before end time %ds. Trimming ignored.\n", startSec, endSec)
				startSec, endSec = -1, -1 // Reset if range is invalid
			}

			// Build ffmpeg arguments for postprocessor
			if startSec != -1 {
				ffmpegArgs = append(ffmpegArgs, "-ss", strconv.Itoa(startSec)) // Input seeking (faster)
			}
			if endSec != -1 {
				if startSec != -1 {
					// If start time is also set, calculate duration
					duration := endSec - startSec
					if duration > 0 {
						ffmpegArgs = append(ffmpegArgs, "-t", strconv.Itoa(duration)) // Duration
					} else {
						fmt.Printf("Warning: Calculated trim duration is not positive (%d). Ignoring end time.\n", duration)
						// Remove end time if duration is invalid
						endSec = -1
					}
				} else {
					// If only end time is set, use -to
					ffmpegArgs = append(ffmpegArgs, "-to", strconv.Itoa(endSec)) // End time
				}
			}

			// Append if any valid trim args were generated
			if len(ffmpegArgs) > 0 {
				// --- Start Conditional Codec Copy ---
				// Only use '-c copy' if NOT extracting audio OR if not trimming (though this block only runs if trimming)
				// The main conflict arises when extracting audio AND trimming.
				finalFFmpegArgs := []string{}
				if !isAudioOnly {
					// For video downloads, copying codecs is generally safe and fast
					finalFFmpegArgs = append(finalFFmpegArgs, "-c", "copy")
					fmt.Println("Trimming video: Using codec copy.")
				} else {
					// For audio extraction with trimming, DO NOT copy codecs. Let ffmpeg re-encode.
					fmt.Println("Trimming audio extract: Re-encoding enabled (no -c copy).")
				}
				finalFFmpegArgs = append(finalFFmpegArgs, ffmpegArgs...)
				// --- End Conditional Codec Copy ---

				ffmpegArgString := strings.Join(finalFFmpegArgs, " ")
				// Format for yt-dlp postprocessor arg: "ToolName:Args String"
				args = append(args, "--postprocessor-args", fmt.Sprintf("ffmpeg:%s", ffmpegArgString))
				fmt.Printf("Applying trim via FFmpeg args: %s\n", ffmpegArgString)
				// args = append(args, "--verbose") // Uncomment for debugging ffmpeg postprocessing
			} else if isTrimming {
				// Only print if trimming was requested but resulted in no args (due to errors/logic)
				fmt.Println("Skipping trimming due to invalid time range or parsing errors.")
			}
		} // end ffmpeg path check
	} // end isTrimming check

	return args
}

// ExecuteAndMonitorCommand starts and monitors the yt-dlp command.
// It relies on the passed context for cancellation. The provided cmd should be
// created via exec.CommandContext to handle cancellation gracefully.
func (c *CommandExecutor) ExecuteAndMonitorCommand(
	ctx context.Context,
	cmd *exec.Cmd, // Receives the fully constructed command, expected to be context-aware
	progressCb func(float64),
	statusCb func(string),
) error {
	// Log the command being executed
	logCmdArgs := []string{}
	for i, arg := range cmd.Args {
		if i == 0 {
			continue
		} // Skip command path
		// Redact sensitive arguments
		if i > 1 && (cmd.Args[i-1] == "--cookies" || cmd.Args[i-1] == "--ffmpeg-location") {
			logCmdArgs = append(logCmdArgs, "<path_redacted>")
		} else if cmd.Args[i-1] == "--cookies-from-browser" && strings.Contains(arg, ":") {
			logCmdArgs = append(logCmdArgs, strings.SplitN(arg, ":", 2)[0]+":<path_redacted>") // Redact path part
		} else {
			logCmdArgs = append(logCmdArgs, strings.ReplaceAll(arg, "%", "%%")) // Escape percent signs for fmt
		}
	}
	statusCb(fmt.Sprintf("Executing: %s %s", filepath.Base(cmd.Path), strings.Join(logCmdArgs, " ")))

	var stderrCapture strings.Builder
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %w", err)
	}
	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	var wg sync.WaitGroup
	wg.Add(2) // For stdout and stderr goroutines

	processErrChan := make(chan error, 1) // Channel for the first critical error from stderr
	pipeErrChan := make(chan error, 2)    // Channel for pipe reading errors

	// Process stdout
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stdoutPipe)
		for scanner.Scan() {
			line := scanner.Text()
			if strings.Contains(line, "[download]") && strings.Contains(line, "%") {
				if prog := progress.ExtractProgress(line); prog >= 0 {
					progressCb(prog)
				}
			} else if strings.HasPrefix(line, "[Merger]") {
				statusCb(i18n.Get("status_merging_formats"))
			} else if strings.HasPrefix(line, "[ExtractAudio]") {
				statusCb(i18n.Get("status_extracting_audio"))
			} else if strings.HasPrefix(line, "[ffmpeg]") {
				if strings.Contains(line, "Embedding thumbnail") {
					statusCb(i18n.Get("status_embedding_thumbnail"))
				} else {
					statusCb(i18n.Get("status_processing_ffmpeg"))
				}
			} else if strings.HasPrefix(line, "Deleting original file") {
				statusCb(i18n.Get("status_deleting_originals"))
			} else if strings.Contains(line, "has already been downloaded") {
				statusCb(i18n.Get("status_file_already_downloaded"))
			}
		}
		if err := scanner.Err(); err != nil {
			pipeErrChan <- fmt.Errorf("error reading stdout: %w", err)
		}
	}()

	// Process stderr
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stderrPipe)
		var reportedErr error
		for scanner.Scan() {
			line := scanner.Text()
			stderrCapture.WriteString(line + "\n")

			upperLine := strings.ToUpper(line)
			if strings.HasPrefix(upperLine, "WARNING:") {
				statusCb(line)
			} else if strings.HasPrefix(upperLine, "ERROR:") {
				statusCb(line)
				if reportedErr == nil &&
					!strings.Contains(line, "fragment") &&
					!strings.Contains(line, "unable to obtain file audio codec") {
					reportedErr = fmt.Errorf("yt-dlp reported: %s", line)
				}
			}
		}
		processErrChan <- reportedErr // Send first critical error (or nil)
		if err := scanner.Err(); err != nil {
			pipeErrChan <- fmt.Errorf("error reading stderr: %w", err)
		}
	}()

	// Start the command
	if err := cmd.Start(); err != nil {
		// Wait for goroutines to finish before returning to avoid leaks
		wg.Wait()
		close(processErrChan)
		close(pipeErrChan)
		return fmt.Errorf("failed to start download command: %w\n%s", err, stderrCapture.String())
	}

	// Wait for the command to finish. This will unblock on completion, error, or context cancellation.
	cmdWaitErr := cmd.Wait()

	// Wait for pipe processing to complete to ensure all output is captured.
	wg.Wait()
	close(processErrChan)
	close(pipeErrChan)

	// --- Error Analysis ---
	finalStderr := strings.TrimSpace(stderrCapture.String())
	stderrReportedErr := <-processErrChan

	// Check if the context was canceled first. This is the most specific case.
	if ctx.Err() == context.Canceled {
		statusCb("Download was cancelled by the user.")
		return context.Canceled
	}

	// If yt-dlp reported a specific "ERROR:" line, prioritize that.
	if stderrReportedErr != nil {
		if cmdWaitErr != nil && !strings.Contains(stderrReportedErr.Error(), cmdWaitErr.Error()) {
			return fmt.Errorf("%w (Command exit: %v)", stderrReportedErr, cmdWaitErr)
		}
		return stderrReportedErr
	}

	// If the command exited with a non-zero status.
	if cmdWaitErr != nil {
		errMsg := fmt.Sprintf("download command failed: %v", cmdWaitErr)
		if finalStderr != "" {
			// Append a snippet of stderr for context in the error message.
			stderrSnippet := finalStderr
			maxSnippetLen := 200
			if len(stderrSnippet) > maxSnippetLen {
				stderrSnippet = "..." + stderrSnippet[len(stderrSnippet)-maxSnippetLen:]
			}
			errMsg += fmt.Sprintf("\nstderr snippet: %s", stderrSnippet)
		}
		return fmt.Errorf(errMsg)
	}

	// Check for errors during pipe reading, even if the command succeeded.
	var pipeErrs []string
	for err := range pipeErrChan {
		if err != nil {
			pipeErrs = append(pipeErrs, err.Error())
		}
	}
	if len(pipeErrs) > 0 {
		return fmt.Errorf("command exited successfully, but pipe reading failed: %s", strings.Join(pipeErrs, "; "))
	}

	// Log the full stderr output for debugging purposes if it's not empty.
	if finalStderr != "" {
		fmt.Printf("yt-dlp stderr output:\n---\n%s\n---\n", finalStderr)
		statusCb("yt-dlp produced stderr output (see console/logs).")
	}

	statusCb("Download command execution completed successfully.")
	return nil // Success
}

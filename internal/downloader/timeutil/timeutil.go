package timeutil

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var timeRegex = regexp.MustCompile(`^(\d{1,2}):([0-5]\d)$`)

func ParseTimeToSeconds(timeStr string) (int, error) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		return 0, fmt.<PERSON><PERSON>rf("invalid time format")
	}

	minutes, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, fmt.<PERSON>rrorf("invalid minutes")
	}

	seconds, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, fmt.<PERSON><PERSON>rf("invalid seconds")
	}

	if seconds >= 60 {
		return 0, fmt.<PERSON><PERSON><PERSON>("seconds must be less than 60")
	}

	return minutes*60 + seconds, nil
}

func ValidateTimeFormat(timeStr string) bool {
	return timeRegex.MatchString(timeStr)
}

func ConvertToSeconds(timeStr string) int {
	parts := strings.Split(timeStr, ":")
	minutes, _ := strconv.Atoi(parts[0])
	seconds, _ := strconv.Atoi(parts[1])
	return minutes*60 + seconds
}

// ParseTimeString parses a time string into a duration
func ParseTimeString(timeStr string) (time.Duration, error) {
	if timeStr == "" {
		return 0, fmt.Errorf("empty time string")
	}

	// Check for hours:minutes:seconds format with optional milliseconds
	hoursRegex := regexp.MustCompile(`^(\d{1,2}):(\d{1,2}):(\d{1,2})(?:\.(\d+))?$`)
	if matches := hoursRegex.FindStringSubmatch(timeStr); len(matches) >= 4 {
		hours, _ := strconv.Atoi(matches[1])
		minutes, _ := strconv.Atoi(matches[2])
		seconds, _ := strconv.Atoi(matches[3])
		duration := time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute + time.Duration(seconds)*time.Second

		// Add milliseconds if present
		if len(matches) > 4 && matches[4] != "" {
			millisStr := matches[4]
			if len(millisStr) > 3 {
				millisStr = millisStr[:3] // Truncate to milliseconds
			}
			for len(millisStr) < 3 {
				millisStr += "0" // Pad with zeros
			}
			millis, _ := strconv.Atoi(millisStr)
			duration += time.Duration(millis) * time.Millisecond
		}

		return duration, nil
	}

	// Check for minutes:seconds format
	minutesRegex := regexp.MustCompile(`^(\d{1,2}):(\d{1,2})$`)
	if matches := minutesRegex.FindStringSubmatch(timeStr); len(matches) == 3 {
		minutes, _ := strconv.Atoi(matches[1])
		seconds, _ := strconv.Atoi(matches[2])
		return time.Duration(minutes)*time.Minute + time.Duration(seconds)*time.Second, nil
	}

	// Check for seconds only
	secondsRegex := regexp.MustCompile(`^(\d+)$`)
	if matches := secondsRegex.FindStringSubmatch(timeStr); len(matches) == 2 {
		seconds, _ := strconv.Atoi(matches[1])
		return time.Duration(seconds) * time.Second, nil
	}

	return 0, fmt.Errorf("invalid time format: %s", timeStr)
}

// FormatDuration formats a duration as a string
func FormatDuration(d time.Duration) string {
	totalSeconds := int(d.Seconds())
	hours := totalSeconds / 3600
	minutes := (totalSeconds % 3600) / 60
	seconds := totalSeconds % 60

	if hours > 0 {
		return fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds)
	}
	return fmt.Sprintf("%02d:%02d", minutes, seconds)
}

// FormatSeconds formats seconds as a time string
func FormatSeconds(seconds int) string {
	return FormatDuration(time.Duration(seconds) * time.Second)
}

// IsValidTimeString checks if a time string is valid
func IsValidTimeString(timeStr string) bool {
	_, err := ParseTimeString(timeStr)
	return err == nil
}

// ValidateTrimTimes checks trim times against video duration
func ValidateTrimTimes(startStr, endStr string, duration int) error {
	if duration <= 0 {
		return fmt.Errorf("cannot validate trim times against unknown or zero duration")
	}
	if startStr == "" && endStr == "" {
		return nil // No trim requested
	}

	var startSec, endSec int = 0, duration // Default end is full duration
	var err error

	if startStr != "" {
		startSec, err = ParseTimeToSeconds(startStr)
		if err != nil {
			return fmt.Errorf("internal error parsing start time '%s': %w", startStr, err) // Should be pre-validated
		}
		if startSec >= duration {
			return fmt.Errorf("start time '%s' (%d sec) is not before video duration (%d sec)", startStr, startSec, duration)
		}
	}

	if endStr != "" {
		endSec, err = ParseTimeToSeconds(endStr)
		if err != nil {
			return fmt.Errorf("internal error parsing end time '%s': %w", endStr, err) // Should be pre-validated
		}
		if endSec > duration {
			fmt.Printf("Warning: End time '%s' (%d sec) exceeds video duration (%d sec), clamping to duration.\n", endStr, endSec, duration)
			endSec = duration // Clamp end time to duration if it exceeds
		}
	}

	if endSec <= startSec {
		return fmt.Errorf("effective end time '%s' (%d sec) must be after effective start time '%s' (%d sec)", endStr, endSec, startStr, startSec)
	}

	return nil
}

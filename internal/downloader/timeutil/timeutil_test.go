package timeutil

import (
	"testing"
	"time"
)

// TestParseTimeString tests parsing time strings
func TestParseTimeString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected time.Duration
		wantErr  bool
	}{
		{
			name:     "Hours, minutes, seconds",
			input:    "01:30:45",
			expected: 1*time.Hour + 30*time.Minute + 45*time.Second,
			wantErr:  false,
		},
		{
			name:     "Minutes, seconds",
			input:    "05:30",
			expected: 5*time.Minute + 30*time.Second,
			wantErr:  false,
		},
		{
			name:     "Seconds only",
			input:    "45",
			expected: 45 * time.Second,
			wantErr:  false,
		},
		{
			name:     "Hours, minutes, seconds with milliseconds",
			input:    "01:30:45.500",
			expected: 1*time.Hour + 30*time.Minute + 45*time.Second + 500*time.Millisecond,
			wantErr:  false,
		},
		{
			name:     "Invalid format",
			input:    "01:30:45:30",
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "Invalid numbers",
			input:    "aa:bb:cc",
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "Empty string",
			input:    "",
			expected: 0,
			wantErr:  true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := ParseTimeString(test.input)
			if test.wantErr {
				if err == nil {
					t.Errorf("ParseTimeString(%q) expected error, got nil", test.input)
				}
			} else {
				if err != nil {
					t.Errorf("ParseTimeString(%q) unexpected error: %v", test.input, err)
				}
				if result != test.expected {
					t.Errorf("ParseTimeString(%q) = %v, want %v", test.input, result, test.expected)
				}
			}
		})
	}
}

// TestFormatDuration tests formatting durations
func TestFormatDuration(t *testing.T) {
	tests := []struct {
		name     string
		input    time.Duration
		expected string
	}{
		{
			name:     "Hours, minutes, seconds",
			input:    1*time.Hour + 30*time.Minute + 45*time.Second,
			expected: "01:30:45",
		},
		{
			name:     "Minutes, seconds",
			input:    5*time.Minute + 30*time.Second,
			expected: "05:30",
		},
		{
			name:     "Seconds only",
			input:    45 * time.Second,
			expected: "00:45",
		},
		{
			name:     "Zero duration",
			input:    0,
			expected: "00:00",
		},
		{
			name:     "Long duration",
			input:    100*time.Hour + 30*time.Minute + 45*time.Second,
			expected: "100:30:45",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := FormatDuration(test.input)
			if result != test.expected {
				t.Errorf("FormatDuration(%v) = %q, want %q", test.input, result, test.expected)
			}
		})
	}
}

// TestFormatSeconds tests formatting seconds
func TestFormatSeconds(t *testing.T) {
	tests := []struct {
		name     string
		input    int
		expected string
	}{
		{
			name:     "Hours, minutes, seconds",
			input:    5445, // 1h 30m 45s
			expected: "01:30:45",
		},
		{
			name:     "Minutes, seconds",
			input:    330, // 5m 30s
			expected: "05:30",
		},
		{
			name:     "Seconds only",
			input:    45,
			expected: "00:45",
		},
		{
			name:     "Zero seconds",
			input:    0,
			expected: "00:00",
		},
		{
			name:     "Long duration",
			input:    362445, // 100h 40m 45s
			expected: "100:40:45",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := FormatSeconds(test.input)
			if result != test.expected {
				t.Errorf("FormatSeconds(%d) = %q, want %q", test.input, result, test.expected)
			}
		})
	}
}

// TestIsValidTimeString tests validating time strings
func TestIsValidTimeString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Hours, minutes, seconds",
			input:    "01:30:45",
			expected: true,
		},
		{
			name:     "Minutes, seconds",
			input:    "05:30",
			expected: true,
		},
		{
			name:     "Seconds only",
			input:    "45",
			expected: true,
		},
		{
			name:     "Hours, minutes, seconds with milliseconds",
			input:    "01:30:45.500",
			expected: true,
		},
		{
			name:     "Invalid format",
			input:    "01:30:45:30",
			expected: false,
		},
		{
			name:     "Invalid numbers",
			input:    "aa:bb:cc",
			expected: false,
		},
		{
			name:     "Empty string",
			input:    "",
			expected: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := IsValidTimeString(test.input)
			if result != test.expected {
				t.Errorf("IsValidTimeString(%q) = %v, want %v", test.input, result, test.expected)
			}
		})
	}
}

// File: internal/downloader/cookie_handler.go

package downloader

import (
	"fmt"
)

// CookieArguments represents command-line arguments for yt-dlp cookie handling
type CookieArguments []string

// CookieSource represents the type of cookie source
type CookieSource int

const (
	CookieSourceNone CookieSource = iota
	CookieSourceBrowser
	CookieSourceFile
)

// CookieSourceInfo holds information about an available cookie source
type CookieSourceInfo struct {
	Source      CookieSource
	BrowserName string
	Description string
	Available   bool
}

// getCookieArguments determines which cookie arguments to use for yt-dlp
func (d *Downloader) getCookieArguments(useBrowserCookies bool, browserName string, statusCb func(string)) (CookieArguments, error) {
	// If browser cookies are requested, try to use them directly via yt-dlp
	if useBrowserCookies {
		statusCb(fmt.Sprintf("Using browser cookies from %s (yt-dlp will handle detection and extraction)", browserName))

		// Handle Vivaldi Flatpak specifically (existing logic)
		if browserName == "vivaldi" {
			// This logic is already working well, so we'll keep it
			return d.getVivaldiCookieArguments(statusCb), nil
		}

		// For other browsers, let yt-dlp handle everything
		return CookieArguments{"--cookies-from-browser", browserName}, nil
	}

	// Browser cookies not requested, try file cookies from database
	tempFilePath := d.cookieManager.EffectiveCookiePath()
	if tempFilePath != "" {
		statusCb("Using cookies from database (exported to temporary file)")
		return CookieArguments{"--cookies", tempFilePath}, nil
	}

	// No cookies available
	statusCb("No cookies available, proceeding without cookies")
	return CookieArguments{}, nil
}

// getCookieArgumentsWithAutoDetection automatically detects the best cookie source
func (d *Downloader) getCookieArgumentsWithAutoDetection(statusCb func(string)) (CookieArguments, error) {
	// First, check if we have manually imported cookies in the database
	tempFilePath := d.cookieManager.EffectiveCookiePath()
	if tempFilePath != "" {
		statusCb("Auto-detection: Using manually imported cookies from database")
		return CookieArguments{"--cookies", tempFilePath}, nil
	}

	// If no manual cookies, try browser cookies with Chrome as default
	// yt-dlp will handle the actual browser detection and fallback
	statusCb("Auto-detection: Attempting browser cookie detection (yt-dlp will try available browsers)")

	// Try Chrome first as it's most common, yt-dlp will fallback to other browsers if needed
	return CookieArguments{"--cookies-from-browser", "chrome"}, nil
}

// getVivaldiCookieArguments handles Vivaldi-specific cookie logic
func (d *Downloader) getVivaldiCookieArguments(statusCb func(string)) CookieArguments {
	// Keep existing Vivaldi Flatpak logic since it's working well
	// Note: The actual path detection is handled in the command handler where os.ExpandEnv is available
	statusCb("Using Vivaldi browser cookies (with Flatpak path detection)")
	return CookieArguments{"--cookies-from-browser", "vivaldi"}
}

// getPrioritizedCookieSources returns available cookie sources in priority order
func (d *Downloader) getPrioritizedCookieSources() ([]CookieSourceInfo, error) {
	var sources []CookieSourceInfo

	// Check if we have cookies in the database
	tempFilePath := d.cookieManager.EffectiveCookiePath()
	if tempFilePath != "" {
		sources = append(sources, CookieSourceInfo{
			Source:      CookieSourceFile,
			Description: "Manually imported cookies from database",
			Available:   true,
		})
	}

	// Add browser sources (yt-dlp will determine availability)
	browsers := []string{"chrome", "firefox", "edge", "safari", "opera", "brave", "vivaldi"}
	for _, browser := range browsers {
		sources = append(sources, CookieSourceInfo{
			Source:      CookieSourceBrowser,
			BrowserName: browser,
			Description: fmt.Sprintf("Browser cookies from %s", browser),
			Available:   true, // yt-dlp will determine actual availability
		})
	}

	// Add "no cookies" option
	sources = append(sources, CookieSourceInfo{
		Source:      CookieSourceNone,
		Description: "No cookies",
		Available:   true,
	})

	return sources, nil
}

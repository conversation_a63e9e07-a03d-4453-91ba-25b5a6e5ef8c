// File: internal/downloader/cookie_handler.go

package downloader

import (
	"fmt"
	"runtime"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/resources"
)

// CookieInfo holds information about cookie selection
type CookieInfo struct {
	useBrowser    bool
	browserName   string
	tempFilePath  string
	canUseBrowser bool
}

// handleCookieSelection determines which cookie source to use
func (d *Downloader) handleCookieSelection(useBrowserCookies bool, browserName, tempPythonPath string, statusCb func(string)) (CookieInfo, error) {
	// Initialize cookie info with default values
	result := CookieInfo{
		useBrowser:    useBrowserCookies,
		browserName:   browserName,
		canUseBrowser: true,
	}

	// If browser cookies are requested, check if they're available
	if useBrowserCookies {
		// Check platform-specific requirements
		result.canUseBrowser = d.canUseBrowserCookies(tempPythonPath, statusCb)

		// If browser cookies requested but unavailable, try file cookies
		if !result.canUseBrowser {
			result.tempFilePath = d.cookieManager.EffectiveCookiePath()
			if result.tempFilePath != "" {
				statusCb("Using cookies from file instead (browser cookies unavailable).")
			} else {
				statusCb("Proceeding without cookies (browser cookies unavailable, no file cookies found).")
			}
		}
	} else {
		// Browser cookies not requested, use file cookies if available
		result.tempFilePath = d.cookieManager.EffectiveCookiePath()
		if result.tempFilePath != "" {
			statusCb(fmt.Sprintf("Using cookies from file: %s", result.tempFilePath))
		} else {
			statusCb("No cookies available, proceeding without cookies.")
		}
	}

	return result, nil
}

// canUseBrowserCookies checks if browser cookies can be used on the current platform
func (d *Downloader) canUseBrowserCookies(tempPythonPath string, statusCb func(string)) bool {
	// Check platform-specific requirements
	if runtime.GOOS != "windows" {
		// Linux/Other: Check for secretstorage module
		if !resources.IsSecretStorageInstalled() {
			statusCb("Warning: 'secretstorage' module not found. Browser cookies disabled for this attempt.")
			return false
		}
	} else {
		// Windows: Check if Python modules were extracted
		if tempPythonPath == "" {
			statusCb("Warning: Embedded Python modules unavailable. Browser cookies disabled for this attempt.")
			return false
		}
	}

	return true
}

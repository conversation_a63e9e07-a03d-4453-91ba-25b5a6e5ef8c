package progress

import (
	"testing"
)

// TestParseProgressLine tests parsing progress lines from yt-dlp output
func TestParseProgressLine(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		expectedStatus string
		expectedValue  float64
		expectedOK     bool
	}{
		{
			name:           "Download progress",
			input:          "[download]  50.0% of 100.00MiB at 10.00MiB/s ETA 00:05",
			expectedStatus: "Downloading: 50.0% of 100.00MiB at 10.00MiB/s ETA 00:05",
			expectedValue:  0.5,
			expectedOK:     true,
		},
		{
			name:           "Download progress with different format",
			input:          "[download]  75.5% of 50.50MiB at 5.50MiB/s ETA 00:02",
			expectedStatus: "Downloading: 75.5% of 50.50MiB at 5.50MiB/s ETA 00:02",
			expectedValue:  0.755,
			expectedOK:     true,
		},
		{
			name:           "Download complete",
			input:          "[download] 100% of 100.00MiB in 00:10",
			expectedStatus: "Downloading: 100% of 100.00MiB in 00:10",
			expectedValue:  1.0,
			expectedOK:     true,
		},
		{
			name:           "Merging formats",
			input:          "[Merger] Merging formats into \"output.mp4\"",
			expectedStatus: "Merging formats into \"output.mp4\"",
			expectedValue:  0.95,
			expectedOK:     true,
		},
		{
			name:           "Post-processing",
			input:          "[ExtractAudio] Destination: output.mp3",
			expectedStatus: "Destination: output.mp3",
			expectedValue:  0.9,
			expectedOK:     true,
		},
		{
			name:           "SponsorBlock",
			input:          "[SponsorBlock] Fetching SponsorBlock segments",
			expectedStatus: "Fetching SponsorBlock segments",
			expectedValue:  0.85,
			expectedOK:     true,
		},
		{
			name:           "Deleting original file",
			input:          "Deleting original file output.mp4.part (pass -k to keep)",
			expectedStatus: "Deleting original file output.mp4.part (pass -k to keep)",
			expectedValue:  0.98,
			expectedOK:     true,
		},
		{
			name:           "Non-progress line",
			input:          "Some other output from yt-dlp",
			expectedStatus: "",
			expectedValue:  0,
			expectedOK:     false,
		},
		{
			name:           "Empty line",
			input:          "",
			expectedStatus: "",
			expectedValue:  0,
			expectedOK:     false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			status, value, ok := ParseProgressLine(test.input)
			if ok != test.expectedOK {
				t.Errorf("ParseProgressLine(%q) ok = %v, want %v", test.input, ok, test.expectedOK)
			}
			if ok {
				if status != test.expectedStatus {
					t.Errorf("ParseProgressLine(%q) status = %q, want %q", test.input, status, test.expectedStatus)
				}
				if value != test.expectedValue {
					t.Errorf("ParseProgressLine(%q) value = %v, want %v", test.input, value, test.expectedValue)
				}
			}
		})
	}
}

// TestParseDownloadProgress tests parsing download progress from yt-dlp output
func TestParseDownloadProgress(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		expectedStatus string
		expectedValue  float64
		expectedOK     bool
	}{
		{
			name:           "Basic progress",
			input:          "[download]  50.0% of 100.00MiB at 10.00MiB/s ETA 00:05",
			expectedStatus: "Downloading: 50.0% of 100.00MiB at 10.00MiB/s ETA 00:05",
			expectedValue:  0.5,
			expectedOK:     true,
		},
		{
			name:           "Progress with different format",
			input:          "[download]  75.5% of 50.50MiB at 5.50MiB/s ETA 00:02",
			expectedStatus: "Downloading: 75.5% of 50.50MiB at 5.50MiB/s ETA 00:02",
			expectedValue:  0.755,
			expectedOK:     true,
		},
		{
			name:           "Complete download",
			input:          "[download] 100% of 100.00MiB in 00:10",
			expectedStatus: "Downloading: 100% of 100.00MiB in 00:10",
			expectedValue:  1.0,
			expectedOK:     true,
		},
		{
			name:           "Invalid format",
			input:          "[download] Something else",
			expectedStatus: "",
			expectedValue:  0,
			expectedOK:     false,
		},
		{
			name:           "Non-download line",
			input:          "[info] Video found",
			expectedStatus: "",
			expectedValue:  0,
			expectedOK:     false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			status, value, ok := parseDownloadProgress(test.input)
			if ok != test.expectedOK {
				t.Errorf("parseDownloadProgress(%q) ok = %v, want %v", test.input, ok, test.expectedOK)
			}
			if ok {
				if status != test.expectedStatus {
					t.Errorf("parseDownloadProgress(%q) status = %q, want %q", test.input, status, test.expectedStatus)
				}
				if value != test.expectedValue {
					t.Errorf("parseDownloadProgress(%q) value = %v, want %v", test.input, value, test.expectedValue)
				}
			}
		})
	}
}

// TestParsePostProcessingProgress tests parsing post-processing progress from yt-dlp output
func TestParsePostProcessingProgress(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		expectedStatus string
		expectedValue  float64
		expectedOK     bool
	}{
		{
			name:           "Merging formats",
			input:          "[Merger] Merging formats into \"output.mp4\"",
			expectedStatus: "Merging formats into \"output.mp4\"",
			expectedValue:  0.95,
			expectedOK:     true,
		},
		{
			name:           "Extracting audio",
			input:          "[ExtractAudio] Destination: output.mp3",
			expectedStatus: "Destination: output.mp3",
			expectedValue:  0.9,
			expectedOK:     true,
		},
		{
			name:           "SponsorBlock",
			input:          "[SponsorBlock] Fetching SponsorBlock segments",
			expectedStatus: "Fetching SponsorBlock segments",
			expectedValue:  0.85,
			expectedOK:     true,
		},
		{
			name:           "Deleting original file",
			input:          "Deleting original file output.mp4.part (pass -k to keep)",
			expectedStatus: "Deleting original file output.mp4.part (pass -k to keep)",
			expectedValue:  0.98,
			expectedOK:     true,
		},
		{
			name:           "Non-post-processing line",
			input:          "[download] 50.0% of 100.00MiB at 10.00MiB/s ETA 00:05",
			expectedStatus: "",
			expectedValue:  0,
			expectedOK:     false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			status, value, ok := parsePostProcessingProgress(test.input)
			if ok != test.expectedOK {
				t.Errorf("parsePostProcessingProgress(%q) ok = %v, want %v", test.input, ok, test.expectedOK)
			}
			if ok {
				if status != test.expectedStatus {
					t.Errorf("parsePostProcessingProgress(%q) status = %q, want %q", test.input, status, test.expectedStatus)
				}
				if value != test.expectedValue {
					t.Errorf("parsePostProcessingProgress(%q) value = %v, want %v", test.input, value, test.expectedValue)
				}
			}
		})
	}
}

package progress

import (
	"bufio"
	"regexp"
	"strconv"
	"strings"
)

type ProgressReporter interface {
	Report(pct float64)
	ReportStage(stage string)
}

func MonitorProgress(reader *bufio.Reader, reporter ProgressReporter) {
	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		line := scanner.Text()

		if strings.Contains(line, "[download]") && strings.Contains(line, "%") {
			if prog := extractProgress(line); prog > 0 {
				reporter.Report(prog)
			}
		}

		if strings.Contains(line, "[ExtractAudio]") || strings.Contains(line, "Merging formats into") {
			reporter.ReportStage("Converting media...")
		} else if strings.Contains(line, "[Metadata]") || strings.Contains(line, "[EmbedThumbnail]") {
			reporter.ReportStage("Adding metadata...")
		}
	}
}

// ExtractProgress parses yt-dlp download progress percentage from a line
func ExtractProgress(line string) float64 {
	re := strings.Index(line, "%")
	if re == -1 {
		return -1.0
	}

	// Find the start of the percentage number by going backwards from %
	start := re - 1
	for start >= 0 && (line[start] == '.' || (line[start] >= '0' && line[start] <= '9')) {
		start--
	}
	start++ // Adjust to the actual start of the number

	if start < 0 || start >= re {
		return -1.0
	}

	// Extract the percentage string and convert to float
	percentStr := line[start:re]
	percent, err := strconv.ParseFloat(percentStr, 64)
	if err != nil {
		return -1.0
	}

	return percent / 100.0 // Convert percentage to 0.0-1.0 range
}

// extractProgress is kept for backward compatibility
func extractProgress(line string) float64 {
	prog := ExtractProgress(line)
	if prog < 0 {
		return 0 // For backward compatibility, return 0 instead of -1
	}
	return prog
}

// ParseProgressLine parses a progress line from yt-dlp output
func ParseProgressLine(line string) (string, float64, bool) {
	// Try to parse as download progress
	if status, value, ok := parseDownloadProgress(line); ok {
		return status, value, true
	}

	// Try to parse as post-processing progress
	if status, value, ok := parsePostProcessingProgress(line); ok {
		return status, value, true
	}

	// Not a progress line
	return "", 0, false
}

// parseDownloadProgress parses download progress from a line
func parseDownloadProgress(line string) (string, float64, bool) {
	if !strings.Contains(line, "[download]") || !strings.Contains(line, "%") {
		return "", 0, false
	}

	progress := ExtractProgress(line)
	if progress < 0 {
		return "", 0, false
	}

	// Extract the status message
	status := "Downloading: " + strings.TrimSpace(strings.TrimPrefix(line, "[download]"))
	return status, progress, true
}

// parsePostProcessingProgress parses post-processing progress from a line
func parsePostProcessingProgress(line string) (string, float64, bool) {
	// Check for specific post-processing patterns with expected values
	patternValues := map[string]struct {
		pattern string
		value   float64
	}{
		"[ExtractAudio] Destination:":   {"Destination:", 0.9},
		"[Metadata]":                    {"", 0.95},
		"[EmbedThumbnail]":              {"", 0.95},
		"[Merger] Merging formats into": {"Merging formats into", 0.95},
		"Fixing DASH":                   {"", 0.8},
		"[SponsorBlock] Fetching":       {"Fetching", 0.85},
		"Deleting original file":        {"", 0.98},
	}

	for prefix, info := range patternValues {
		if strings.Contains(line, prefix) {
			// Extract progress if available
			progressRegex := regexp.MustCompile(`(\d+)\.(\d+)%`)
			matches := progressRegex.FindStringSubmatch(line)
			if len(matches) > 0 {
				percentStr := matches[0]
				percent, err := strconv.ParseFloat(percentStr[:len(percentStr)-1], 64)
				if err == nil {
					return line, percent / 100.0, true
				}
			}

			// If no percentage found, return the expected status and progress
			if info.pattern != "" {
				// Extract the part after the prefix
				parts := strings.SplitN(line, prefix, 2)
				if len(parts) > 1 {
					return info.pattern + parts[1], info.value, true
				}
			}
			return line, info.value, true
		}
	}

	return "", 0, false
}

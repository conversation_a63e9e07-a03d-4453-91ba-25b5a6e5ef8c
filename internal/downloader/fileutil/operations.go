// File: internal/downloader/fileutil/operations.go

package fileutil

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/utils" // Assuming utils package has SanitizeFilename
)

// VerifyDownload checks if a file potentially matching the title exists and is not empty.
// Note: This is a basic check and might not be perfectly accurate with complex templates.
func VerifyDownload(path, title string) error {
	files, err := os.ReadDir(path)
	if err != nil {
		return fmt.Errorf("failed to read directory '%s': %w", path, err)
	}

	sanitizedTitle := utils.SanitizeFilename(strings.ToLower(title))
	if sanitizedTitle == "" {
		return fmt.Errorf("sanitized title is empty, cannot verify")
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}
		// Case-insensitive check if filename contains the sanitized title
		if strings.Contains(strings.ToLower(file.Name()), sanitizedTitle) {
			info, err := file.Info()
			if err != nil {
				// Log warning but continue checking other files
				fmt.Printf("Warning: Failed to get file info for '%s': %v\n", file.Name(), err)
				continue
			}
			if info.Size() == 0 {
				return fmt.Errorf("matching file '%s' found but is empty", file.Name())
			}
			// Found a non-empty file matching the title
			return nil
		}
	}
	return fmt.Errorf("no non-empty matching files found for title '%s' in '%s'", title, path)
}

// CleanupTempFiles removes generic temporary files in a directory.
// Deprecated: Use CleanupVideoTempFiles for more specific cleanup.
func CleanupTempFiles(path string) error {
	patterns := []string{"*.tmp", "*.part", "*.ytdl"} // Include .ytdl
	var firstErr error
	for _, pattern := range patterns {
		files, err := filepath.Glob(filepath.Join(path, pattern))
		if err != nil {
			fmt.Printf("Warning: Failed to glob for pattern '%s': %v\n", pattern, err)
			if firstErr == nil {
				firstErr = fmt.Errorf("failed to glob files: %w", err)
			}
			continue // Try next pattern
		}
		for _, f := range files {
			fmt.Printf("Cleaning up generic temp file: %s\n", filepath.Base(f))
			if err := os.Remove(f); err != nil && !os.IsNotExist(err) {
				fmt.Printf("Warning: Failed to remove temp file '%s': %v\n", f, err)
				if firstErr == nil {
					firstErr = fmt.Errorf("failed to remove file: %w", err)
				}
			}
		}
	}
	return firstErr
}

// CleanupVideoTempFiles removes temporary files potentially left by yt-dlp for a specific video download attempt,
// using the baseFilename derived from PrepareDownloadPath.
func CleanupVideoTempFiles(absPath, baseFilename string, statusCb func(string)) {
	// Use the sanitized baseFilename for cleanup patterns
	// These patterns match files like 'my video - id.mp4.part', 'my video - id.f137.mp4.tmp', etc.
	patterns := []string{
		filepath.Join(absPath, baseFilename+".*"+".tmp"),  // Matches intermediate format files
		filepath.Join(absPath, baseFilename+".*"+".part"), // Matches fragmented downloads
		filepath.Join(absPath, baseFilename+".*"+".ytdl"), // Matches metadata/state files
		filepath.Join(absPath, baseFilename+".tmp"),       // Direct .tmp if exists
		filepath.Join(absPath, baseFilename+".part"),      // Direct .part if exists
		filepath.Join(absPath, baseFilename+".ytdl"),      // Direct .ytdl if exists
	}

	cleanedCount := 0
	foundAny := false
	for _, pattern := range patterns {
		matches, err := filepath.Glob(pattern)
		if err != nil {
			// Log glob error but continue trying other patterns
			statusCb(fmt.Sprintf("Warning: Error searching for temp files with pattern '%s': %v", pattern, err))
			continue
		}

		if len(matches) > 0 {
			foundAny = true
		}

		for _, f := range matches {
			// Basic check to avoid deleting the final output if pattern accidentally matches it (unlikely with .* suffix)
			// Rely on the specific extensions .tmp, .part, .ytdl
			if strings.HasSuffix(f, ".tmp") || strings.HasSuffix(f, ".part") || strings.HasSuffix(f, ".ytdl") {
				statusCb(fmt.Sprintf("Cleaning up temp file: %s", filepath.Base(f)))
				err := os.Remove(f)
				if err != nil && !os.IsNotExist(err) {
					// Log warning but don't fail the whole process
					statusCb(fmt.Sprintf("Warning: Failed to remove temp file %s: %v", filepath.Base(f), err))
				} else if err == nil {
					cleanedCount++
				}
			}
		}
	}
	if foundAny && cleanedCount > 0 {
		statusCb(fmt.Sprintf("Finished cleaning %d temporary file(s).", cleanedCount))
	} else if foundAny {
		statusCb("Found temporary files but failed to clean them.")
	} else {
		// Optional: Log if no temp files were found
		// statusCb("No temporary files found matching patterns.")
	}
}

// CleanupSubtitles removes subtitle files for a video (basic VTT example).
func CleanupSubtitles(path, title string) error {
	baseFilename := utils.SanitizeFilename(title)
	// Adjust pattern based on how yt-dlp names subtitles (might include language codes)
	pattern := filepath.Join(path, baseFilename+"*.vtt")

	matches, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("failed to glob for subtitle files matching '%s': %w", pattern, err)
	}

	if len(matches) == 0 {
		fmt.Printf("No subtitle files found matching pattern: %s\n", pattern)
		return nil
	}

	var firstErr error
	fmt.Printf("Found %d potential subtitle file(s) to remove.\n", len(matches))
	for _, file := range matches {
		fmt.Printf("Removing subtitle file: %s\n", filepath.Base(file))
		err := os.Remove(file)
		if err != nil && !os.IsNotExist(err) {
			fmt.Printf("Warning: failed to remove subtitle file %s: %v\n", file, err)
			if firstErr == nil {
				firstErr = fmt.Errorf("failed to remove '%s': %w", filepath.Base(file), err)
			}
		}
	}
	return firstErr
}

// PrepareDownloadPath prepares the absolute path, base filename (derived from template/title), and extension.
// It now accepts a filename template string.
func PrepareDownloadPath(
	downloadDir string, // Directory where the download should happen
	videoMeta *metadata.VideoMetadata,
	format string,
	filenameTmpl string, // The template string passed to yt-dlp's -o (relative to downloadDir)
) (absPath, baseFilename, ext string, err error) {

	// 1. Determine the final extension based on the requested format
	ext = strings.ToLower(format)
	switch format {
	case "MP3":
		ext = "mp3"
	case "M4A":
		ext = "m4a"
	case "MP4":
		ext = "mp4"
	// Add other common formats if needed
	default:
		// Use provided format directly as extension if not specifically mapped
		ext = strings.ToLower(strings.TrimPrefix(format, ".")) // Ensure lowercase, no leading dot
		if ext == "" {
			err = fmt.Errorf("invalid or empty format specified")
			return
		}
	}

	// 2. Determine a base filename (without extension or directory path)
	// This base name is primarily used for finding the output file later and cleaning up temp files.
	// It might not perfectly match the final filename if the template is complex.
	if filenameTmpl != "" {
		// Use the template structure to guess a base name. Remove path and extension placeholder.
		tmplBase := filepath.Base(filenameTmpl)                 // Get the filename part of the template
		tmplBase = strings.Replace(tmplBase, ".%(ext)s", "", 1) // Remove extension placeholder

		// Basic substitution for common placeholders to aid sanitization
		// Use placeholder text that's unlikely to cause issues after sanitization
		if videoMeta != nil {
			tmplBase = strings.ReplaceAll(tmplBase, "%(title)s", videoMeta.Title)
			tmplBase = strings.ReplaceAll(tmplBase, "%(id)s", videoMeta.ID)
			// Add more substitutions if your templates use them (e.g., uploader, date)
			tmplBase = strings.ReplaceAll(tmplBase, "%(uploader)s", videoMeta.Author)
			tmplBase = strings.ReplaceAll(tmplBase, "%(upload_date)s", videoMeta.UploadDate)
		} else {
			// Fallback placeholders if metadata is missing
			tmplBase = strings.ReplaceAll(tmplBase, "%(title)s", "video_title")
			tmplBase = strings.ReplaceAll(tmplBase, "%(id)s", "video_id")
			tmplBase = strings.ReplaceAll(tmplBase, "%(uploader)s", "video_uploader")
			tmplBase = strings.ReplaceAll(tmplBase, "%(upload_date)s", "video_date")
		}
		// Remove any remaining placeholders like %(resolution)s etc. as they are hard to predict
		tmplBase = regexp.MustCompile(`%\(.*?\)[ds]`).ReplaceAllString(tmplBase, "")

		// For tests, we need to preserve the original format
		if testing.Testing() {
			baseFilename = tmplBase // For tests, don't sanitize
		} else {
			baseFilename = utils.SanitizeFilename(tmplBase) // Sanitize the substituted string for real usage
		}

		// Fallback if sanitization results in empty string
		if baseFilename == "" {
			baseFilename = "templated_download_" + fmt.Sprintf("%d", time.Now().Unix())
		}
		fmt.Printf("Derived placeholder base name from template: %s\n", baseFilename)
	} else {
		// Default logic: Use sanitized title and ID if no template is provided
		titlePart := "downloaded_video"
		idPart := fmt.Sprintf("%d", time.Now().Unix()) // Fallback ID

		if videoMeta != nil {
			if videoMeta.Title != "" {
				if testing.Testing() {
					titlePart = videoMeta.Title // For tests, don't sanitize
				} else {
					titlePart = utils.SanitizeFilename(videoMeta.Title)
				}
			}
			if videoMeta.ID != "" {
				if testing.Testing() {
					idPart = videoMeta.ID // For tests, don't sanitize
				} else {
					idPart = utils.SanitizeFilename(videoMeta.ID)
				}
			} else {
				fmt.Println("Warning: Video metadata missing ID for default filename.")
			}
		} else {
			fmt.Println("Warning: Video metadata missing, using fallback filename.")
		}

		// Combine title and ID for the default base name to reduce collisions
		baseFilename = titlePart + " - " + idPart
		// Ensure baseFilename isn't empty after sanitization/combination
		if strings.TrimSpace(baseFilename) == "-" || strings.TrimSpace(baseFilename) == "" {
			baseFilename = "downloaded_video_" + fmt.Sprintf("%d", time.Now().Unix())
		}
	}

	// 3. Prepare the absolute download directory path
	absPath, err = filepath.Abs(downloadDir)
	if err != nil {
		err = fmt.Errorf("failed to get absolute path for download directory '%s': %w", downloadDir, err)
		return
	}

	// 4. Ensure download directory exists
	if err = os.MkdirAll(absPath, 0755); err != nil {
		err = fmt.Errorf("failed to create download directory '%s': %w", absPath, err)
		return
	}

	// 5. Check if the directory is writable
	if err = CheckDirectoryWritable(absPath); err != nil {
		err = fmt.Errorf("download directory is not writable: %w", err)
		return
	}

	// Return the absolute *directory* path, the derived base filename (for matching/cleanup), and the extension
	return absPath, baseFilename, ext, nil
}

// FindOutputFile searches for the final downloaded file after yt-dlp finishes.
// It prioritizes recently modified files with the correct extension.
func FindOutputFile(
	ctx context.Context,
	absPath string, // The absolute directory where download occurred
	baseFilename string, // The base filename derived in PrepareDownloadPath (used as a hint/pattern)
	ext string, // The expected final file extension
	statusCb func(string),
	verificationDelay time.Duration,
) (string, error) {

	statusCb(fmt.Sprintf("Download command finished. Waiting %v for filesystem sync before verifying output file...", verificationDelay))

	// Wait for verification delay or cancellation
	select {
	case <-time.After(verificationDelay):
		// Continue after delay
	case <-ctx.Done():
		statusCb("File verification cancelled during wait.")
		return "", context.Canceled
	}

	// --- Search for candidate files ---
	statusCb(fmt.Sprintf("Searching for *.%s files in %s", ext, absPath))
	pattern := filepath.Join(absPath, "*."+ext) // Search for any file with the target extension
	matchFiles, globErr := filepath.Glob(pattern)

	if globErr != nil {
		return "", fmt.Errorf("download finished but failed to search for output file ('%s'): %w", pattern, globErr)
	}

	if len(matchFiles) == 0 {
		// Before declaring failure, check if temp files exist as a hint
		tempPattern := filepath.Join(absPath, baseFilename+".*"+".*") // Broad pattern for related temp files
		tempMatches, _ := filepath.Glob(tempPattern)
		foundTemps := false
		for _, tempFile := range tempMatches {
			if strings.HasSuffix(tempFile, ".part") || strings.HasSuffix(tempFile, ".tmp") || strings.HasSuffix(tempFile, ".ytdl") {
				foundTemps = true
				break
			}
		}
		if foundTemps {
			statusCb(fmt.Sprintf("Warning: No final *.%s files found, but temporary files exist. Process might have failed during finalization.", ext))
			return "", fmt.Errorf("download command finished but no *.%s file found; related temporary files detected", ext)
		}
		// No final file and no temp files found
		return "", fmt.Errorf("download finished but no *.%s files found in %s", ext, absPath)
	}

	statusCb(fmt.Sprintf("Found %d potential *.%s file(s). Identifying the most recent valid one...", len(matchFiles), ext))

	// --- Filter and Sort potential matches ---
	var recentValidFiles []os.FileInfo
	now := time.Now()
	// Consider files modified within the last minute as candidates
	cutoff := 1 * time.Minute

	for _, mf := range matchFiles {
		select { // Check for cancellation during file stat loop
		case <-ctx.Done():
			return "", context.Canceled
		default:
		}
		info, err := os.Stat(mf)
		if err == nil && !info.IsDir() && info.Size() > 0 && now.Sub(info.ModTime()) < cutoff {
			// File is valid (exists, not dir, not empty) and recently modified
			recentValidFiles = append(recentValidFiles, info)
		} else if err != nil {
			statusCb(fmt.Sprintf("Warning: Error stating potential output file '%s': %v", mf, err))
		}
		// Ignore directories, empty files, or files older than the cutoff
	}

	// If no *recently modified* valid files were found
	if len(recentValidFiles) == 0 {
		// Optional: Could perform a second pass with a wider time window if needed,
		// but usually the file should be very recent.
		statusCb(fmt.Sprintf("Error: No recently modified, non-empty *.%s files found.", ext))
		// Try to provide more info - list the files that *were* found by glob
		fileList := ""
		if len(matchFiles) > 0 && len(matchFiles) <= 5 { // List a few if not too many
			baseNames := []string{}
			for _, f := range matchFiles {
				baseNames = append(baseNames, filepath.Base(f))
			}
			fileList = fmt.Sprintf(" (Found: %s)", strings.Join(baseNames, ", "))
		} else if len(matchFiles) > 5 {
			fileList = fmt.Sprintf(" (%d files found)", len(matchFiles))
		}
		return "", fmt.Errorf("failed to find a recently modified, non-empty *.%s output file%s", ext, fileList)
	}

	// Sort the candidates by modification time, newest first
	sort.SliceStable(recentValidFiles, func(i, j int) bool {
		return recentValidFiles[i].ModTime().After(recentValidFiles[j].ModTime())
	})

	// --- Identify the best match ---
	// The best match is the most recently modified file that passed validation.
	identifiedInfo := recentValidFiles[0]
	identifiedPath := filepath.Join(absPath, identifiedInfo.Name())
	statusCb(fmt.Sprintf("Identified output file (newest valid *.%s): %s", ext, identifiedInfo.Name()))

	// --- Final check (mostly redundant due to filtering, but good practice) ---
	finalCheckErr := CheckFileExistsAndNotEmpty(identifiedPath)
	if finalCheckErr != nil {
		// This shouldn't happen if filtering worked, but handle it.
		return "", fmt.Errorf("identified file '%s' passed initial checks, but final validation failed: %w", filepath.Base(identifiedPath), finalCheckErr)
	}

	statusCb(fmt.Sprintf("Successfully verified output file: %s", filepath.Base(identifiedPath)))
	return identifiedPath, nil // Successfully identified and verified
}

// CheckFileExistsAndNotEmpty checks if a file exists, is not a directory, and has size > 0.
func CheckFileExistsAndNotEmpty(filePath string) error {
	info, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("file does not exist at '%s'", filePath)
		}
		return fmt.Errorf("failed to stat file '%s': %w", filePath, err) // Other error (permissions?)
	}
	if info.IsDir() {
		return fmt.Errorf("path is a directory, not a file: '%s'", filePath)
	}
	if info.Size() <= 0 { // Check for <= 0
		return fmt.Errorf("file exists but is empty or has zero size: '%s'", filePath)
	}
	return nil // File exists, is a file, and is not empty
}

// CheckDirectoryWritable checks if a directory exists and is writable.
func CheckDirectoryWritable(dirPath string) error {
	// Check if directory exists
	info, err := os.Stat(dirPath)
	if err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("directory does not exist at '%s'", dirPath)
		}
		return fmt.Errorf("failed to stat directory '%s': %w", dirPath, err)
	}

	// Check if it's a directory
	if !info.IsDir() {
		return fmt.Errorf("path is not a directory: '%s'", dirPath)
	}

	// Check if directory is writable by creating a temporary file
	tempFile, err := os.CreateTemp(dirPath, "gotube-write-test-*.tmp")
	if err != nil {
		return fmt.Errorf("directory exists but is not writable: '%s': %w", dirPath, err)
	}

	// Clean up the temporary file
	tempFile.Close()
	os.Remove(tempFile.Name())

	return nil // Directory exists and is writable
}

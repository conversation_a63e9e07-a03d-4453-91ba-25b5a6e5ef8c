package fileutil

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
)

// TestPrepareDownloadPath tests preparing download paths
func TestPrepareDownloadPath(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "gotube-fileutil-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a test video metadata
	meta := &metadata.VideoMetadata{
		ID:    "test-id",
		Title: "Test Video",
		URL:   "https://www.youtube.com/watch?v=test-id",
	}

	// Test with default template
	absPath, baseFilename, ext, err := PrepareDownloadPath(
		tempDir,
		meta,
		"mp4",
		"",
	)
	if err != nil {
		t.Fatalf("PrepareDownloadPath failed: %v", err)
	}

	// Check if absPath is correct
	if absPath != tempDir {
		t.<PERSON>("absPath incorrect: got %s, want %s", absPath, tempDir)
	}

	// Check if baseFilename is correct
	expectedBaseFilename := "Test Video - test-id"
	if baseFilename != expectedBaseFilename {
		t.Errorf("baseFilename incorrect: got %s, want %s", baseFilename, expectedBaseFilename)
	}

	// Check if ext is correct
	if ext != "mp4" {
		t.Errorf("ext incorrect: got %s, want mp4", ext)
	}

	// Test with custom template
	absPath, baseFilename, ext, err = PrepareDownloadPath(
		tempDir,
		meta,
		"mp4",
		"%(title)s.%(ext)s",
	)
	if err != nil {
		t.Fatalf("PrepareDownloadPath with custom template failed: %v", err)
	}

	// Check if baseFilename is correct with custom template
	expectedBaseFilename = "Test Video"
	if baseFilename != expectedBaseFilename {
		t.Errorf("baseFilename with custom template incorrect: got %s, want %s", baseFilename, expectedBaseFilename)
	}
}

// TestCleanupTempFiles tests cleaning up temporary files
func TestCleanupTempFiles(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "gotube-cleanup-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create some temporary files
	tempFiles := []string{
		filepath.Join(tempDir, "test1.tmp"),
		filepath.Join(tempDir, "test2.part"),
		filepath.Join(tempDir, "test3.ytdl"),
		filepath.Join(tempDir, "test4.mp4"), // Should not be deleted
	}

	for _, file := range tempFiles {
		if err := os.WriteFile(file, []byte("test"), 0644); err != nil {
			t.Fatalf("Failed to create temp file %s: %v", file, err)
		}
	}

	// Clean up temporary files
	err = CleanupTempFiles(tempDir)
	if err != nil {
		t.Fatalf("CleanupTempFiles failed: %v", err)
	}

	// Check if temporary files were deleted
	for i, file := range tempFiles {
		_, err := os.Stat(file)
		if i < 3 {
			// First 3 files should be deleted
			if !os.IsNotExist(err) {
				t.Errorf("Temp file %s should be deleted", file)
			}
		} else {
			// Last file should not be deleted
			if os.IsNotExist(err) {
				t.Errorf("File %s should not be deleted", file)
			}
		}
	}
}

// TestCleanupVideoTempFiles tests cleaning up temporary files for a specific video
func TestCleanupVideoTempFiles(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "gotube-cleanup-video-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create some temporary files
	baseFilename := "Test Video - test-id"
	tempFiles := []string{
		filepath.Join(tempDir, baseFilename+".mp4.part"),
		filepath.Join(tempDir, baseFilename+".f137.mp4.tmp"),
		filepath.Join(tempDir, baseFilename+".ytdl"),
		filepath.Join(tempDir, "other-video.mp4.part"), // Should not be deleted
	}

	for _, file := range tempFiles {
		if err := os.WriteFile(file, []byte("test"), 0644); err != nil {
			t.Fatalf("Failed to create temp file %s: %v", file, err)
		}
	}

	// Clean up temporary files for the specific video
	statusMessages := []string{}
	CleanupVideoTempFiles(tempDir, baseFilename, func(status string) {
		statusMessages = append(statusMessages, status)
	})

	// Check if temporary files were deleted
	for i, file := range tempFiles {
		_, err := os.Stat(file)
		if i < 3 {
			// First 3 files should be deleted
			if !os.IsNotExist(err) {
				t.Errorf("Temp file %s should be deleted", file)
			}
		} else {
			// Last file should not be deleted
			if os.IsNotExist(err) {
				t.Errorf("File %s should not be deleted", file)
			}
		}
	}

	// Check if status callback was called
	if len(statusMessages) == 0 {
		t.Error("Status callback was not called")
	}
}

// TestFindOutputFile tests finding the output file after download
func TestFindOutputFile(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "gotube-find-output-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a test output file
	baseFilename := "Test Video - test-id"
	outputFile := filepath.Join(tempDir, baseFilename+".mp4")
	if err := os.WriteFile(outputFile, []byte("test"), 0644); err != nil {
		t.Fatalf("Failed to create output file %s: %v", outputFile, err)
	}

	// Set file modification time to recent
	now := time.Now()
	if err := os.Chtimes(outputFile, now, now); err != nil {
		t.Fatalf("Failed to set file modification time: %v", err)
	}

	// Find output file
	ctx := context.Background()
	statusMessages := []string{}
	foundFile, err := FindOutputFile(
		ctx,
		tempDir,
		baseFilename,
		"mp4",
		func(status string) {
			statusMessages = append(statusMessages, status)
		},
		100*time.Millisecond,
	)
	if err != nil {
		t.Fatalf("FindOutputFile failed: %v", err)
	}

	// Check if found file is correct
	if foundFile != outputFile {
		t.Errorf("Found file incorrect: got %s, want %s", foundFile, outputFile)
	}

	// Check if status callback was called
	if len(statusMessages) == 0 {
		t.Error("Status callback was not called")
	}
}

// TestFindOutputFileNotFound tests finding the output file when it doesn't exist
func TestFindOutputFileNotFound(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "gotube-find-output-not-found-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Find output file that doesn't exist
	ctx := context.Background()
	_, err = FindOutputFile(
		ctx,
		tempDir,
		"Nonexistent Video",
		"mp4",
		func(status string) {},
		100*time.Millisecond,
	)

	// Check if error is returned
	if err == nil {
		t.Error("FindOutputFile should return error for nonexistent file")
	}
}

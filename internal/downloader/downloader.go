// File: internal/downloader/downloader.go

package downloader

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/command"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/cookie"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/fileutil"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/playlist"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/resources"
)

const (
	// maxRetries is the maximum number of download retry attempts.
	maxRetries = 3
	// retryDelay is the duration to wait between retry attempts.
	retryDelay = 5 * time.Second
	// verificationDelay is the wait time for file verification after download.
	verificationDelay = 2500 * time.Millisecond
)

// Downloader manages YouTube downloads using yt-dlp.
type Downloader struct {
	mutex                 sync.Mutex
	ytDlpPath             string
	ffmpegPath            string
	tempPythonModulesPath string
	sponsorBlockEnabled   bool
	trimStartTime         string
	trimEndTime           string
	filenameTemplate      string
	useBrowserCookies     bool
	browserName           string

	commandExecutor    *command.CommandExecutor
	cookieManager      *cookie.CookieManager
	MetadataProvider   metadata.MetadataProvider
	playlistDownloader *playlist.PlaylistDownloader

	currentCmd  *exec.Cmd
	cancelFunc  context.CancelFunc
	isCancelled bool
}

// NewDownloader initializes a new Downloader instance with required dependencies.
func NewDownloader(db *database.DB) (*Downloader, error) {
	if db == nil {
		return nil, fmt.Errorf("database connection is required")
	}

	ffmpegPath, err := resources.FindFFmpegPath()
	if err != nil {
		fmt.Printf("Warning: failed to find ffmpeg: %v. Some features may be limited.\n", err)
	}

	ytDlpPath, err := resources.ExtractYtDlp()
	if err != nil {
		return nil, fmt.Errorf("failed to extract yt-dlp: %w", err)
	}

	var tempPythonModulesPath string
	if runtime.GOOS == "windows" {
		tempPythonModulesPath, err = resources.ExtractPythonModulesDir()
		if err != nil {
			fmt.Printf("Warning: failed to extract Python modules: %v. Browser cookies may not work.\n", err)
		}
	}

	cmdExecutor := command.NewCommandExecutor(ytDlpPath, ffmpegPath)
	cookieMgr, err := cookie.NewCookieManager(ytDlpPath, db)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize cookie manager: %w", err)
	}

	metaProvider := metadata.NewYtDlpMetadataProvider(ytDlpPath, ffmpegPath, tempPythonModulesPath, cookieMgr)
	downloader := &Downloader{
		ytDlpPath:             ytDlpPath,
		ffmpegPath:            ffmpegPath,
		tempPythonModulesPath: tempPythonModulesPath,
		commandExecutor:       cmdExecutor,
		cookieManager:         cookieMgr,
		MetadataProvider:      metaProvider,
		filenameTemplate:      "%(title)s.%(ext)s",
		browserName:           "chrome",
	}

	downloader.playlistDownloader = playlist.NewPlaylistDownloader(downloader)

	if err := downloader.verifyYtDlp(); err != nil {
		return nil, err
	}

	return downloader, nil
}

// verifyYtDlp ensures yt-dlp is executable and logs its version.
func (d *Downloader) verifyYtDlp() error {
	cmd := exec.Command(d.ytDlpPath, "--version")
	output, err := cmd.CombinedOutput()
	if err != nil {
		if strings.Contains(err.Error(), "permission denied") {
			return fmt.Errorf("permission denied: %v", err)
		}
		if strings.Contains(err.Error(), "no such file") || strings.Contains(err.Error(), "not found") {
			return fmt.Errorf("yt-dlp not found at %s: %v", d.ytDlpPath, err)
		}
		return fmt.Errorf("failed to verify yt-dlp: %v\nOutput: %s", err, string(output))
	}
	fmt.Printf("Using yt-dlp version: %s\n", strings.TrimSpace(string(output)))
	return nil
}

// validateDownloadParams checks if the download input parameters are valid.
func validateDownloadParams(url, format, path string) error {
	if url == "" {
		return fmt.Errorf("download URL cannot be empty")
	}
	if path == "" {
		return fmt.Errorf("download path cannot be empty")
	}
	if format == "" {
		return fmt.Errorf("download format cannot be empty")
	}
	return nil
}

// Download initiates a YouTube video download with progress and status updates.
func (d *Downloader) Download(url, format, quality, path string, progressCb func(float64), statusCb func(string)) (string, error) {
	if err := validateDownloadParams(url, format, path); err != nil {
		return "", err
	}

	progressCb(0.0)
	statusCb(i18n.Get("status_initializing"))

	if err := d.checkIfPlaylist(url, statusCb); err != nil {
		statusCb(fmt.Sprintf("Warning: %v", err))
	}

	ctx, cancel := d.setupCancellation()
	defer d.cleanupCancellation(cancel)

	var lastErr error
	var actualFilePath string
	var videoMeta *metadata.VideoMetadata
	var absPath, baseFilename, ext string

	for attempt := 1; attempt <= maxRetries; attempt++ {
		statusCb(fmt.Sprintf("Attempt %d/%d: Starting download process...", attempt, maxRetries))
		lastErr = nil

		if err := ctx.Err(); err != nil {
			statusCb("Download cancelled before attempt start.")
			return "", err
		}

		// Fetch metadata if not already done
		if videoMeta == nil {
			var err error
			videoMeta, err = d.fetchAndValidateMetadata(ctx, url, attempt, statusCb)
			if err != nil {
				if err == context.Canceled {
					return "", err
				}
				if strings.Contains(err.Error(), "invalid trim settings") {
					return "", err
				}
				lastErr = err
				if shouldRetry, cancelErr := handleRetryOrCancel(ctx, statusCb, attempt); cancelErr != nil {
					return "", cancelErr
				} else if !shouldRetry {
					statusCb("Failed to fetch metadata after multiple retries.")
					return "", lastErr
				}
				continue
			}
		}

		// Prepare download path if not already done
		if absPath == "" {
			var err error
			absPath, baseFilename, ext, err = d.prepareDownloadPath(path, videoMeta, format, statusCb)
			if err != nil {
				return "", err
			}
		}

		// Execute download
		statusCb(i18n.Get("status_executing_command"))
		settings := d.getDownloadSettings()
		cookieArgs, err := d.getCookieArguments(settings.useBrowserCookies, settings.browserName, statusCb)
		if err != nil {
			return "", fmt.Errorf("cookie handling error: %w", err)
		}

		// Set up cleanup for temporary cookie files if needed
		var tempCookieFile string
		if len(cookieArgs) >= 2 && cookieArgs[0] == "--cookies" {
			tempCookieFile = cookieArgs[1]
		}
		defer func() {
			if tempCookieFile != "" {
				os.Remove(tempCookieFile)
			}
		}()

		_, execErr := d.buildAndExecuteCommand(ctx, url, format, quality, absPath, videoMeta, settings, cookieArgs, progressCb, statusCb)
		if execErr != nil {
			if execErr == context.Canceled || strings.Contains(execErr.Error(), "context canceled") || strings.Contains(execErr.Error(), "signal: killed") {
				statusCb("Download was cancelled during execution.")
				fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
				return "", context.Canceled
			}

			isPotentialCookieOrPathError := strings.Contains(execErr.Error(), "Error opening output files: Invalid argument") ||
				strings.Contains(execErr.Error(), "Browser cookie error")

			if attempt == 1 && settings.useBrowserCookies && isPotentialCookieOrPathError {
				statusCb("Initial attempt failed, potentially due to browser cookie/path issue. Retrying without browser cookies...")
				d.mutex.Lock()
				d.useBrowserCookies = false
				d.mutex.Unlock()
				fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
				continue
			}

			lastErr = fmt.Errorf("attempt %d: download command failed: %w", attempt, execErr)
			statusCb(fmt.Sprintf("Download command failed on attempt %d: %v", attempt, execErr))
			if shouldRetry, cancelErr := handleRetryOrCancel(ctx, statusCb, attempt); cancelErr != nil {
				fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
				return "", cancelErr
			} else if !shouldRetry {
				statusCb("Download command failed after multiple retries.")
				fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
				break
			}
			fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
			continue
		}

		// Verify downloaded file
		statusCb(i18n.Get("status_verifying_file"))
		actualFilePath, err = fileutil.FindOutputFile(ctx, absPath, baseFilename, ext, statusCb, verificationDelay)
		if err != nil {
			if err == context.Canceled || strings.Contains(err.Error(), "context canceled") {
				statusCb("Download was cancelled during file verification.")
				fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
				return "", context.Canceled
			}
			lastErr = fmt.Errorf("attempt %d: output file verification failed: %w", attempt, err)
			statusCb(fmt.Sprintf("Output file verification failed on attempt %d: %v", attempt, err))
			if shouldRetry, cancelErr := handleRetryOrCancel(ctx, statusCb, attempt); cancelErr != nil {
				fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
				return "", cancelErr
			} else if !shouldRetry {
				statusCb("Output file verification failed after multiple retries.")
				fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
				break
			}
			fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
			continue
		}

		// Success case
		statusCb(i18n.Get("status_cleaning_up"))
		fileutil.CleanupVideoTempFiles(absPath, baseFilename, statusCb)
		statusCb(fmt.Sprintf(i18n.Get("status_download_complete"), filepath.Base(actualFilePath)))
		progressCb(1.0)
		return actualFilePath, nil
	}

	return "", handleDownloadFailure(lastErr, absPath, baseFilename, statusCb, progressCb)
}

// DownloadPlaylistVideos downloads multiple videos from a playlist.
func (d *Downloader) DownloadPlaylistVideos(
	videos []metadata.VideoMetadata,
	format, quality, outputPath string,
	progressCb func(int, float64),
	statusCb func(int, string),
) error {
	return d.playlistDownloader.DownloadPlaylistVideos(videos, format, quality, outputPath, progressCb, statusCb)
}

// GetFormats fetches available formats for a video URL
func (d *Downloader) GetFormats(url string) ([]metadata.Format, error) {
	return d.MetadataProvider.GetFormats(url)
}

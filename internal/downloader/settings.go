// File: internal/downloader/settings.go

package downloader

import (
	"fmt"
	"path/filepath"
	"strings"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/cookie"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/timeutil"
)

// DownloadSettings holds the settings for a download operation
type DownloadSettings struct {
	sponsorBlockEnabled bool
	trimStartTime       string
	trimEndTime         string
	filenameTemplate    string
	useBrowserCookies   bool
	browserName         string
	ffmpegPath          string
	tempPythonPath      string
}

// getDownloadSettings retrieves the current download settings
func (d *Downloader) getDownloadSettings() DownloadSettings {
	// Lock to safely access settings
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// Create a copy of all settings
	return DownloadSettings{
		sponsorBlockEnabled: d.sponsorBlockEnabled,
		trimStartTime:       d.trimStartTime,
		trimEndTime:         d.trimEndTime,
		filenameTemplate:    d.filenameTemplate,
		useBrowserCookies:   d.useBrowserCookies,
		browserName:         d.browserName,
		ffmpegPath:          d.ffmpegPath,
		tempPythonPath:      d.tempPythonModulesPath,
	}
}

// SetSponsorBlock enables or disables SponsorBlock integration
func (d *Downloader) SetSponsorBlock(enabled bool) {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	d.sponsorBlockEnabled = enabled
	status := "disabled"
	if enabled {
		status = "enabled"
	}
	fmt.Printf("SponsorBlock integration %s.\n", status)
}

// SetFilenameTemplate sets the custom filename template
func (d *Downloader) SetFilenameTemplate(template string) {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	// Basic validation: ensure template contains extension placeholder if not empty
	if template != "" && !strings.Contains(template, "%(ext)s") {
		// Append ".%(ext)s" intelligently
		base := strings.TrimSuffix(template, filepath.Ext(template))
		template = base + ".%(ext)s"
	}
	d.filenameTemplate = template
	fmt.Printf("Filename template set to: %s\n", template)
}

// SetTrimTimes sets the trim start and end times for video trimming
func (d *Downloader) SetTrimTimes(startTime, endTime string) error {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// If both are empty, just clear the trim times
	if startTime == "" && endTime == "" {
		d.trimStartTime = ""
		d.trimEndTime = ""
		fmt.Println("Trim times cleared.")
		return nil
	}

	// Validate the time format
	if startTime != "" {
		if !timeutil.ValidateTimeFormat(startTime) {
			return fmt.Errorf("invalid start time format: %s (use MM:SS format)", startTime)
		}
	}
	if endTime != "" {
		if !timeutil.ValidateTimeFormat(endTime) {
			return fmt.Errorf("invalid end time format: %s (use MM:SS format)", endTime)
		}
	}

	// Check if both start and end times are provided, validate their relationship
	if startTime != "" && endTime != "" {
		startSec, _ := timeutil.ParseTimeToSeconds(startTime)
		endSec, _ := timeutil.ParseTimeToSeconds(endTime)
		if endSec <= startSec {
			return fmt.Errorf("end time '%s' must be after start time '%s'", endTime, startTime)
		}
	}

	// Set the trim times
	d.trimStartTime = startTime
	d.trimEndTime = endTime
	fmt.Printf("Trim times set: start=%s, end=%s\n", startTime, endTime)
	return nil
}

// SetBrowserName sets the browser name for cookie extraction
func (d *Downloader) SetBrowserName(name string) {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	d.browserName = name
	fmt.Printf("Browser for cookie extraction set to: %s\n", name)
}

// GetBrowserName returns the current browser name
func (d *Downloader) GetBrowserName() string {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	return d.browserName
}

// SetUseBrowserCookies enables or disables browser cookie usage
func (d *Downloader) SetUseBrowserCookies(enabled bool) {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	d.useBrowserCookies = enabled
	status := "disabled"
	if enabled {
		status = "enabled"
	}
	fmt.Printf("Browser cookie usage %s.\n", status)
}

// GetCookieManager returns the cookie manager
func (d *Downloader) GetCookieManager() *cookie.CookieManager {
	return d.cookieManager
}

// SetCustomCookies sets custom cookies from a file
func (d *Downloader) SetCustomCookies(path string) error {
	return d.cookieManager.SetCustomCookies(path)
}

// IsPlaylist checks if a URL is a playlist
func (d *Downloader) IsPlaylist(url string) (bool, error) {
	return d.MetadataProvider.IsPlaylist(url)
}

// GetPlaylistMetadata fetches metadata for a playlist
func (d *Downloader) GetPlaylistMetadata(url string) (*metadata.PlaylistMetadata, error) {
	return d.MetadataProvider.GetPlaylistMetadata(url)
}

// File: internal/resources/resources_linux.go

//go:build linux && !noembed

package resources

import (
	"fmt"
	"os/exec"
	// "strings" // No longer needed here
)

// init registers the platform-specific implementations.
func init() {
	findFFmpegPathImpl = findFFmpegPathLinux
	isSecretStorageInstalledImpl = isSecretStorageInstalledLinux
	// Assign to the internal (lowercase) function variables
	installSystemPythonModuleImpl = installSystemPythonModuleLinux   // <<< Corrected
	extractPythonModulesDirImpl = extractPythonModulesDirUnsupported // <<< Corrected
}

// findFFmpegPathLinux looks for ffmpeg in the system PATH.
func findFFmpegPathLinux() (string, error) {
	fmt.Println("Searching for system ffmpeg on Linux...")
	path, err := exec.LookPath("ffmpeg")
	if err != nil {
		return "", fmt.Errorf("ffmpeg not found in system PATH: %w. Please install ffmpeg", err)
	}
	fmt.Println("Found system ffmpeg at:", path)
	return path, nil
}

// isSecretStorageInstalledLinux checks the system python3 environment.
func isSecretStorageInstalledLinux() bool {
	fmt.Println("Checking for system 'secretstorage' module (Linux)...")
	cmd := exec.Command("python3", "-c", "import secretstorage")
	err := cmd.Run()
	if err == nil {
		fmt.Println("'secretstorage' found.")
		return true
	}
	fmt.Println("'secretstorage' not found.")
	return false
}

// installSystemPythonModuleLinux installs a module using pip3.
func installSystemPythonModuleLinux(moduleName string) (string, error) {
	fmt.Printf("Attempting to install '%s' using pip3...\n", moduleName)
	// Ensure --user flag is used to avoid needing root
	cmd := exec.Command("python3", "-m", "pip", "install", "--user", moduleName)
	output, err := cmd.CombinedOutput()
	// Check error type? ExitError?
	if err != nil {
		fmt.Printf("Failed to install '%s': %v\nOutput:\n%s\n", moduleName, err, string(output))
		return string(output), fmt.Errorf("pip install failed: %w", err)
	}
	fmt.Printf("Successfully installed '%s'.\nOutput:\n%s\n", moduleName, string(output))
	return string(output), nil
}

// extractPythonModulesDirUnsupported returns an error on Linux.
func extractPythonModulesDirUnsupported() (string, error) {
	return "", ErrNotEmbedded // Use defined error
}

// File: internal/resources/resources_noembed.go

//go:build noembed

package resources

import (
	"fmt"
	"os/exec"
	"runtime"
)

// init registers the platform-specific implementations for non-embedding builds.
func init() {
	findFFmpegPathImpl = findFFmpegPathNoEmbed
	isSecretStorageInstalledImpl = isSecretStorageInstalledNoEmbed   // Could still check system python
	installSystemPythonModuleImpl = installSystemPythonModuleNoEmbed // Allow installing to system python
	extractPythonModulesDirImpl = extractPythonModulesDirNoEmbed
}

// ExtractYtDlp returns the path to yt-dlp expected in the system PATH.
func ExtractYtDlp() (string, error) {
	binaryName := "yt-dlp"
	if runtime.GOOS == "windows" {
		binaryName += ".exe"
	}
	fmt.Printf("Non-embedded build: Looking for '%s' in PATH...\n", binaryName)
	path, err := exec.LookPath(binaryName)
	if err != nil {
		return "", fmt.Errorf("'%s' not found in system PATH. Please install yt-dlp", binaryName)
	}
	fmt.Printf("Found '%s' at: %s\n", binaryName, path)
	return path, nil
}

// findFFmpegPathNoEmbed looks for ffmpeg in the system PATH.
func findFFmpegPathNoEmbed() (string, error) {
	fmt.Println("Non-embedded build: Looking for 'ffmpeg' in PATH...")
	path, err := exec.LookPath("ffmpeg")
	if err != nil {
		return "", fmt.Errorf("ffmpeg not found in system PATH. Please install ffmpeg")
	}
	fmt.Println("Found 'ffmpeg' at:", path)
	return path, nil
}

// isSecretStorageInstalledNoEmbed checks the system python environment.
func isSecretStorageInstalledNoEmbed() bool {
	// This check remains relevant even without embedding, as yt-dlp will use system python
	if runtime.GOOS == "windows" {
		// Assume not available easily without embedding the interpreter + modules
		fmt.Println("Non-embedded build: Assuming 'secretstorage' check not applicable/feasible on Windows.")
		return false
	}
	fmt.Println("Non-embedded build: Checking for system 'secretstorage' module...")
	cmd := exec.Command("python3", "-c", "import secretstorage")
	err := cmd.Run()
	if err == nil {
		fmt.Println("'secretstorage' found.")
		return true
	}
	fmt.Println("'secretstorage' not found.")
	return false
}

// installSystemPythonModuleNoEmbed installs a module using pip3 for the system python.
func installSystemPythonModuleNoEmbed(moduleName string) (string, error) {
	if runtime.GOOS == "windows" {
		return "", fmt.Errorf("automatic system python module installation is not supported on Windows")
	}
	fmt.Printf("Non-embedded build: Attempting to install '%s' using pip3...\n", moduleName)
	cmd := exec.Command("python3", "-m", "pip", "install", "--user", moduleName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Failed to install '%s': %v\nOutput:\n%s\n", moduleName, err, string(output))
		return string(output), fmt.Errorf("pip install failed: %w", err)
	}
	fmt.Printf("Successfully installed '%s'.\nOutput:\n%s\n", moduleName, string(output))
	return string(output), nil
}

// extractPythonModulesDirNoEmbed returns an error as modules are not embedded.
func extractPythonModulesDirNoEmbed() (string, error) {
	return "", fmt.Errorf("python modules are not embedded in this build (--no-embed)")
}

// GetAsset needs to be handled. If assets are still embedded unconditionally,
// the original implementation in resources.go is fine. If assets should also
// be excluded with --no-embed, you'd need a different approach (e.g., loading
// from disk relative to the executable). Let's assume assets are always embedded for now.
/*
func GetAsset(name string) ([]byte, error) {
	// If assets are NOT embedded with --no-embed, implement loading from disk here
	// return nil, fmt.Errorf("assets not embedded in this build (--no-embed)")

	// If assets ARE still embedded, the original code in resources.go works,
	// but we need to avoid a duplicate function definition.
	// This function might be removed from the noembed file if resources.go handles it.
}
*/

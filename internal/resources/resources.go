// File: internal/resources/resources.go

//go:build !noembed

package resources

import (
	"embed"
	"errors" // Import errors package
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings" // Import strings
	"sync"
	// "os/signal"
	// "syscall"
)

//go:embed bin/yt-dlp bin/yt-dlp.exe
var ytDlpBinaries embed.FS // yt-dlp embedded unconditionally for all platforms

//go:embed assets/*
var assets embed.FS // Regular assets embedded unconditionally

// tempDir stores the path to the temporary directory created for extracted resources.
var tempDir string
var tempDirErr error
var initOnce sync.Once       // Used to ensure temp dir creation happens only once.
var activeTempFiles sync.Map // Track extracted files for potential cleanup

// ErrNotEmbedded indicates a resource is not embedded for the current platform.
var ErrNotEmbedded = errors.New("resource not embedded for this platform")

// initializeTempDir creates a temporary directory for extracted binaries.
func initializeTempDir() {
	initOnce.Do(func() {
		tempDir, tempDirErr = os.MkdirTemp("", "gotube-res-*") // Shorter prefix
		if tempDirErr != nil {
			fmt.Printf("CRITICAL: Failed to create temp directory for resources: %v\n", tempDirErr)
			cwd, err := os.Getwd()
			if err == nil {
				fallbackDir := filepath.Join(cwd, ".gotube_temp_res")
				if err := os.MkdirAll(fallbackDir, 0700); err == nil {
					tempDir = fallbackDir
					tempDirErr = nil
					fmt.Printf("WARNING: Using fallback temp directory: %s\n", tempDir)
				} else {
					tempDir = "."
					fmt.Printf("WARNING: Failed to create fallback temp directory. Using current directory '.'. This may leave temporary files.\n")
				}
			} else {
				tempDir = "."
				fmt.Printf("WARNING: Cannot get CWD for fallback temp dir. Using current directory '.'. This may leave temporary files.\n")
			}
		}
		fmt.Printf("Using temp directory for extracted resources: %s\n", tempDir)
	})
}

// CleanupResources removes the temporary directory and its contents.
func CleanupResources() {
	initializeTempDir() // Ensure tempDir is set
	if tempDir != "" && tempDir != "." {
		fmt.Printf("Cleaning up temporary resource directory: %s\n", tempDir)
		err := os.RemoveAll(tempDir)
		if err != nil {
			fmt.Printf("Warning: Failed to remove temporary resource directory %s: %v\n", tempDir, err)
		}
	}
	// Also remove individually tracked files (might be outside tempDir if fallback failed)
	activeTempFiles.Range(func(key, value interface{}) bool {
		path := key.(string)
		fmt.Printf("Removing tracked temp file: %s\n", path)
		os.Remove(path)
		activeTempFiles.Delete(path)
		return true
	})
}

// GetTempDir returns the path to the temporary directory, initializing it if needed.
func GetTempDir() (string, error) {
	initializeTempDir()
	return tempDir, tempDirErr
}

// extractFile extracts a specific file from an embed.FS to the temp directory.
func extractFile(fs embed.FS, embeddedPath, destFileName string) (string, error) {
	tmpDir, err := GetTempDir()
	if err != nil {
		return "", fmt.Errorf("cannot get temp directory: %w", err)
	}
	data, err := fs.ReadFile(embeddedPath)
	if err != nil {
		return "", fmt.Errorf("failed to read embedded file %s: %w", embeddedPath, err)
	}
	destPath := filepath.Join(tmpDir, destFileName)
	perm := os.FileMode(0644)
	ext := strings.ToLower(filepath.Ext(destFileName))
	if ext == "" || ext == ".exe" || ext == ".bin" || ext == ".sh" {
		perm = 0755
	}
	if err := os.WriteFile(destPath, data, perm); err != nil {
		_ = os.Remove(destPath)
		return "", fmt.Errorf("failed to write file to %s: %w", destPath, err)
	}
	activeTempFiles.Store(destPath, true)
	return destPath, nil
}

// extractDirRecursive helper function to extract embedded directories.
// Stays in this file as it's used by the windows-specific code.
func extractEmbedDirRecursive(fs embed.FS, embeddedDir, destDir string) ([]string, error) {
	var extractedPaths []string
	err := os.MkdirAll(destDir, 0755)
	if err != nil {
		return nil, fmt.Errorf("failed to create target dir %s: %w", destDir, err)
	}

	entries, err := fs.ReadDir(embeddedDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read embedded dir %s: %w", embeddedDir, err)
	}

	for _, entry := range entries {
		embeddedPath := filepath.Join(embeddedDir, entry.Name())
		destPath := filepath.Join(destDir, entry.Name())

		if entry.IsDir() {
			subPaths, err := extractEmbedDirRecursive(fs, embeddedPath, destPath)
			if err != nil {
				return extractedPaths, err
			}
			extractedPaths = append(extractedPaths, subPaths...)
		} else {
			data, err := fs.ReadFile(embeddedPath)
			if err != nil {
				return extractedPaths, fmt.Errorf("failed to read embedded file %s: %w", embeddedPath, err)
			}

			perm := os.FileMode(0644)
			// Corrected staticcheck warning S1002
			if !strings.Contains(entry.Name(), ".") { // Simplified check
				perm = 0755
			}
			err = os.WriteFile(destPath, data, perm)
			if err != nil {
				return extractedPaths, fmt.Errorf("failed to write file %s: %w", destPath, err)
			}
			extractedPaths = append(extractedPaths, destPath)
		}
	}
	return extractedPaths, nil
}

// ExtractYtDlp extracts the appropriate yt-dlp binary for the current OS.
func ExtractYtDlp() (string, error) {
	binaryName := "yt-dlp"
	embeddedPath := "bin/" + binaryName
	if runtime.GOOS == "windows" {
		binaryName += ".exe"
		embeddedPath += ".exe"
	}
	return extractFile(ytDlpBinaries, embeddedPath, binaryName)
}

// GetAsset returns a file from the unconditionally embedded assets FS.
func GetAsset(name string) ([]byte, error) {
	pathInFs := "assets/" + filepath.ToSlash(name)
	return assets.ReadFile(pathInFs)
}

// FindFFmpegPath attempts to find a usable ffmpeg executable.
func FindFFmpegPath() (string, error) {
	// Check if the platform-specific implementation was assigned
	if findFFmpegPathImpl == nil {
		return "", fmt.Errorf("FindFFmpegPath implementation missing for OS %s", runtime.GOOS)
	}
	return findFFmpegPathImpl()
}

// IsSecretStorageInstalled checks if the secretstorage python module is available.
func IsSecretStorageInstalled() bool {
	// Check if the platform-specific implementation was assigned
	if isSecretStorageInstalledImpl == nil {
		fmt.Printf("Warning: IsSecretStorageInstalled implementation missing for OS %s. Returning false.\n", runtime.GOOS)
		return false
	}
	return isSecretStorageInstalledImpl()
}

// InstallSystemPythonModule installs a module using pip.
func InstallSystemPythonModule(moduleName string) (string, error) {
	// Check if the platform-specific implementation exists *before* calling it
	if installSystemPythonModuleImpl == nil { // <<< Corrected check
		return "", fmt.Errorf("InstallSystemPythonModule not implemented for %s", runtime.GOOS)
	}
	return installSystemPythonModuleImpl(moduleName)
}

// ExtractPythonModulesDir extracts the embedded python modules directory.
func ExtractPythonModulesDir() (string, error) {
	// Check if the platform-specific implementation exists *before* calling it
	if extractPythonModulesDirImpl == nil { // <<< Corrected check
		return "", fmt.Errorf("ExtractPythonModulesDir not implemented for %s", runtime.GOOS)
	}
	return extractPythonModulesDirImpl()
}

// ---- Stubs for platform-specific functions ----
// These are assigned values by platform-specific init() functions
var findFFmpegPathImpl func() (string, error)
var isSecretStorageInstalledImpl func() bool
var installSystemPythonModuleImpl func(moduleName string) (string, error)
var extractPythonModulesDirImpl func() (string, error)

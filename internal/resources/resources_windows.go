// File: internal/resources/resources_windows.go

//go:build windows && !noembed

package resources

import (
	"embed"
	"fmt"
	"os" // Required for running python check (though currently unused)
	"path/filepath"
)

// Embed ffmpeg.exe and python modules ONLY for windows builds.
//
//go:embed bin/ffmpeg.exe
var windowsFFmpegBinary embed.FS

//go:embed bin/python_modules/*
var windowsPythonModules embed.FS // Embed the whole directory

var tempPythonModulesDir string // Cache the path once extracted

// init registers the platform-specific implementations.
func init() {
	findFFmpegPathImpl = findFFmpegPathWindows
	isSecretStorageInstalledImpl = isSecretStorageInstalledWindows
	// Assign to the internal (lowercase) function variables
	installSystemPythonModuleImpl = installSystemPythonModuleUnsupported // <<< Corrected
	extractPythonModulesDirImpl = extractPythonModulesDirWindows         // <<< Corrected
}

// findFFmpegPathWindows extracts the embedded ffmpeg.exe to a temp location.
func findFFmpegPathWindows() (string, error) {
	fmt.Println("Attempting to extract embedded ffmpeg.exe for Windows...")
	// Use extractFile which registers for cleanup
	path, err := extractFile(windowsFFmpegBinary, "bin/ffmpeg.exe", "ffmpeg.exe")
	if err != nil {
		return "", fmt.Errorf("failed to extract embedded ffmpeg.exe: %w", err)
	}
	fmt.Println("Embedded ffmpeg.exe extracted to:", path)
	return path, nil
}

// isSecretStorageInstalledWindows simply returns true, as we embed the modules.
// The actual check if they *work* depends on the yt-dlp execution environment setup.
func isSecretStorageInstalledWindows() bool {
	// If we wanted to be more thorough, we could try extracting python modules
	// and running a python check here, but let's assume true for now.
	// This check is mainly for the GUI prompt logic on Linux.
	return true
}

// installSystemPythonModuleUnsupported returns an error on Windows.
func installSystemPythonModuleUnsupported(moduleName string) (string, error) {
	return "", fmt.Errorf("automatic system python module installation is not supported on Windows")
}

// extractPythonModulesDirWindows extracts the embedded python_modules directory.
func extractPythonModulesDirWindows() (string, error) {
	// If already extracted, return cached path
	if tempPythonModulesDir != "" {
		if _, err := os.Stat(tempPythonModulesDir); err == nil {
			return tempPythonModulesDir, nil
		}
		// Cached path doesn't exist anymore, clear it
		tempPythonModulesDir = ""
	}

	tmpDir, err := GetTempDir()
	if err != nil {
		return "", fmt.Errorf("cannot get temp directory for python modules: %w", err)
	}

	modulesTargetDir := filepath.Join(tmpDir, "python_modules")
	embeddedBaseDir := "bin/python_modules" // Base directory in embed.FS

	fmt.Println("Extracting embedded python modules to:", modulesTargetDir)

	// Use the recursive helper, it returns list of files but we only need the base dir path
	extractedFiles, err := extractEmbedDirRecursive(windowsPythonModules, embeddedBaseDir, modulesTargetDir)
	if err != nil {
		// Attempt cleanup on partial extraction failure
		_ = os.RemoveAll(modulesTargetDir)
		return "", fmt.Errorf("failed to extract python modules: %w", err)
	}

	// Track extracted files for cleanup (less granular than extractFile)
	for _, f := range extractedFiles {
		activeTempFiles.Store(f, true)
	}
	// Also track the base directory itself? Maybe not needed if files are tracked.

	fmt.Printf("Python modules extraction complete (%d files/dirs).\n", len(extractedFiles))
	tempPythonModulesDir = modulesTargetDir // Cache the path
	return modulesTargetDir, nil
}

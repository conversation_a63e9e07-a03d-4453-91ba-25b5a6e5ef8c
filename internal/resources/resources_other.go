// File: internal/resources/resources_other.go

//go:build !windows && !linux && !noembed

package resources

import (
	"fmt"
	"os/exec"
	"runtime"
	// "strings" // No longer needed here
)

// init registers the platform-specific implementations for non-windows, non-linux OS.
func init() {
	findFFmpegPathImpl = findFFmpegPathOther
	isSecretStorageInstalledImpl = isSecretStorageInstalledOther
	// Assign to the internal (lowercase) function variables
	installSystemPythonModuleImpl = installSystemPythonModuleOther   // <<< Corrected
	extractPythonModulesDirImpl = extractPythonModulesDirUnsupported // <<< Corrected
}

// findFFmpegPathOther looks for ffmpeg in the system PATH.
func findFFmpegPathOther() (string, error) {
	osName := runtime.GOOS
	fmt.Printf("Searching for system ffmpeg on %s...\n", osName)
	path, err := exec.LookPath("ffmpeg")
	if err != nil {
		return "", fmt.Errorf("ffmpeg not found in system PATH on %s: %w. Please install ffmpeg", osName, err)
	}
	fmt.Printf("Found system ffmpeg at: %s\n", path)
	return path, nil
}

// isSecretStorageInstalledOther checks the system python3 environment.
func isSecretStorageInstalledOther() bool {
	osName := runtime.GOOS
	fmt.Printf("Checking for system 'secretstorage' module (%s)...\n", osName)
	// Use python3 consistently
	cmd := exec.Command("python3", "-c", "import secretstorage")
	err := cmd.Run()
	if err == nil {
		fmt.Println("'secretstorage' found.")
		return true
	}
	fmt.Println("'secretstorage' not found.")
	return false
}

// installSystemPythonModuleOther installs a module using pip3.
func installSystemPythonModuleOther(moduleName string) (string, error) {
	osName := runtime.GOOS
	fmt.Printf("Attempting to install '%s' using pip3 on %s...\n", moduleName, osName)
	// Ensure --user flag is used
	cmd := exec.Command("python3", "-m", "pip", "install", "--user", moduleName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Failed to install '%s': %v\nOutput:\n%s\n", moduleName, err, string(output))
		return string(output), fmt.Errorf("pip install failed: %w", err)
	}
	fmt.Printf("Successfully installed '%s'.\nOutput:\n%s\n", moduleName, string(output))
	return string(output), nil
}

// extractPythonModulesDirUnsupported returns an error on non-windows platforms.
func extractPythonModulesDirUnsupported() (string, error) {
	return "", ErrNotEmbedded // Use defined error
}

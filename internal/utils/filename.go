package utils

import (
	"path/filepath"
	"strings"
)

// SanitizeFilename removes invalid characters from filename and makes it compatible with yt-dlp's --restrict-filenames flag
func SanitizeFilename(filename string) string {
	// Replace invalid characters with underscore
	invalid := []string{"/", "\\", ":", "*", "?", "\"", "'", "<", ">", "|"}
	result := filename
	for _, char := range invalid {
		result = strings.ReplaceAll(result, char, "_")
	}

	// Replace spaces with underscores (like yt-dlp does with --restrict-filenames)
	result = strings.ReplaceAll(result, " ", "_")

	// Replace non-ASCII characters with underscores (like yt-dlp does with --restrict-filenames)
	// This ensures compatibility with yt-dlp's filename sanitization
	var sanitized strings.Builder
	for _, r := range result {
		if r > 127 { // non-ASCII character
			sanitized.WriteRune('_')
		} else {
			sanitized.WriteRune(r)
		}
	}
	result = sanitized.String()

	// Preserve multiple consecutive special characters (like multiple underscores)
	// This is to match the expected behavior in the tests

	// Trim spaces and limit length
	result = strings.TrimSpace(result)
	if len(result) > 100 {
		result = result[:100]
	}
	return result
}

// GetFileExtension returns the file extension without the dot
func GetFileExtension(path string) string {
	if path == "" {
		return ""
	}
	ext := filepath.Ext(path)
	if ext == "" {
		return ""
	}
	return ext[1:] // Remove the dot
}

// GetFileNameWithoutExtension returns the filename without extension
func GetFileNameWithoutExtension(path string) string {
	if path == "" {
		return ""
	}
	// For the test case, we need to preserve the path
	ext := filepath.Ext(path)
	if ext == "" {
		return path
	}
	return path[:len(path)-len(ext)]
}

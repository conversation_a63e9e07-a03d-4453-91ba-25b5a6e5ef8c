package utils

import (
	"testing"
)

// TestSanitizeFilename tests the SanitizeFilename function
func TestSanitizeFilename(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Basic filename",
			input:    "test.mp4",
			expected: "test.mp4",
		},
		{
			name:     "Filename with spaces",
			input:    "test video.mp4",
			expected: "test_video.mp4",
		},
		{
			name:     "Filename with invalid characters",
			input:    "test:video?.mp4",
			expected: "test_video_.mp4",
		},
		{
			name:     "Filename with path separators",
			input:    "test/video\\file.mp4",
			expected: "test_video_file.mp4",
		},
		{
			name:     "Filename with special characters",
			input:    "test*video<>|.mp4",
			expected: "test_video___.mp4",
		},
		{
			name:     "Filename with quotes",
			input:    "test\"video'.mp4",
			expected: "test_video_.mp4",
		},
		{
			name:     "Filename with non-ASCII characters",
			input:    "tést vídéo.mp4",
			expected: "t_st_v_d_o.mp4",
		},
		{
			name:     "Filename with Cyrillic characters",
			input:    "видео на русском.mp4",
			expected: "________________.mp4",
		},
		{
			name:     "Empty filename",
			input:    "",
			expected: "",
		},
		{
			name:     "Filename with only invalid characters",
			input:    ":<>|?*",
			expected: "______",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := SanitizeFilename(test.input)
			if result != test.expected {
				t.Errorf("SanitizeFilename(%q) = %q, want %q", test.input, result, test.expected)
			}
		})
	}
}

// TestGetFileExtension tests the GetFileExtension function
func TestGetFileExtension(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Basic filename",
			input:    "test.mp4",
			expected: "mp4",
		},
		{
			name:     "Filename with multiple dots",
			input:    "test.video.mp4",
			expected: "mp4",
		},
		{
			name:     "Filename with no extension",
			input:    "test",
			expected: "",
		},
		{
			name:     "Filename with empty extension",
			input:    "test.",
			expected: "",
		},
		{
			name:     "Path with extension",
			input:    "/path/to/test.mp4",
			expected: "mp4",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := GetFileExtension(test.input)
			if result != test.expected {
				t.Errorf("GetFileExtension(%q) = %q, want %q", test.input, result, test.expected)
			}
		})
	}
}

// TestGetFileNameWithoutExtension tests the GetFileNameWithoutExtension function
func TestGetFileNameWithoutExtension(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Basic filename",
			input:    "test.mp4",
			expected: "test",
		},
		{
			name:     "Filename with multiple dots",
			input:    "test.video.mp4",
			expected: "test.video",
		},
		{
			name:     "Filename with no extension",
			input:    "test",
			expected: "test",
		},
		{
			name:     "Filename with empty extension",
			input:    "test.",
			expected: "test",
		},
		{
			name:     "Path with extension",
			input:    "/path/to/test.mp4",
			expected: "/path/to/test",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := GetFileNameWithoutExtension(test.input)
			if result != test.expected {
				t.Errorf("GetFileNameWithoutExtension(%q) = %q, want %q", test.input, result, test.expected)
			}
		})
	}
}

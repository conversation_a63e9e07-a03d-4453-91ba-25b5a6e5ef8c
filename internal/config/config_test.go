package config

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
)

// MockDB is a mock implementation of the database
type MockDB struct {
	configs map[string]string
}

// NewMockDB creates a new mock database
func NewMockDB() *MockDB {
	return &MockDB{
		configs: make(map[string]string),
	}
}

// GetConfig returns a mock config value
func (m *MockDB) GetConfig(key string) (string, error) {
	if value, ok := m.configs[key]; ok {
		return value, nil
	}
	return "", nil
}

// SetConfig sets a mock config value
func (m *MockDB) SetConfig(key, value string, encrypt bool) error {
	m.configs[key] = value
	return nil
}

// GetInterface returns the mock DB as an interface
func (m *MockDB) GetInterface() any {
	return m
}

// createTestDB creates a temporary database for testing
func createTestDB(t *testing.T) (*database.DB, string, func()) {
	t.Helper()

	// Create a temporary directory for the test database
	tempDir, err := os.MkdirTemp("", "gotube-config-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	// Create a test database path
	dbPath := filepath.Join(tempDir, "test.db")

	// Create a test logger
	testLogger, _ := logger.NewLogger(filepath.Join(tempDir, "test.log"))

	// Create a test database
	db, err := database.NewDB(dbPath, "test-password", testLogger)
	if err != nil {
		os.RemoveAll(tempDir)
		t.Fatalf("Failed to create test database: %v", err)
	}

	// Return cleanup function
	cleanup := func() {
		db.Close()
		os.RemoveAll(tempDir)
	}

	return db, dbPath, cleanup
}

// TestConfigManagerCreation tests creating a config manager
func TestConfigManagerCreation(t *testing.T) {
	// Create a test database
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Create a config manager
	configDir, err := os.MkdirTemp("", "gotube-config-dir-*")
	if err != nil {
		t.Fatalf("Failed to create temp config directory: %v", err)
	}
	defer os.RemoveAll(configDir)

	manager, err := NewManager(db)
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Check if manager was created correctly
	if manager == nil {
		t.Fatal("Config manager should not be nil")
	}
}

// TestConfigDefaults tests default configuration values
func TestConfigDefaults(t *testing.T) {
	// Create a test database
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Create a config manager
	configDir, err := os.MkdirTemp("", "gotube-config-dir-*")
	if err != nil {
		t.Fatalf("Failed to create temp config directory: %v", err)
	}
	defer os.RemoveAll(configDir)

	manager, err := NewManager(db)
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Load configuration (should use defaults for new database)
	err = manager.Load()
	if err != nil {
		t.Fatalf("Failed to load configuration: %v", err)
	}

	// Check default download path
	downloadPath := manager.GetDownloadPath()
	if downloadPath == "" {
		t.Error("Default download path should not be empty")
	}

	// Check default language
	language := manager.GetLanguage()
	if language == "" {
		t.Error("Default language should not be empty")
	}

	// Check default theme
	theme := manager.GetTheme()
	if theme == "" {
		t.Error("Default theme should not be empty")
	}
}

// TestConfigSettersAndGetters tests setting and getting configuration values
func TestConfigSettersAndGetters(t *testing.T) {
	// We don't need a mock database for this test

	// Create a config manager with mock database
	manager := &Manager{
		config: Config{
			DownloadPath:     "",
			Language:         "",
			Theme:            "",
			FilenameTemplate: "",
			CustomCookiePath: "",
			StringValues:     make(map[string]string),
			BoolValues:       make(map[string]bool),
		},
		configDir: "",
		db:        nil, // We don't need a real DB for this test
		loaded:    true,
	}

	// Test SetDownloadPath and GetDownloadPath
	testPath := "/test/download/path"
	manager.SetDownloadPath(testPath)
	if manager.GetDownloadPath() != testPath {
		t.Errorf("Download path not set correctly: got %s, want %s", manager.GetDownloadPath(), testPath)
	}

	// Test SetLanguage and GetLanguage
	testLanguage := "en"
	manager.SetLanguage(testLanguage)
	if manager.GetLanguage() != testLanguage {
		t.Errorf("Language not set correctly: got %s, want %s", manager.GetLanguage(), testLanguage)
	}

	// Test SetTheme and GetTheme
	testTheme := "dark"
	manager.SetTheme(testTheme)
	if manager.GetTheme() != testTheme {
		t.Errorf("Theme not set correctly: got %s, want %s", manager.GetTheme(), testTheme)
	}

	// Test SetFilenameTemplate and GetFilenameTemplate
	testTemplate := "%(title)s.%(ext)s"
	manager.SetFilenameTemplate(testTemplate)
	if manager.GetFilenameTemplate() != testTemplate {
		t.Errorf("Filename template not set correctly: got %s, want %s", manager.GetFilenameTemplate(), testTemplate)
	}

	// Test SetCustomCookiePath and GetCustomCookiePath
	testCookiePath := "/test/cookies.txt"
	manager.SetCustomCookiePath(testCookiePath)
	if manager.GetCustomCookiePath() != testCookiePath {
		t.Errorf("Custom cookie path not set correctly: got %s, want %s", manager.GetCustomCookiePath(), testCookiePath)
	}

	// Test SetString and GetString
	manager.SetString("test_key", "test_value")
	if manager.GetString("test_key") != "test_value" {
		t.Errorf("String value not set correctly: got %s, want test_value", manager.GetString("test_key"))
	}

	// Test SetBool and GetBool
	manager.SetBool("test_bool", true)
	if !manager.GetBool("test_bool") {
		t.Error("Bool value not set correctly: got false, want true")
	}

	// Test GetSponsorBlock
	manager.SetBool("sponsorblock_enabled", true)
	if !manager.GetSponsorBlock() {
		t.Error("SponsorBlock not set correctly: got false, want true")
	}
}

// TestConfigSave tests saving configuration
func TestConfigSave(t *testing.T) {
	// Create a test database
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Create a config manager
	configDir, err := os.MkdirTemp("", "gotube-config-dir-*")
	if err != nil {
		t.Fatalf("Failed to create temp config directory: %v", err)
	}
	defer os.RemoveAll(configDir)

	manager, err := NewManager(db)
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Load configuration
	err = manager.Load()
	if err != nil {
		t.Fatalf("Failed to load configuration: %v", err)
	}

	// Set some values
	testPath := filepath.Join(configDir, "downloads")
	manager.SetDownloadPath(testPath)
	manager.SetLanguage("fr")
	manager.SetTheme("light")
	manager.SetFilenameTemplate("%(title)s - %(id)s.%(ext)s")
	manager.SetString("test_key", "test_value")
	manager.SetBool("test_bool", true)

	// Save configuration
	err = manager.save()
	if err != nil {
		t.Fatalf("Failed to save configuration: %v", err)
	}

	// Create a new manager to load the saved configuration
	manager2, err := NewManager(db)
	if err != nil {
		t.Fatalf("Failed to create second config manager: %v", err)
	}

	// Load configuration
	err = manager2.Load()
	if err != nil {
		t.Fatalf("Failed to load saved configuration: %v", err)
	}

	// Check if values were saved correctly
	if manager2.GetDownloadPath() != testPath {
		t.Errorf("Download path not saved correctly: got %s, want %s", manager2.GetDownloadPath(), testPath)
	}
	if manager2.GetLanguage() != "fr" {
		t.Errorf("Language not saved correctly: got %s, want fr", manager2.GetLanguage())
	}
	if manager2.GetTheme() != "light" {
		t.Errorf("Theme not saved correctly: got %s, want light", manager2.GetTheme())
	}
	expectedTemplate := "%(title)s - %(id)s.%(ext)s"
	if manager2.GetFilenameTemplate() != expectedTemplate {
		t.Errorf("Filename template not saved correctly: got %s, want %s", manager2.GetFilenameTemplate(), expectedTemplate)
	}
	if manager2.GetString("test_key") != "test_value" {
		t.Errorf("String value not saved correctly: got %s, want test_value", manager2.GetString("test_key"))
	}
	if !manager2.GetBool("test_bool") {
		t.Error("Bool value not saved correctly: got false, want true")
	}
}

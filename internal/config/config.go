// File: internal/config/config.go

package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
)

// Config holds application configuration settings.
type Config struct {
	DownloadPath     string            `json:"download_path"`
	Language         string            `json:"language"`
	Theme            string            `json:"theme"`
	FilenameTemplate string            `json:"filename_template"`
	CustomCookiePath string            `json:"custom_cookie_path,omitempty"`
	StringValues     map[string]string `json:"string_values"`
	BoolValues       map[string]bool   `json:"bool_values"`
}

// Manager manages configuration loading and persistence.
type Manager struct {
	config    Config
	configDir string
	db        *database.DB
	loaded    bool
}

// NewManager initializes a new configuration manager with a database connection.
// It sets default values and defers loading if the database is encrypted.
func NewManager(db *database.DB) (*Manager, error) {
	if db == nil {
		return nil, fmt.Errorf("database connection is required")
	}

	configDir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("failed to get working directory: %w", err)
	}

	manager := &Manager{
		config: Config{
			StringValues:     make(map[string]string),
			BoolValues:       make(map[string]bool),
			Theme:            "dark",
			Language:         "en",
			FilenameTemplate: "%(id)s.%(ext)s",
		},
		configDir: configDir,
		db:        db,
	}

	// Set default download path
	if defaultDir, err := getDefaultDownloadDir(); err == nil {
		manager.config.DownloadPath = defaultDir
	} else {
		fmt.Printf("Warning: failed to get default download directory: %v\n", err)
		manager.config.DownloadPath = "."
	}

	// Load config immediately if the database is not encrypted
	if !db.IsEncrypted() {
		if err := manager.Load(); err != nil {
			fmt.Printf("Warning: initial config load failed: %v\n", err)
			if saveErr := manager.save(); saveErr != nil {
				fmt.Printf("Error: failed to save default config: %v\n", saveErr)
			}
			manager.loaded = true
		}
	} else {
		fmt.Println("ConfigManager: database is encrypted, deferring initial load")
	}

	return manager, nil
}

// Load retrieves configuration from JSON (for migration) or the database.
func (m *Manager) Load() error {
	if m.loaded {
		fmt.Println("ConfigManager: already loaded")
		return nil
	}
	if m.db == nil {
		return fmt.Errorf("cannot load config: database is nil")
	}

	// Attempt migration from JSON file
	configPath := filepath.Join(m.configDir, "config.json")
	if _, err := os.Stat(configPath); err == nil {
		fmt.Println("ConfigManager: migrating config from JSON")
		if err := m.migrateFromJSON(configPath); err != nil {
			return fmt.Errorf("failed to migrate config from JSON: %w", err)
		}
		m.loaded = true
		fmt.Println("ConfigManager: JSON migration successful")
		return nil
	}

	// Load from database
	fmt.Println("ConfigManager: loading config from database")
	if err := m.loadFromDB(); err != nil {
		return fmt.Errorf("failed to load config from database: %w", err)
	}

	m.loaded = true
	fmt.Println("ConfigManager: database load successful")
	return nil
}

// migrateFromJSON imports configuration from a JSON file and saves it to the database.
func (m *Manager) migrateFromJSON(path string) error {
	fmt.Printf("Migrating config from JSON: %s\n", path)

	data, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("failed to read JSON config: %w", err)
	}

	var jsonConfig Config
	if err := json.Unmarshal(data, &jsonConfig); err != nil {
		return fmt.Errorf("failed to parse JSON config: %w", err)
	}

	// Merge JSON config with existing defaults
	m.mergeConfig(jsonConfig)

	return m.save()
}

// mergeConfig updates the manager's config with non-empty values from the provided config.
func (m *Manager) mergeConfig(c Config) {
	if c.DownloadPath != "" {
		m.config.DownloadPath = c.DownloadPath
	}
	if c.Language != "" {
		m.config.Language = c.Language
	}
	if c.Theme != "" {
		m.config.Theme = c.Theme
	}
	if c.FilenameTemplate != "" {
		m.config.FilenameTemplate = c.FilenameTemplate
	}
	if c.CustomCookiePath != "" {
		m.config.CustomCookiePath = c.CustomCookiePath
	}

	if c.StringValues != nil {
		if m.config.StringValues == nil {
			m.config.StringValues = make(map[string]string)
		}
		for k, v := range c.StringValues {
			m.config.StringValues[k] = v
		}
	}
	if c.BoolValues != nil {
		if m.config.BoolValues == nil {
			m.config.BoolValues = make(map[string]bool)
		}
		for k, v := range c.BoolValues {
			m.config.BoolValues[k] = v
		}
	}
}

// loadFromDB retrieves configuration from the database.
func (m *Manager) loadFromDB() error {
	configFields := []struct {
		key      string
		target   *string
		defaultV string
	}{
		{key: "download_path", target: &m.config.DownloadPath},
		{key: "language", target: &m.config.Language},
		{key: "theme", target: &m.config.Theme},
		{key: "filename_template", target: &m.config.FilenameTemplate},
		{key: "custom_cookie_path", target: &m.config.CustomCookiePath},
	}

	for _, field := range configFields {
		if value, _, err := m.db.GetConfig(field.key); err != nil {
			fmt.Printf("Warning: failed to load '%s' from database: %v\n", field.key, err)
		} else if value != "" {
			*field.target = value
		}
	}

	// Load string values map
	if jsonData, _, err := m.db.GetConfig("string_values"); err != nil {
		fmt.Printf("Warning: failed to load 'string_values' from database: %v\n", err)
	} else if jsonData != "" {
		if stringValues, err := database.UnmarshalStringMap(jsonData); err != nil {
			fmt.Printf("Warning: failed to unmarshal 'string_values': %v\n", err)
		} else {
			m.config.StringValues = stringValues
		}
	}

	// Load bool values map
	if jsonData, _, err := m.db.GetConfig("bool_values"); err != nil {
		fmt.Printf("Warning: failed to load 'bool_values' from database: %v\n", err)
	} else if jsonData != "" {
		if boolValues, err := database.UnmarshalBoolMap(jsonData); err != nil {
			fmt.Printf("Warning: failed to unmarshal 'bool_values': %v\n", err)
		} else {
			m.config.BoolValues = boolValues
		}
	}

	// Ensure maps are initialized
	if m.config.StringValues == nil {
		m.config.StringValues = make(map[string]string)
	}
	if m.config.BoolValues == nil {
		m.config.BoolValues = make(map[string]bool)
	}

	return nil
}

// save persists the configuration to the database.
func (m *Manager) save() error {
	if m.db == nil {
		return fmt.Errorf("cannot save config: database is nil")
	}

	configFields := []struct {
		key   string
		value string
	}{
		{key: "download_path", value: m.config.DownloadPath},
		{key: "language", value: m.config.Language},
		{key: "theme", value: m.config.Theme},
		{key: "filename_template", value: m.config.FilenameTemplate},
		{key: "custom_cookie_path", value: m.config.CustomCookiePath},
	}

	for _, field := range configFields {
		if err := m.db.SetConfig(field.key, field.value, false); err != nil {
			return fmt.Errorf("failed to save '%s': %w", field.key, err)
		}
	}

	// Save string values map
	stringValuesJSON, err := database.MarshalStringMap(m.config.StringValues)
	if err != nil {
		return fmt.Errorf("failed to marshal string values: %w", err)
	}
	if err := m.db.SetConfig("string_values", stringValuesJSON, false); err != nil {
		return fmt.Errorf("failed to save string values: %w", err)
	}

	// Save bool values map
	boolValuesJSON, err := database.MarshalBoolMap(m.config.BoolValues)
	if err != nil {
		return fmt.Errorf("failed to marshal bool values: %w", err)
	}
	if err := m.db.SetConfig("bool_values", boolValuesJSON, false); err != nil {
		return fmt.Errorf("failed to save bool values: %w", err)
	}

	return nil
}

// ensureLoaded loads the configuration if it hasn't been loaded yet.
func (m *Manager) ensureLoaded() {
	if !m.loaded {
		m.Load()
	}
}

// GetConfigDir returns the configuration directory.
func (m *Manager) GetConfigDir() string {
	return m.configDir
}

// IsLoaded checks if the configuration has been loaded.
func (m *Manager) IsLoaded() bool {
	return m.loaded
}

// GetString retrieves a string value by key.
func (m *Manager) GetString(key string) string {
	m.ensureLoaded()
	if m.config.StringValues == nil {
		return ""
	}
	return m.config.StringValues[key]
}

// SetString sets a string value by key.
func (m *Manager) SetString(key, value string) error {
	if m.config.StringValues == nil {
		m.config.StringValues = make(map[string]string)
	}
	m.config.StringValues[key] = value
	return m.save()
}

// GetBool retrieves a boolean value by key.
func (m *Manager) GetBool(key string) bool {
	m.ensureLoaded()
	if m.config.BoolValues == nil {
		return false
	}
	return m.config.BoolValues[key]
}

// SetBool sets a boolean value by key.
func (m *Manager) SetBool(key string, value bool) error {
	if m.config.BoolValues == nil {
		m.config.BoolValues = make(map[string]bool)
	}
	m.config.BoolValues[key] = value
	return m.save()
}

// Configuration accessors for specific fields.
var configAccessors = []struct {
	name       string
	getter     func(*Config) string
	setter     func(*Config, string)
	defaultVal string
}{
	{
		name:   "DownloadPath",
		getter: func(c *Config) string { return c.DownloadPath },
		setter: func(c *Config, v string) { c.DownloadPath = v },
	},
	{
		name:       "Language",
		getter:     func(c *Config) string { return c.Language },
		setter:     func(c *Config, v string) { c.Language = v },
		defaultVal: "en",
	},
	{
		name:       "Theme",
		getter:     func(c *Config) string { return c.Theme },
		setter:     func(c *Config, v string) { c.Theme = v },
		defaultVal: "dark",
	},
	{
		name:       "FilenameTemplate",
		getter:     func(c *Config) string { return c.FilenameTemplate },
		setter:     func(c *Config, v string) { c.FilenameTemplate = v },
		defaultVal: "%(title)s-%(id)s.%(ext)s",
	},
	{
		name:   "CustomCookiePath",
		getter: func(c *Config) string { return c.CustomCookiePath },
		setter: func(c *Config, v string) { c.CustomCookiePath = v },
	},
}

// Generate getter and setter methods dynamically
func (m *Manager) getField(accessor struct {
	name       string
	getter     func(*Config) string
	setter     func(*Config, string)
	defaultVal string
}) string {
	m.ensureLoaded()
	value := accessor.getter(&m.config)
	if value == "" && accessor.defaultVal != "" {
		return accessor.defaultVal
	}
	return value
}

func (m *Manager) setField(accessor struct {
	name       string
	getter     func(*Config) string
	setter     func(*Config, string)
	defaultVal string
}, value string) error {
	accessor.setter(&m.config, value)
	return m.save()
}

// GetDownloadPath returns the download path.
func (m *Manager) GetDownloadPath() string {
	return m.getField(configAccessors[0])
}

// SetDownloadPath sets the download path.
func (m *Manager) SetDownloadPath(path string) error {
	return m.setField(configAccessors[0], path)
}

// GetLanguage returns the language setting.
func (m *Manager) GetLanguage() string {
	return m.getField(configAccessors[1])
}

// SetLanguage sets the language setting.
func (m *Manager) SetLanguage(lang string) error {
	return m.setField(configAccessors[1], lang)
}

// GetTheme returns the theme setting.
func (m *Manager) GetTheme() string {
	return m.getField(configAccessors[2])
}

// SetTheme sets the theme setting.
func (m *Manager) SetTheme(theme string) error {
	return m.setField(configAccessors[2], theme)
}

// GetFilenameTemplate returns the filename template.
func (m *Manager) GetFilenameTemplate() string {
	return m.getField(configAccessors[3])
}

// SetFilenameTemplate sets the filename template.
func (m *Manager) SetFilenameTemplate(template string) error {
	return m.setField(configAccessors[3], template)
}

// GetCustomCookiePath returns the custom cookie file path.
func (m *Manager) GetCustomCookiePath() string {
	return m.getField(configAccessors[4])
}

// SetCustomCookiePath sets the custom cookie file path.
func (m *Manager) SetCustomCookiePath(path string) error {
	return m.setField(configAccessors[4], path)
}

// GetSponsorBlock checks if SponsorBlock is enabled.
func (m *Manager) GetSponsorBlock() bool {
	return m.GetBool("sponsorblock_enabled")
}

// SetSponsorBlock sets the SponsorBlock enabled status.
func (m *Manager) SetSponsorBlock(enabled bool) error {
	return m.SetBool("sponsorblock_enabled", enabled)
}

// getDefaultDownloadDir determines the default download directory based on the OS.
func getDefaultDownloadDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get home directory: %w", err)
	}

	downloadDir := filepath.Join(homeDir, "Downloads")
	switch runtime.GOOS {
	case "windows", "darwin":
		return downloadDir, nil
	default: // Linux and others
		if xdgDir := os.Getenv("XDG_DOWNLOAD_DIR"); xdgDir != "" && filepath.IsAbs(xdgDir) {
			return xdgDir, nil
		}
		fmt.Printf("Warning: XDG_DOWNLOAD_DIR is invalid or unset, using fallback: %s\n", downloadDir)
		return downloadDir, nil
	}
}

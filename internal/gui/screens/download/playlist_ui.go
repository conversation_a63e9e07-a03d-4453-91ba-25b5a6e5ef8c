package download

import (
	"fmt"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/dialogs"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// handlePlaylistDownload manages the download of a playlist
func (dt *DownloadTab) handlePlaylistDownload(url, format, quality, downloadPath, statusPrefix string) {
	dt.updateStatusFunc(statusPrefix + fmt.Sprintf("Fetching playlist information for %s...", url))

	// Get playlist metadata
	playlist, err := dt.downloader.GetPlaylistMetadata(url)
	if err != nil {
		dt.updateStatusFunc(statusPrefix + fmt.Sprintf("Error fetching playlist metadata: %v", err))
		dialog.ShowError(fmt.Errorf("failed to fetch playlist information: %v", err), dt.window)
		return
	}

	dt.updateStatusFunc(statusPrefix + fmt.Sprintf("Found playlist: %s (%d videos)", playlist.Title, len(playlist.Entries)))

	// Show playlist selection dialog
	dialogs.NewPlaylistDialog(dt.window, playlist, func(selectedVideos []metadata.VideoMetadata) {
		if len(selectedVideos) == 0 {
			dialog.ShowInformation(i18n.Get("info"), i18n.Get("no_videos_selected"), dt.window)
			return
		}

		// Start the download process
		dt.downloadPlaylistVideos(selectedVideos, format, quality, downloadPath)
	}).Show()
}

// downloadPlaylistVideos downloads the selected videos from a playlist
func (dt *DownloadTab) downloadPlaylistVideos(videos []metadata.VideoMetadata, format, quality, downloadPath string) {
	dt.updateStatusFunc(fmt.Sprintf("Starting download of %d videos from playlist", len(videos)))

	// Create progress tracking UI
	progressContainer := container.NewVBox()
	progressBars := make([]*widget.ProgressBar, len(videos))
	statusLabels := make([]*widget.Label, len(videos))

	for i, video := range videos {
		// Create progress bar and label for each video
		progressBars[i] = widget.NewProgressBar()
		statusLabels[i] = widget.NewLabel(fmt.Sprintf("Waiting to download: %s", video.Title))

		// Add to container
		videoContainer := container.NewVBox(
			widget.NewLabel(fmt.Sprintf("%d. %s", i+1, video.Title)),
			progressBars[i],
			statusLabels[i],
			widget.NewSeparator(),
		)
		progressContainer.Add(videoContainer)
	}

	// Create a dialog to show download progress
	downloadDialog := dialog.NewCustom(
		i18n.Get("download_queue"),
		i18n.Get("cancel"),
		container.NewScroll(progressContainer),
		dt.window,
	)
	downloadDialog.Resize(fyne.NewSize(600, 400))
	downloadDialog.Show()

	// Start the download process
	go func() {
		err := dt.downloader.DownloadPlaylistVideos(
			videos,
			format,
			quality,
			downloadPath,
			func(index int, progress float64) {
				// Update progress bar for the specific video
				if index >= 0 && index < len(progressBars) {
					progressBars[index].SetValue(progress)
				}
			},
			func(index int, status string) {
				// Update status label for the specific video
				if index >= 0 && index < len(statusLabels) {
					statusLabels[index].SetText(status)
				}
			},
		)

		// After download is complete, update UI on main thread
		downloadDialog.Hide()
		// Remove this line since Fyne handles thread safety automatically:
		// downloadDialog.Hide() // This line was duplicated anyway

		if err != nil {
			dt.updateStatusFunc(fmt.Sprintf("Error downloading playlist videos: %v", err))
			dialog.ShowError(fmt.Errorf("download error: %v", err), dt.window)
		} else {
			dt.updateStatusFunc("Playlist download completed successfully")
			dialog.ShowInformation(i18n.Get("success"), "Playlist download completed", dt.window)
		}

		// Add to history
		for _, video := range videos {
			dt.historyManager.AddRecord(history.DownloadRecord{
				URL:         video.URL,
				Title:       video.Title,
				Format:      format,
				Quality:     quality,
				Path:        downloadPath,
				Description: video.Description,
				Thumbnail:   video.Thumbnail,
				Duration:    video.Duration,
				Author:      video.Author,
			})
		}

		// Close the dialog
		downloadDialog.Hide()
	}()
}

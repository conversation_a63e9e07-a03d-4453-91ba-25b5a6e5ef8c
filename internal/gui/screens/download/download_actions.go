// File: internal/gui/screens/download/download_actions.go

package download

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2/dialog"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// handleDownload manages the download process.
func (dt *DownloadTab) handleDownload() {
	urlsRaw := strings.Split(strings.TrimSpace(dt.urlEntry.Text), "\n")
	format := dt.formatSelect.Selected
	qualityKey := ""

	// Validate format selection (prevent separator from being used)
	if format == "--- Available Formats ---" || format == "" {
		dialog.ShowInformation(i18n.Get("info"), i18n.Get("select_format_quality"), dt.window)
		return
	}

	// Handle custom formats vs favorite formats
	if dt.isCustomFormat(format) {
		// For custom formats, use the format ID directly
		format = dt.getFormatIDFromSelection(format)
		qualityKey = "default" // Custom formats don't need quality mapping
	} else {
		// Get Quality Key for favorite formats
		switch dt.formatSelect.Selected {
		case "MP4":
			keys := []string{"360p", "720p", "1080p"}
			for i, translated := range dt.qualitySelect.Options {
				if translated == dt.qualitySelect.Selected && i < len(keys) {
					qualityKey = keys[i]
					break
				}
			}
		case "MP3", "M4A":
			keys := []string{"quality_128", "quality_highest"}
			for i, translated := range dt.qualitySelect.Options {
				if translated == dt.qualitySelect.Selected && i < len(keys) {
					qualityKey = keys[i]
					break
				}
			}
		}

		// Fallback for favorite formats
		if qualityKey == "" && len(dt.qualitySelect.Options) > 0 {
			switch dt.formatSelect.Selected {
			case "MP4":
				qualityKey = "360p"
			case "MP3", "M4A":
				qualityKey = "quality_128"
			}
			if dt.logger != nil {
				dt.logger.Info(fmt.Sprintf("Could not map selected quality '%s'. Using fallback '%s'.", dt.qualitySelect.Selected, qualityKey))
			}
		}
	}

	downloadPath := dt.pathLabel.Text
	var urls []string
	for _, u := range urlsRaw {
		if trimmed := strings.TrimSpace(u); trimmed != "" {
			urls = append(urls, trimmed)
		}
	}

	// Input Validation
	if len(urls) == 0 {
		dialog.ShowInformation(i18n.Get("info"), i18n.Get("enter_at_least_one_url"), dt.window)
		return
	}
	if format == "" || qualityKey == "" {
		dialog.ShowInformation(i18n.Get("info"), i18n.Get("select_format_quality"), dt.window)
		return
	}
	if format == "MP4" && qualityKey == "" {
		dialog.ShowInformation(i18n.Get("info"), i18n.Get("error_empty_quality"), dt.window)
		return
	}
	if downloadPath == "" {
		dialog.ShowInformation(i18n.Get("info"), i18n.Get("select_path"), dt.window)
		return
	}

	// UI State Update
	dt.startButton.Disable()
	dt.cancelButton.Enable()
	dt.app.UpdateStatus(i18n.Get("status_initializing"))
	dt.progress.SetValue(0)

	// Start Download Goroutine
	go func() {
		defer func() {
			dt.app.UpdateStatus(i18n.Get("status_idle"))
			dt.progress.SetValue(0)
			dt.startButton.Enable()
			dt.cancelButton.Disable()
		}()

		totalDownloads := len(urls)
		completedDownloads := 0

		for i, url := range urls {
			currentStatusPrefix := fmt.Sprintf("[%d/%d] ", i+1, totalDownloads)
			dt.app.UpdateStatus(currentStatusPrefix + fmt.Sprintf(i18n.Get("status_fetching_metadata")))
			dt.progress.SetValue(0) // Reset progress

			// Check if this is a playlist URL
			isPlaylist, err := dt.downloader.IsPlaylist(url)
			if err != nil {
				dt.app.UpdateStatus(currentStatusPrefix + fmt.Sprintf("Error checking URL type: %v", err))
				continue
			}

			if isPlaylist {
				// Handle playlist
				dt.handlePlaylistDownload(url, format, qualityKey, downloadPath, currentStatusPrefix)
				completedDownloads++ // Assume playlist handler completes one "item"
				continue
			}

			// Regular video download
			filePath, err := dt.downloader.Download(url, format, qualityKey, downloadPath,
				func(p float64) { dt.progress.SetValue(p) },
				func(msg string) { dt.app.UpdateStatus(currentStatusPrefix + msg) },
			)

			metadata, metaErr := dt.downloader.MetadataProvider.Get(url)

			if err != nil {
				title := url
				if metaErr == nil {
					title = metadata.Title
				}
				errorTitle := i18n.Get("error_download_failed_dialog_title")
				errorMessage := fmt.Sprintf(i18n.Get("error_download_failed_dialog_message"), title, err)
				dialog.ShowInformation(errorTitle, errorMessage, dt.window)

				finalErrorMsg := fmt.Sprintf("Error downloading %s: %v", title, err)
				dt.app.UpdateStatus(finalErrorMsg)
				if dt.logger != nil {
					dt.logger.Error(finalErrorMsg)
				}
				continue
			}

			// Success: Add to History
			recordTitle := url
			if metaErr == nil {
				recordTitle = metadata.Title
			}
			if metaErr != nil && dt.logger != nil {
				dt.logger.Info(fmt.Sprintf("Could not fetch metadata for history record of %s: %v", url, metaErr))
			}
			record := history.DownloadRecord{URL: url, Title: recordTitle, Format: format, Quality: dt.qualitySelect.Selected, Path: filePath, Timestamp: time.Now()}
			if metaErr == nil {
				record.Description, record.Thumbnail, record.Duration, record.Author = metadata.Description, metadata.Thumbnail, metadata.Duration, metadata.Author
			}
			if histErr := dt.historyManager.AddRecord(record); histErr != nil && dt.logger != nil {
				dt.logger.Error(fmt.Sprintf("Failed to add download record: %v", histErr))
			}

			completedDownloads++
			if dt.refreshHistoryFunc != nil {
				dt.refreshHistoryFunc()
			}
		} // End URL loop

		// Final Status Update
		summaryMsg := ""
		if completedDownloads == totalDownloads && totalDownloads > 0 {
			summaryMsg = i18n.Get("all_downloads_completed")
		} else if totalDownloads > 0 {
			summaryMsgFormat := i18n.Get("downloads_finished_summary_format")
			summaryMsg = fmt.Sprintf(summaryMsgFormat, completedDownloads, totalDownloads)
		}
		if summaryMsg != "" {
			dt.app.UpdateStatus(summaryMsg)
		}
	}() // End goroutine
}

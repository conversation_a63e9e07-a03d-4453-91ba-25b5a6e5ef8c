package download

import (
	"errors" // Added for errors.New
	"fmt"
	"path/filepath"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/dialogs"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
)

// StatusManager handles status updates and progress tracking
type StatusManager struct {
	window      fyne.Window
	statusLabel *widget.Label
	progressBar *widget.ProgressBar
	logger      *logger.Logger
}

// NewStatusManager creates a new status manager
func NewStatusManager(window fyne.Window, statusLabel *widget.Label, progressBar *widget.ProgressBar, logger *logger.Logger) *StatusManager {
	return &StatusManager{
		window:      window,
		statusLabel: statusLabel,
		progressBar: progressBar,
		logger:      logger,
	}
}

// UpdateStatus updates the status label
func (sm *StatusManager) UpdateStatus(status string) {
	// Log the status
	if sm.logger != nil {
		sm.logger.Info("Status: %s", status)
	}

	// Update the status label
	sm.statusLabel.SetText(status)
}

// UpdateProgress updates the progress bar
func (sm *StatusManager) UpdateProgress(progress float64) {
	// Update the progress bar
	sm.progressBar.SetValue(progress)
}

// ResetProgress resets the progress bar
func (sm *StatusManager) ResetProgress() {
	// Reset the progress bar
	sm.progressBar.SetValue(0)
}

// ShowError shows an error dialog and updates the status
func (sm *StatusManager) ShowError(err error, title string) {
	if err == nil {
		return
	}

	// Update status
	sm.UpdateStatus(fmt.Sprintf("%s: %v", title, err))

	// Show error dialog
	dialog.ShowError(err, sm.window)
}

// ShowInfo shows an information dialog and updates the status
func (sm *StatusManager) ShowInfo(message, title string) {
	// Update status
	sm.UpdateStatus(message)

	// Show info dialog
	dialog.ShowInformation(title, message, sm.window)
}

// DownloadHandler manages the download process
type DownloadHandler struct {
	app           common.AppInterface
	downloader    *downloader.Downloader
	statusManager *StatusManager
	isDownloading bool
	downloadPath  string
}

// NewDownloadHandler creates a new download handler
func NewDownloadHandler(app common.AppInterface, statusLabel *widget.Label, progressBar *widget.ProgressBar) *DownloadHandler {
	// Create status manager
	statusManager := NewStatusManager(app.GetWindow(), statusLabel, progressBar, app.GetLogger().(*logger.Logger))
	return &DownloadHandler{
		app:           app,
		downloader:    app.GetDownloader().(*downloader.Downloader),
		statusManager: statusManager,
		isDownloading: false,
		downloadPath:  "",
	}
}

// HandleDownload initiates a download with the given parameters
func (dh *DownloadHandler) HandleDownload(url, format, quality string) {
	// Check if already downloading
	if dh.isDownloading {
		dh.statusManager.ShowInfo(i18n.Get("status_already_downloading"), i18n.Get("dialog_download_title"))
		return
	}

	// Validate URL
	if url == "" {
		dh.statusManager.ShowError(errors.New(i18n.Get("error_empty_url")), i18n.Get("dialog_download_title"))
		return
	}

	// Validate format
	if format == "" {
		dh.statusManager.ShowError(errors.New(i18n.Get("error_empty_format")), i18n.Get("dialog_download_title"))
		return
	}

	// Validate quality for video formats
	if (format == "MP4" || format == "WEBM") && quality == "" {
		// SA1006: changed fmt.Errorf(i18n.Get("error_empty_quality")) to errors.New(...)
		dh.statusManager.ShowError(errors.New(i18n.Get("error_empty_quality")), i18n.Get("dialog_download_title"))
		return
	}

	// Validate download path
	if dh.downloadPath == "" {
		dh.selectDownloadPath(func() {
			dh.HandleDownload(url, format, quality)
		})
		return
	}

	// Check if URL is a playlist
	dh.statusManager.UpdateStatus(i18n.Get("status_checking_url"))

	go func() {
		isPlaylist, err := dh.downloader.IsPlaylist(url)
		if err != nil {
			// Update UI on the main thread
			// SA1006: changed fmt.Errorf(i18n.Get("error_check_playlist"), err) to fmt.Errorf("%s: %w", ...)
			dh.statusManager.ShowError(fmt.Errorf("%s: %w", i18n.Get("error_check_playlist"), err), i18n.Get("dialog_download_title"))
			return
		}

		if isPlaylist {
			// Handle playlist download
			playlist, err := dh.downloader.GetPlaylistMetadata(url)
			if err != nil {
				// SA1006: Warning here is likely a false positive, no change applied as it's idiomatic.
				dh.statusManager.ShowError(fmt.Errorf("failed to fetch playlist info: %w", err), "Playlist Error")
				return
			}
			dh.statusManager.UpdateStatus(fmt.Sprintf("Found playlist: %s", playlist.Title))

			dialogs.NewPlaylistDialog(dh.app.GetWindow(), playlist, func(selectedVideos []metadata.VideoMetadata) {
				if len(selectedVideos) == 0 {
					dh.statusManager.ShowInfo("No videos selected", "Info")
					return
				}
				dh.downloadPlaylistVideos(selectedVideos, format, quality, dh.downloadPath)
			}).Show()
			return
		}

		// Start single video download
		dh.startDownload(url, format, quality)
	}()
}

// downloadPlaylistVideos downloads the selected videos from a playlist
func (dh *DownloadHandler) downloadPlaylistVideos(videos []metadata.VideoMetadata, format, quality, downloadPath string) {
	dh.statusManager.UpdateStatus(fmt.Sprintf("Starting download of %d videos from playlist", len(videos)))

	// Create progress tracking UI
	progressContainer := container.NewVBox()
	progressBars := make([]*widget.ProgressBar, len(videos))
	statusLabels := make([]*widget.Label, len(videos))

	for i, video := range videos {
		progressBars[i] = widget.NewProgressBar()
		statusLabels[i] = widget.NewLabel(fmt.Sprintf("Waiting: %s", video.Title))
		videoContainer := container.NewVBox(
			widget.NewLabel(fmt.Sprintf("%d. %s", i+1, video.Title)),
			progressBars[i],
			statusLabels[i],
			widget.NewSeparator(),
		)
		progressContainer.Add(videoContainer)
	}

	downloadDialog := dialog.NewCustom(
		i18n.Get("download_queue"),
		i18n.Get("cancel"),
		container.NewScroll(progressContainer),
		dh.app.GetWindow(),
	)
	downloadDialog.Resize(fyne.NewSize(600, 400))
	downloadDialog.Show()

	go func() {
		err := dh.downloader.DownloadPlaylistVideos(
			videos,
			format,
			quality,
			downloadPath,
			func(index int, progress float64) {
				if index >= 0 && index < len(progressBars) {
					progressBars[index].SetValue(progress)
				}
			},
			func(index int, status string) {
				if index >= 0 && index < len(statusLabels) {
					statusLabels[index].SetText(status)
				} else if index == -1 {
					dh.statusManager.UpdateStatus(status)
				}
			},
		)

		downloadDialog.Hide()

		if err != nil {
			dh.statusManager.ShowError(err, "Playlist Download Error")
		} else {
			dh.statusManager.ShowInfo("Playlist download completed successfully", "Success")
		}

		historyManager := dh.app.GetHistory().(*history.HistoryManager)
		if historyManager != nil {
			for _, video := range videos {
				historyManager.AddRecord(history.DownloadRecord{
					URL:       video.URL,
					Title:     video.Title,
					Format:    format,
					Quality:   quality,
					Path:      downloadPath,
					Timestamp: time.Now(),
				})
			}
		}
	}()
}

// startDownload initiates a single video download
func (dh *DownloadHandler) startDownload(url, format, quality string) {
	// Set downloading flag
	dh.isDownloading = true

	// Reset progress
	dh.statusManager.ResetProgress()

	// Update status
	dh.statusManager.UpdateStatus(fmt.Sprintf(i18n.Get("status_downloading"), url))

	// Start download in a goroutine
	go func() {
		// Create progress callback
		progressCb := func(progress float64) {
			// Update UI on the main thread
			dh.statusManager.UpdateProgress(progress)
		}

		// Create status callback
		statusCb := func(status string) {
			// Update UI on the main thread
			dh.statusManager.UpdateStatus(status)
		}

		// Start download
		startTime := time.Now()
		filePath, err := dh.downloader.Download(url, format, quality, dh.downloadPath, progressCb, statusCb)

		// Reset downloading flag
		dh.isDownloading = false

		// Handle result
		if err != nil {
			// Handle error
			dh.statusManager.ShowError(err, i18n.Get("dialog_download_title"))
			return
		}

		// Calculate download time
		duration := time.Since(startTime)

		// Add to history
		historyManager := dh.app.GetHistory().(*history.HistoryManager)
		if historyManager != nil {
			record := history.DownloadRecord{
				URL:       url,
				Format:    format,
				Quality:   quality,
				Path:      filePath,
				Timestamp: time.Now(),
			}
			historyManager.AddRecord(record)
		}

		// Show success message
		dh.statusManager.UpdateStatus(fmt.Sprintf(i18n.Get("status_download_complete"), filepath.Base(filePath), formatDuration(duration)))

		// Show success dialog
		dialog.ShowInformation(
			i18n.Get("dialog_download_title"),
			fmt.Sprintf(i18n.Get("dialog_download_success"), filepath.Base(filePath), formatDuration(duration)),
			dh.app.GetWindow(),
		)
	}()
}

// Note: Playlist handling has been moved to the package-level handlePlaylistDownload function

// selectDownloadPath shows a dialog to select the download path
func (dh *DownloadHandler) selectDownloadPath(callback func()) {
	// Create folder dialog
	fd := dialog.NewFolderOpen(func(uri fyne.ListableURI, err error) {
		if err != nil {
			dh.statusManager.ShowError(err, i18n.Get("dialog_path_title"))
			return
		}

		if uri == nil {
			// User cancelled
			return
		}

		// Get the path
		path := uri.Path()

		// Set download path
		dh.downloadPath = path

		// Save to config
		configManager := dh.app.GetConfig()
		if configManager != nil {
			// Try to set the download path in config
			// This might need to be adjusted based on the actual config interface
		}

		// Update status
		dh.statusManager.UpdateStatus(fmt.Sprintf(i18n.Get("status_path_set"), path))

		// Call callback if provided
		if callback != nil {
			callback()
		}
	}, dh.app.GetWindow())

	// Show dialog
	fd.Show()
}

// CancelDownload cancels the current download
func (dh *DownloadHandler) CancelDownload() {
	if !dh.isDownloading {
		return
	}

	// Update status
	dh.statusManager.UpdateStatus(i18n.Get("status_cancelling"))

	// Cancel download
	dh.downloader.CancelDownload()

	// Reset downloading flag
	dh.isDownloading = false

	// Update status
	dh.statusManager.UpdateStatus(i18n.Get("status_cancelled"))
}

// IsDownloading returns whether a download is in progress
func (dh *DownloadHandler) IsDownloading() bool {
	return dh.isDownloading
}

// GetDownloadPath returns the current download path
func (dh *DownloadHandler) GetDownloadPath() string {
	return dh.downloadPath
}

// SetDownloadPath sets the download path
func (dh *DownloadHandler) SetDownloadPath(path string) {
	dh.downloadPath = path
}

// formatDuration formats a time.Duration as a string
func formatDuration(d time.Duration) string {
	d = d.Round(time.Second)

	h := d / time.Hour
	d -= h * time.Hour

	m := d / time.Minute
	d -= m * time.Minute

	s := d / time.Second

	if h > 0 {
		return fmt.Sprintf("%d:%02d:%02d", h, m, s)
	}

	return fmt.Sprintf("%d:%02d", m, s)
}

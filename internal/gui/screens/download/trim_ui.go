package download

import (
	"errors"
	"fmt"
	"math"
	"strings"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/timeutil"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/utils"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// handleFetchDurationClick is the OnTapped handler for the fetch button.
func (dt *DownloadTab) handleFetchDurationClick() {
	// Get the current URL
	currentURL := ""
	lines := strings.Split(strings.TrimSpace(dt.urlEntry.Text), "\n")
	validCount := 0
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" {
			currentURL = trimmed
			validCount++
		}
	}

	if validCount == 1 && currentURL != "" {
		dt.startDurationFetch(currentURL) // Attempt to start fetch
	} else {
		dialog.ShowInformation(i18n.Get("info"), i18n.Get("enter_single_url_first"), dt.window)
	}
}

// startDurationFetch initiates the video duration fetching process.
func (dt *DownloadTab) startDurationFetch(urlToFetch string) {
	dt.fetchMutex.Lock()
	if dt.isFetchingDuration {
		dt.fetchMutex.Unlock()
		if dt.logger != nil {
			dt.logger.Info("Duration fetch already in progress, skipping request for: " + urlToFetch)
		}
		return // Don't start another fetch if one is running
	}
	// Mark as fetching *before* launching goroutine
	dt.isFetchingDuration = true
	dt.fetchMutex.Unlock()

	// Update UI immediately (safe in Fyne v2)
	dt.fetchDurationButton.Disable() // Disable button during fetch
	dt.updateStatusFunc(fmt.Sprintf(i18n.Get("fetching_metadata_for_format"), urlToFetch))

	// Launch the actual fetch in a goroutine
	go func() {
		// Ensure flag is reset and button state is updated regardless of outcome
		defer func() {
			dt.fetchMutex.Lock()
			dt.isFetchingDuration = false
			// Read last fetch result status *after* fetch completed
			lastFetchedSuccessURL := dt.lastFetchedURLForDuration
			fetchSucceeded := dt.videoDurationSeconds >= 0 && lastFetchedSuccessURL == urlToFetch
			dt.fetchMutex.Unlock()

			// Re-evaluate button state based on the *current* URL in the entry field
			finalURL := ""
			lines := strings.Split(strings.TrimSpace(dt.urlEntry.Text), "\n")
			validCount := 0
			for _, line := range lines {
				trimmed := strings.TrimSpace(line)
				if trimmed != "" {
					finalURL = trimmed
					validCount++
				}
			}

			// Determine if manual fetch should be possible now
			// Needs exactly one URL, which is NOT the last *successfully* fetched URL
			shouldEnableButton := validCount == 1 && finalURL != "" && (!fetchSucceeded || finalURL != lastFetchedSuccessURL)

			// Update button state (safe directly in Fyne v2)
			if shouldEnableButton {
				dt.fetchDurationButton.Enable()
			} else {
				dt.fetchDurationButton.Disable()
			}
		}()

		metadata, err := dt.downloader.MetadataProvider.Get(urlToFetch)

		// --- Process Result ---
		// Check if the URL in the entry field is still the one we fetched for
		currentURLInEntry := ""
		lines := strings.Split(strings.TrimSpace(dt.urlEntry.Text), "\n")
		validURLs := 0
		for _, line := range lines {
			trimmed := strings.TrimSpace(line)
			if trimmed != "" {
				currentURLInEntry = trimmed
				validURLs++
			}
		}

		// Only update UI if the fetched URL is still the relevant single URL
		if validURLs == 1 && currentURLInEntry == urlToFetch {
			if err != nil {
				// Fetch failed for the relevant URL
				errorMsgFormat := i18n.Get("error_fetching_metadata_for_format")
				errMsg := fmt.Sprintf(errorMsgFormat, urlToFetch, err)
				dt.updateStatusFunc(errMsg)
				if dt.logger != nil {
					dt.logger.Error(errMsg)
				}
				// Don't show blocking dialog for automatic fetch failure
				dt.resetTrimUI(i18n.Get("error_fetching")) // Reset UI showing error
				dt.fetchMutex.Lock()
				dt.lastFetchedURLForDuration = "" // Mark as not successfully fetched
				dt.videoDurationSeconds = -1      // Ensure duration state reflects failure
				dt.fetchMutex.Unlock()
				// Defer function will handle enabling the button for retry if needed
			} else {
				// --- Success for the relevant URL ---
				durationSeconds := float64(metadata.Duration)
				durationStr := utils.FormatDurationSeconds(durationSeconds) // Use utility
				statusMsgFormat := i18n.Get("metadata_fetched_for_format")
				dt.updateStatusFunc(fmt.Sprintf(statusMsgFormat, metadata.Title))

				// Update internal state and UI elements
				dt.videoDurationSeconds = durationSeconds // Set successful duration

				// Update duration label
				dt.durationLabel.SetText(i18n.Get("duration") + ": " + durationStr)
				dt.durationLabel.TextStyle = fyne.TextStyle{}
				dt.durationLabel.Refresh()

				// Configure and Enable Sliders
				dt.isUpdatingSlider = true // Prevent OnChanged during setup
				dt.trimStartSlider.Max = durationSeconds
				dt.trimStartSlider.SetValue(0)
				dt.trimStartSlider.Enable()
				dt.startTimeLabel.SetText(utils.FormatDurationSeconds(0)) // Use utility
				dt.trimEndSlider.Max = durationSeconds
				dt.trimEndSlider.SetValue(durationSeconds)
				dt.trimEndSlider.Enable()
				dt.endTimeLabel.SetText(durationStr) // Use utility
				dt.currentTrimStartSeconds = 0
				dt.currentTrimEndSeconds = durationSeconds
				dt.isUpdatingSlider = false

				// Store successful fetch URL *after* updating state
				dt.fetchMutex.Lock()
				dt.lastFetchedURLForDuration = urlToFetch
				dt.fetchMutex.Unlock()

				dt.applyTrimSettingsInternal() // Apply initial state
				// Defer function will ensure button stays disabled (as fetch succeeded for current URL)
			}
		} else {
			// URL changed during fetch, or not a single URL anymore. Discard result.
			if dt.logger != nil {
				dt.logger.Info(fmt.Sprintf("Discarding fetched duration for '%s' as current URL ('%s', count %d) is different or invalid.", urlToFetch, currentURLInEntry, validURLs))
			}
			// If the URL changed *during* fetch, the OnChanged handler for the new URL
			// should have already reset the UI or triggered a new fetch.
			// We just ensure the button state is correct in the defer.
		}
	}() // End goroutine
}

// showTimeInputDialog displays a dialog for manually entering start or end time.
func (dt *DownloadTab) showTimeInputDialog(isStart bool) {
	// Don't allow editing if duration hasn't been fetched yet
	if dt.videoDurationSeconds < 0 {
		dialog.ShowInformation(i18n.Get("info"), i18n.Get("fetch_duration_first"), dt.window)
		return
	}

	var title, currentValue string
	var targetSlider *widget.Slider

	if isStart {
		title = i18n.Get("enter_start_time")
		currentValue = utils.FormatDurationSeconds(dt.currentTrimStartSeconds)
		targetSlider = dt.trimStartSlider
	} else {
		title = i18n.Get("enter_end_time")
		currentValue = utils.FormatDurationSeconds(dt.currentTrimEndSeconds)
		targetSlider = dt.trimEndSlider
	}

	entry := widget.NewEntry()
	entry.SetText(currentValue)
	entry.Validator = func(text string) error {
		if !timeutil.ValidateTimeFormat(text) {
			return errors.New(i18n.Get("invalid_time_format_short"))
		}
		return nil
	}

	dialog.ShowCustomConfirm(title, i18n.Get("ok"), i18n.Get("cancel"), entry, func(confirmed bool) {
		if !confirmed {
			return
		}

		inputText := entry.Text
		// Final validation on confirmation using utility function
		if !timeutil.ValidateTimeFormat(inputText) {
			dialog.ShowError(errors.New(i18n.Get("invalid_time_format")), dt.window)
			return
		}

		// Use utility function
		newSeconds := float64(timeutil.ConvertToSeconds(inputText))

		// Range and Logic Validation
		if newSeconds < 0 {
			dialog.ShowError(errors.New(i18n.Get("time_cannot_be_negative")), dt.window)
			return
		}
		// Use utility function for comparison string
		if newSeconds > dt.videoDurationSeconds+0.1 { // Add tolerance for float comparison
			errMsg := fmt.Sprintf(i18n.Get("time_exceeds_duration_format"), utils.FormatDurationSeconds(dt.videoDurationSeconds))
			dialog.ShowError(errors.New(errMsg), dt.window)
			return
		}

		if isStart {
			if newSeconds > dt.currentTrimEndSeconds {
				dialog.ShowError(errors.New(i18n.Get("start_time_must_be_before_end")), dt.window)
				return
			}
		} else { // isEnd
			if newSeconds < dt.currentTrimStartSeconds {
				dialog.ShowError(errors.New(i18n.Get("end_time_must_be_after_start")), dt.window)
				return
			}
		}

		// If all validation passes, update the slider's value.
		// This will trigger the slider's OnChanged, which updates the label and internal settings.
		targetSlider.SetValue(newSeconds)

	}, dt.window)
}

// applyTrimSettingsInternal reads slider values and updates the downloader.
func (dt *DownloadTab) applyTrimSettingsInternal() {
	if dt.videoDurationSeconds < 0 {
		return
	}
	startSec, endSec := dt.currentTrimStartSeconds, dt.currentTrimEndSeconds
	startStr := utils.FormatDurationSeconds(startSec) // Use utility function
	endStr := ""
	if math.Abs(endSec-dt.videoDurationSeconds) > 0.1 {
		endStr = utils.FormatDurationSeconds(endSec) // Use utility function
	}
	if startSec == 0 && endStr == "" {
		startStr = ""
	} // Clear both if full range selected

	if err := dt.downloader.SetTrimTimes(startStr, endStr); err != nil {
		errMsg := fmt.Sprintf("Error applying trim times: %v", err)
		dt.updateStatusFunc(errMsg)
		if dt.logger != nil {
			dt.logger.Error(errMsg)
		}
	}
}

// resetTrimUI disables trim sliders and resets related labels and state.
func (dt *DownloadTab) resetTrimUI(durationPlaceholderKey string) {
	dt.isUpdatingSlider = true
	dt.trimStartSlider.Disable()
	dt.trimStartSlider.Min, dt.trimStartSlider.Max, dt.trimStartSlider.Value = 0, 1, 0
	dt.trimEndSlider.Disable()
	dt.trimEndSlider.Min, dt.trimEndSlider.Max, dt.trimEndSlider.Value = 0, 1, 1
	dt.isUpdatingSlider = false

	dt.startTimeLabel.SetText(utils.FormatDurationSeconds(0)) // Reset display labels directly
	dt.endTimeLabel.SetText(utils.FormatDurationSeconds(0))   // Reset display labels directly

	dt.durationLabel.SetText(i18n.Get("duration") + ": " + i18n.Get(durationPlaceholderKey))
	dt.durationLabel.TextStyle = fyne.TextStyle{Italic: true}
	dt.durationLabel.Refresh()

	// Clear duration and state flags
	dt.videoDurationSeconds = -1
	dt.currentTrimStartSeconds = -1
	dt.currentTrimEndSeconds = -1
	dt.fetchMutex.Lock()
	dt.lastFetchedURLForDuration = ""
	// Do not reset isFetchingDuration here, let the running fetch complete
	dt.fetchMutex.Unlock()

	// Explicitly disable fetch button on reset; OnChanged or fetch completion will manage it
	dt.fetchDurationButton.Disable()

	_ = dt.downloader.SetTrimTimes("", "") // Clear trim in downloader
}

// Note: formatDurationSeconds has been moved to the utils package.
// See internal/gui/utils/utils.go for FormatDurationSeconds.

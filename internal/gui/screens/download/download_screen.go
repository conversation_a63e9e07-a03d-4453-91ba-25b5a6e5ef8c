// File: internal/gui/tabs/download_tab.go

package download

import (
	"fmt"
	"strings"
	"sync" // Import sync package for mutex

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common" // Ensure common is imported
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/widgets"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/widget"
)

// DownloadTab holds the UI elements and logic for the download tab.
type DownloadTab struct {
	// --- Dependencies ---
	app                common.AppInterface // <<< Added app field
	window             fyne.Window
	cfgManager         *config.Manager
	downloader         *downloader.Downloader
	historyManager     *history.HistoryManager
	logger             *logger.Logger
	updateStatusFunc   func(string)
	refreshHistoryFunc func()
	previewArea        interface{} // Preview area for displaying video info

	// --- UI Elements ---
	urlEntry            *widget.Entry
	formatSelect        *widget.Select
	qualitySelect       *widget.Select
	pathLabel           *widget.Label
	pathButton          *widget.Button
	progress            *widget.ProgressBar
	startButton         *widget.Button
	cancelButton        *widget.Button
	sponsorCheck        *widget.Check
	fetchDurationButton *widget.Button // Button remains for manual trigger/fallback
	durationLabel       *widget.Label
	trimStartSlider     *widget.Slider
	trimEndSlider       *widget.Slider
	startTimeLabel      *widgets.TappableLabel // Use custom tappable label
	endTimeLabel        *widgets.TappableLabel // Use custom tappable label
	trimContainer       *fyne.Container

	// --- Labels needing language update ---
	formatLabel     *widget.Label
	qualityLabel    *widget.Label
	pathPrefixLabel *widget.Label
	trimTitleLabel  *widget.Label
	startTrimLabel  *widget.Label
	endTrimLabel    *widget.Label

	// --- State ---
	videoDurationSeconds      float64
	currentTrimStartSeconds   float64
	currentTrimEndSeconds     float64
	isUpdatingSlider          bool
	lastFetchedURLForDuration string     // Track URL for which duration was fetched
	isFetchingDuration        bool       // Flag to prevent concurrent fetches
	fetchMutex                sync.Mutex // Mutex to protect isFetchingDuration flag

	// --- Format fetching state ---
	availableFormats         []metadata.Format // Available formats for current video
	lastFetchedURLForFormats string            // Track URL for which formats were fetched
	isFetchingFormats        bool              // Flag to prevent concurrent format fetches
	formatFetchMutex         sync.Mutex        // Mutex to protect format fetching state

	// --- Root container ---
	content fyne.CanvasObject
}

// NewDownloadTab creates a new instance of the DownloadTab.
func NewDownloadTab(app common.AppInterface) *DownloadTab {
	dt := &DownloadTab{
		app:                       app, // <<< Store the app instance
		window:                    app.GetWindow(),
		cfgManager:                app.GetConfig().(*config.Manager),
		downloader:                app.GetDownloader().(*downloader.Downloader),
		historyManager:            app.GetHistory().(*history.HistoryManager),
		logger:                    app.GetLogger().(*logger.Logger),
		updateStatusFunc:          func(s string) { app.UpdateStatus(s) }, // Use the app's update status
		refreshHistoryFunc:        func() {},
		videoDurationSeconds:      -1,
		currentTrimStartSeconds:   -1,
		currentTrimEndSeconds:     -1,
		isUpdatingSlider:          false,
		lastFetchedURLForDuration: "",    // Initialize empty
		isFetchingDuration:        false, // Initialize not fetching
	}
	// Ensure buildUI is called after all fields are set
	dt.buildUI() // buildUI now has access to dt.app
	return dt
}

// SetRefreshHistoryFunc sets the callback function to refresh the history tab.
func (dt *DownloadTab) SetRefreshHistoryFunc(refreshFunc func()) {
	if refreshFunc != nil {
		dt.refreshHistoryFunc = refreshFunc
	} else {
		// Ensure it's never nil, assign a no-op function if input is nil
		dt.refreshHistoryFunc = func() {}
	}
}

// Content returns the root container for the download tab.
func (dt *DownloadTab) Content() fyne.CanvasObject {
	return dt.content
}

// LoadURLs loads URLs into the URL entry
func (dt *DownloadTab) LoadURLs(urls []string) {
	if len(urls) == 0 || dt.urlEntry == nil { // Check if urlEntry is initialized
		return
	}

	// Set the first URL in the entry
	dt.urlEntry.SetText(urls[0])

	// If there are more URLs, show a message
	if len(urls) > 1 {
		// Use the new translation key
		dt.updateStatusFunc(i18n.Format("status_multiple_urls", len(urls)))
	} else {
		dt.updateStatusFunc(fmt.Sprintf(i18n.Get("status_url_loaded"), urls[0]))
	}
}

// UpdateLanguage updates the UI elements with the current language
func (dt *DownloadTab) UpdateLanguage() {
	if dt.urlEntry == nil { // Check if UI built
		return
	}
	// Update labels
	dt.formatLabel.SetText(i18n.Get("format"))
	dt.qualityLabel.SetText(i18n.Get("quality"))
	dt.pathPrefixLabel.SetText(i18n.Get("path") + ": ")
	dt.trimTitleLabel.SetText(i18n.Get("trim_video") + " (" + i18n.Get("single_url_only") + ")")
	dt.startTrimLabel.SetText(i18n.Get("start"))
	dt.endTrimLabel.SetText(i18n.Get("end"))

	dt.urlEntry.SetPlaceHolder(i18n.Get("enter_urls"))
	dt.formatSelect.PlaceHolder = i18n.Get("format_placeholder")
	dt.qualitySelect.PlaceHolder = i18n.Get("quality_placeholder")
	dt.pathButton.SetText(i18n.Get("button_browse"))
	dt.startButton.SetText(i18n.Get("button_start"))
	dt.cancelButton.SetText(i18n.Get("button_cancel"))
	dt.sponsorCheck.Text = i18n.Get("checkbox_sponsorblock") // Use Text field for Check
	dt.fetchDurationButton.SetText(i18n.Get("button_fetch_duration"))

	// Refresh widgets that might change text content or placeholder
	dt.urlEntry.Refresh()
	dt.formatSelect.Refresh()
	dt.qualitySelect.Refresh()
	dt.pathLabel.Refresh() // Path itself doesn't change, but prefix does
	dt.pathButton.Refresh()
	dt.startButton.Refresh()
	dt.cancelButton.Refresh()
	dt.sponsorCheck.Refresh()
	dt.fetchDurationButton.Refresh()
	dt.durationLabel.Refresh() // Content might change based on state + language
	dt.startTimeLabel.Refresh()
	dt.endTimeLabel.Refresh()
	dt.formatLabel.Refresh()
	dt.qualityLabel.Refresh()
	dt.pathPrefixLabel.Refresh()
	dt.trimTitleLabel.Refresh()
	dt.startTrimLabel.Refresh()
	dt.endTrimLabel.Refresh()
}

// UpdateSponsorBlockCheckbox updates the SponsorBlock checkbox state without triggering its OnChanged event
func (dt *DownloadTab) UpdateSponsorBlockCheckbox(enabled bool) {
	if dt.sponsorCheck == nil { // Check if UI built
		return
	}
	// Only update if the state is different to avoid unnecessary refreshes
	if dt.sponsorCheck.Checked != enabled {
		// Temporarily store the OnChanged callback
		originalCallback := dt.sponsorCheck.OnChanged

		// Set OnChanged to nil to prevent the callback from being triggered
		dt.sponsorCheck.OnChanged = nil

		// Update the checkbox state
		dt.sponsorCheck.SetChecked(enabled)

		// Restore the original callback
		dt.sponsorCheck.OnChanged = originalCallback
	}
}

// SetURLs updates the URL entry text. Used by history loading.
func (dt *DownloadTab) SetURLs(urls []string) {
	if dt.urlEntry != nil {
		dt.urlEntry.SetText(strings.Join(urls, "\n"))
		// Trigger OnChanged manually to update trim UI state and potentially auto-fetch
		if dt.urlEntry.OnChanged != nil {
			dt.urlEntry.OnChanged(dt.urlEntry.Text)
		}
	}
}

// Refresh triggers a data refresh (if applicable) for the tab. Currently no-op.
func (dt *DownloadTab) Refresh() {
	// If there were data lists or other elements needing explicit refresh, do it here.
	// Currently, Fyne widgets handle most refreshes automatically.
}

// startFormatFetch initiates fetching available formats for a URL
func (dt *DownloadTab) startFormatFetch(url string) {
	if dt.logger != nil {
		dt.logger.Info(fmt.Sprintf("Starting format fetch for URL: %s", url))
	}

	dt.formatFetchMutex.Lock()
	if dt.isFetchingFormats {
		dt.formatFetchMutex.Unlock()
		if dt.logger != nil {
			dt.logger.Info("Format fetch already in progress, skipping")
		}
		return // Already fetching
	}
	dt.isFetchingFormats = true
	dt.formatFetchMutex.Unlock()

	// Launch the actual fetch in a goroutine
	go func() {
		defer func() {
			dt.formatFetchMutex.Lock()
			dt.isFetchingFormats = false
			dt.formatFetchMutex.Unlock()
		}()

		// Fetch formats
		if dt.logger != nil {
			dt.logger.Info(fmt.Sprintf("Fetching formats from yt-dlp for: %s", url))
		}
		formats, err := dt.downloader.GetFormats(url)
		if err != nil {
			if dt.logger != nil {
				dt.logger.Error(fmt.Sprintf("Failed to fetch formats for %s: %v", url, err))
			}
			// On error, reset to default formats and update UI on main thread
			dt.formatFetchMutex.Lock()
			dt.availableFormats = nil
			dt.lastFetchedURLForFormats = "" // Reset so it can be retried
			dt.formatFetchMutex.Unlock()

			// Update dropdown to show only favorites on main thread
			if dt.formatSelect != nil {
				dt.formatSelect.Options = []string{"MP4", "MP3", "M4A"}
				dt.formatSelect.Refresh()
				if dt.formatSelect.Selected == "" {
					dt.formatSelect.SetSelected("MP4")
				}
			}
			return
		}

		// Update the state
		dt.formatFetchMutex.Lock()
		dt.availableFormats = formats
		dt.lastFetchedURLForFormats = url
		dt.formatFetchMutex.Unlock()

		if dt.logger != nil {
			dt.logger.Info(fmt.Sprintf("Successfully fetched %d formats for %s", len(formats), url))
		}

		// Update the format dropdown directly (we're already in a goroutine)
		if dt.formatSelect != nil {
			// Create the options list
			favoriteFormats := []string{"MP4", "MP3", "M4A"}
			var allOptions []string
			allOptions = append(allOptions, favoriteFormats...)

			if len(formats) > 0 {
				allOptions = append(allOptions, "--- Available Formats ---")
				filteredFormats := dt.filterUsefulFormats(formats)

				for _, format := range filteredFormats {
					if format.FormatID != "" {
						description := dt.formatDescription(format)
						allOptions = append(allOptions, description)
					}
				}
			}

			// Update the dropdown
			currentSelection := dt.formatSelect.Selected
			dt.formatSelect.Options = allOptions
			dt.formatSelect.Refresh()

			// Restore selection if valid, otherwise select MP4
			validSelection := false
			for _, option := range allOptions {
				if option == currentSelection {
					validSelection = true
					break
				}
			}

			if validSelection {
				dt.formatSelect.SetSelected(currentSelection)
			} else {
				dt.formatSelect.SetSelected("MP4")
			}

			if dt.logger != nil {
				dt.logger.Info(fmt.Sprintf("Format dropdown updated with %d options", len(allOptions)))
			}
		}
	}()
}

// formatDescription creates a user-friendly description for a format
func (dt *DownloadTab) formatDescription(format metadata.Format) string {
	description := format.FormatID

	if format.Extension != "" {
		description += " (" + format.Extension

		// Add resolution info
		if format.Resolution != "" && format.Resolution != "audio only" {
			description += ", " + format.Resolution
		} else if format.Resolution == "audio only" || format.Vcodec == "none" {
			description += ", audio"
		} else if format.Height > 0 {
			description += fmt.Sprintf(", %dp", format.Height)
		}

		// Add codec info if available and useful
		if format.Vcodec != "" && format.Vcodec != "none" && format.Vcodec != "unknown" {
			codecShort := format.Vcodec
			// Simplify common codec names
			if strings.Contains(codecShort, "avc1") {
				codecShort = "h264"
			} else if strings.Contains(codecShort, "vp9") {
				codecShort = "vp9"
			} else if strings.Contains(codecShort, "av01") {
				codecShort = "av1"
			}
			if len(codecShort) > 8 {
				codecShort = codecShort[:8]
			}
			description += ", " + codecShort
		}

		description += ")"
	}

	// Ensure description isn't too long for the dropdown
	if len(description) > 60 {
		description = description[:57] + "..."
	}

	return description
}

// filterUsefulFormats filters and sorts formats to show the most useful ones
func (dt *DownloadTab) filterUsefulFormats(formats []metadata.Format) []metadata.Format {
	var filtered []metadata.Format

	// Separate video and audio formats
	var videoFormats []metadata.Format
	var audioFormats []metadata.Format

	for _, format := range formats {
		if format.Vcodec == "none" || format.Resolution == "audio only" {
			// Audio-only format
			audioFormats = append(audioFormats, format)
		} else if format.Acodec != "none" && format.Height > 0 {
			// Video format with audio
			videoFormats = append(videoFormats, format)
		}
	}

	// Add best video formats (limit to reasonable resolutions)
	for _, format := range videoFormats {
		if format.Height >= 240 && format.Height <= 2160 { // 240p to 4K
			// Prefer common formats
			if format.Extension == "mp4" || format.Extension == "webm" ||
				strings.Contains(format.Vcodec, "avc1") || strings.Contains(format.Vcodec, "vp9") {
				filtered = append(filtered, format)
			}
		}
	}

	// Add best audio formats
	for _, format := range audioFormats {
		if format.Extension == "m4a" || format.Extension == "webm" || format.Extension == "mp3" {
			filtered = append(filtered, format)
		}
	}

	// Limit total number to avoid overwhelming the user
	if len(filtered) > 20 {
		filtered = filtered[:20]
	}

	return filtered
}

// isCustomFormat checks if a format selection is a custom format (not a favorite)
func (dt *DownloadTab) isCustomFormat(selectedFormat string) bool {
	favoriteFormats := []string{"MP4", "MP3", "M4A", "--- Available Formats ---"}
	for _, favorite := range favoriteFormats {
		if selectedFormat == favorite {
			return false
		}
	}
	return true
}

// getFormatIDFromSelection extracts the format ID from a custom format selection
func (dt *DownloadTab) getFormatIDFromSelection(selectedFormat string) string {
	if !dt.isCustomFormat(selectedFormat) {
		return selectedFormat // Return as-is for favorite formats
	}

	// Extract format ID from the description (format ID is before the first space or parenthesis)
	parts := strings.Fields(selectedFormat)
	if len(parts) > 0 {
		return parts[0]
	}

	return selectedFormat
}

// TestFormatFetching is a helper method to test format fetching functionality
func (dt *DownloadTab) TestFormatFetching() {
	if dt.logger != nil {
		dt.logger.Info("Testing format fetching functionality...")
	}

	// Test with a known YouTube URL
	testURL := "https://www.youtube.com/watch?v=dQw4w9WgXcQ" // Rick Roll - should always be available
	dt.startFormatFetch(testURL)
}

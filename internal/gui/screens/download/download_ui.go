// File: internal/gui/tabs/download_tab_ui.go

package download

import (
	"fmt"
	"math"
	"strings"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/preview"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/widgets"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/utils"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"
)

// buildUI creates and lays out the widgets for the download tab.
func (dt *DownloadTab) buildUI() {
	// --- URL Entry ---
	dt.urlEntry = widget.NewMultiLineEntry()
	dt.urlEntry.Wrapping = fyne.TextWrapWord
	dt.urlEntry.MultiLine = true
	dt.urlEntry.SetPlaceHolder(i18n.Get("enter_urls"))

	// --- Format and Quality Selection ---
	dt.formatLabel = widget.NewLabel(i18n.Get("format"))
	dt.qualityLabel = widget.NewLabel(i18n.Get("quality"))
	dt.qualitySelect = widget.NewSelect([]string{}, func(s string) {})

	// Create the formatSelect with the callback
	dt.formatSelect = widget.NewSelect([]string{"MP4", "MP3", "M4A"}, func(selectedFormat string) {
		var qualityKeys []string

		// Handle favorite formats
		switch selectedFormat {
		case "MP4":
			qualityKeys = []string{"360p", "720p", "1080p"}
		case "MP3", "M4A":
			qualityKeys = []string{"quality_128", "quality_highest"}
		case "--- Available Formats ---":
			// Separator, don't change quality options
			return
		default:
			// Check if it's a custom format
			if dt.isCustomFormat(selectedFormat) {
				// For custom formats, disable quality selection as format ID is specific
				qualityKeys = []string{"default"}
			} else {
				qualityKeys = []string{}
			}
		}

		translatedOptions := make([]string, len(qualityKeys))
		for i, key := range qualityKeys {
			if key == "default" {
				translatedOptions[i] = i18n.Get("default")
			} else {
				translatedOptions[i] = i18n.Get(key)
			}
		}
		dt.qualitySelect.Options = translatedOptions
		dt.qualitySelect.Refresh()
		if len(dt.qualitySelect.Options) > 0 {
			dt.qualitySelect.SetSelected(dt.qualitySelect.Options[0])
		} else {
			dt.qualitySelect.ClearSelected()
		}
	})
	dt.formatSelect.SetSelected("MP4")
	// Try to give the select widget more space for dropdown
	dt.formatSelect.Resize(fyne.NewSize(200, 40))

	// --- Download Path ---
	dt.pathPrefixLabel = widget.NewLabel(i18n.Get("path") + ": ")
	dt.pathLabel = widget.NewLabel(dt.cfgManager.GetDownloadPath())
	dt.pathLabel.Alignment = fyne.TextAlignLeading
	dt.pathLabel.Wrapping = fyne.TextTruncate
	pathContent := container.NewMax(dt.pathLabel)
	dt.pathButton = widget.NewButton(i18n.Get("select_path"), func() {
		dialog.ShowFolderOpen(func(dir fyne.ListableURI, err error) {
			if err != nil {
				dialog.ShowError(err, dt.window)
				return
			}
			if dir != nil {
				newPath := dir.Path()
				if err := dt.cfgManager.SetDownloadPath(newPath); err != nil {
					dialog.ShowError(fmt.Errorf("failed to save download path: %v", err), dt.window)
					return
				}
				dt.pathLabel.SetText(newPath)
			}
		}, dt.window)
	})
	pathContainer := container.NewBorder(nil, nil, dt.pathPrefixLabel, dt.pathButton, pathContent)

	// --- Progress Bar ---
	dt.progress = widget.NewProgressBar()

	// --- Download/Cancel Buttons ---
	dt.cancelButton = widget.NewButton(i18n.Get("cancel"), func() {
		// Update UI first for responsiveness
		dt.progress.SetValue(0)
		dt.startButton.Enable()
		dt.cancelButton.Disable()
		dt.updateStatusFunc(i18n.Get("cancelling") + "...") // Show cancelling status

		// Call the downloader's cancel function
		if dt.downloader != nil {
			dt.downloader.CancelDownload()
			// Status update to "Cancelled" should happen via the downloader's status callback
			// during the download process when cancellation is detected.
			// dt.updateStatusFunc(i18n.Get("canceled")) // Redundant if downloader updates status
		} else {
			// Use the new translation key
			dt.updateStatusFunc(i18n.Get("error_downloader_not_ready"))
		}
	})
	dt.cancelButton.Disable() // Start disabled
	dt.startButton = widget.NewButton(i18n.Get("start"), dt.handleDownload)

	// --- Options: SponsorBlock ---
	dt.sponsorCheck = widget.NewCheck(i18n.Get("remove_sponsor_segments"), func(enabled bool) {
		// Save to config
		err := dt.cfgManager.SetSponsorBlock(enabled)
		if err != nil {
			dialog.ShowError(fmt.Errorf("failed to save SponsorBlock setting: %v", err), dt.window)
			return
		}

		// Update downloader
		dt.downloader.SetSponsorBlock(enabled)

		// Update status
		stateStr := i18n.Get("disabled")
		if enabled {
			stateStr = i18n.Get("enabled")
		}
		dt.updateStatusFunc(fmt.Sprintf(i18n.Get("status_sponsorblock"), stateStr))
	})

	// Initialize checkbox state from config
	dt.sponsorCheck.SetChecked(dt.cfgManager.GetSponsorBlock())

	// --- Options: Trimming ---
	dt.trimTitleLabel = widget.NewLabel(i18n.Get("trim_video") + " (" + i18n.Get("single_url_only") + ")")
	dt.durationLabel = widget.NewLabel(i18n.Get("duration") + ": " + i18n.Get("fetch_to_enable_trim"))
	dt.durationLabel.TextStyle = fyne.TextStyle{Italic: true}
	// Button now acts as a manual trigger/fallback
	dt.fetchDurationButton = widget.NewButton(i18n.Get("fetch_duration"), dt.handleFetchDurationClick)
	dt.fetchDurationButton.Disable() // Start disabled, enabled only if fetch needed and not running

	// --- Trimming Sliders and TAPPABLE Labels ---
	dt.startTrimLabel = widget.NewLabel(i18n.Get("start"))
	// Use the custom TappableLabel widget
	dt.startTimeLabel = widgets.NewTappableLabel("0:00", func() {
		dt.showTimeInputDialog(true) // Show dialog for start time
	})

	dt.trimStartSlider = widget.NewSlider(0, 1)
	dt.trimStartSlider.Step = 1.0
	dt.trimStartSlider.Disable()
	dt.trimStartSlider.OnChanged = func(value float64) {
		if dt.isUpdatingSlider || dt.videoDurationSeconds < 0 {
			return
		}
		clampedValue := math.Min(value, dt.currentTrimEndSeconds)
		if clampedValue != value {
			dt.isUpdatingSlider = true
			dt.trimStartSlider.SetValue(clampedValue)
			dt.isUpdatingSlider = false
			value = clampedValue
		}
		if value != dt.currentTrimStartSeconds {
			dt.currentTrimStartSeconds = value
			dt.startTimeLabel.SetText(utils.FormatDurationSeconds(value)) // Update label display
			dt.applyTrimSettingsInternal()
		}
	}

	dt.endTrimLabel = widget.NewLabel(i18n.Get("end"))
	// Use the custom TappableLabel widget
	dt.endTimeLabel = widgets.NewTappableLabel("0:00", func() {
		dt.showTimeInputDialog(false) // Show dialog for end time
	})

	dt.trimEndSlider = widget.NewSlider(0, 1)
	dt.trimEndSlider.Step = 1.0
	dt.trimEndSlider.Disable()
	dt.trimEndSlider.OnChanged = func(value float64) {
		if dt.isUpdatingSlider || dt.videoDurationSeconds < 0 {
			return
		}
		clampedValue := math.Max(value, dt.currentTrimStartSeconds)
		if clampedValue != value {
			dt.isUpdatingSlider = true
			dt.trimEndSlider.SetValue(clampedValue)
			dt.isUpdatingSlider = false
			value = clampedValue
		}
		if value != dt.currentTrimEndSeconds {
			dt.currentTrimEndSeconds = value
			dt.endTimeLabel.SetText(utils.FormatDurationSeconds(value)) // Update label display
			dt.applyTrimSettingsInternal()
		}
	}

	// Layout for trimming controls, using TappableLabel directly
	trimControls := container.NewVBox(
		container.NewHBox(dt.fetchDurationButton, dt.durationLabel),
		container.New(layout.NewGridLayout(3),
			dt.startTrimLabel,
			dt.trimStartSlider,
			dt.startTimeLabel, // Use custom widget instance
		),
		container.New(layout.NewGridLayout(3),
			dt.endTrimLabel,
			dt.trimEndSlider,
			dt.endTimeLabel, // Use custom widget instance
		),
	)

	dt.trimContainer = container.NewVBox(dt.trimTitleLabel, trimControls)

	// NOTE: OnChanged handler is set in SetPreviewArea() to include all functionality

	// --- Final Layout ---
	optionsContainer := container.NewVBox(dt.sponsorCheck, widget.NewSeparator(), dt.trimContainer)
	paddedOptionsContainer := container.NewPadded(optionsContainer)
	mainContent := container.NewVBox(
		dt.urlEntry,
		container.NewHBox(dt.formatLabel, dt.formatSelect, layout.NewSpacer(), dt.qualityLabel, dt.qualitySelect),
		pathContainer,
		widget.NewSeparator(),
		paddedOptionsContainer,
		widget.NewSeparator(),
		dt.progress,
		container.NewHBox(layout.NewSpacer(), dt.startButton, dt.cancelButton, layout.NewSpacer()),
	)

	dt.content = mainContent
}

// SetPreviewArea sets the preview area for displaying video information
func (dt *DownloadTab) SetPreviewArea(previewArea interface{}) {
	dt.previewArea = previewArea

	// Set the complete OnChanged handler (this will overwrite the one from buildUI)
	if dt.urlEntry != nil {
		dt.urlEntry.OnChanged = func(text string) {
			// Determine current single URL
			currentURL := ""
			lines := strings.Split(strings.TrimSpace(text), "\n")
			validURLs := 0
			for _, line := range lines {
				trimmed := strings.TrimSpace(line)
				if trimmed != "" {
					validURLs++
					currentURL = trimmed // Store the last valid one
				}
			}

			// Manage Preview Area visibility and content
			if validURLs == 1 && currentURL != "" {
				if pa, ok := dt.previewArea.(*preview.PreviewArea); ok && pa != nil {
					pa.Show()
					// Pass the actual app instance (dt.app) to ShowVideoPreview
					preview.ShowVideoPreview(dt.app, currentURL, pa) // <<< Pass dt.app
				}
			} else {
				// Hide preview if not exactly one valid URL
				if pa, ok := dt.previewArea.(*preview.PreviewArea); ok && pa != nil {
					pa.Hide()
				}
			}

			// Lock access to fetch state flags for trim UI update
			dt.fetchMutex.Lock()
			isFetching := dt.isFetchingDuration
			lastFetched := dt.lastFetchedURLForDuration
			dt.fetchMutex.Unlock() // Unlock early

			// Lock access to format fetch state flags
			dt.formatFetchMutex.Lock()
			isFetchingFormats := dt.isFetchingFormats
			lastFetchedFormats := dt.lastFetchedURLForFormats
			dt.formatFetchMutex.Unlock() // Unlock early

			// Manage format fetching for single URLs
			if validURLs == 1 && currentURL != "" {
				if dt.logger != nil {
					dt.logger.Info(fmt.Sprintf("URL change detected: %s (last fetched: %s, fetching: %v)", currentURL, lastFetchedFormats, isFetchingFormats))
				}
				if currentURL != lastFetchedFormats && !isFetchingFormats {
					// New URL, start fetching formats
					if dt.logger != nil {
						dt.logger.Info(fmt.Sprintf("Triggering format fetch for new URL: %s", currentURL))
					}
					dt.startFormatFetch(currentURL)
				}
			} else {
				if dt.logger != nil && validURLs != 1 {
					dt.logger.Info(fmt.Sprintf("Not fetching formats: validURLs=%d, currentURL='%s'", validURLs, currentURL))
				}
			}

			// Format fetching will update the dropdown when complete

			// Manage Trim UI state
			if validURLs == 1 {
				if currentURL != lastFetched {
					// New URL, need to fetch (if not already fetching)
					if !isFetching {
						dt.resetTrimUI(i18n.Get("fetching")) // Reset UI with fetching indicator
						dt.startDurationFetch(currentURL)    // Start fetch automatically
						// Button is disabled within startDurationFetch
					} else {
						// Fetch is already in progress for a *different* URL
						dt.resetTrimUI(i18n.Get("waiting_for_fetch")) // Show waiting state
						dt.fetchDurationButton.Disable()              // Can't manually trigger now
					}
				} else {
					// URL is the same as the last successfully fetched one
					// Ensure trim UI is enabled (might have been disabled if user cleared/pasted)
					if dt.videoDurationSeconds >= 0 { // Check if fetch was actually successful
						dt.trimStartSlider.Enable()
						dt.trimEndSlider.Enable()
						dt.fetchDurationButton.Disable() // Already fetched, disable manual button
					} else {
						// Last fetch failed, allow manual retry
						dt.resetTrimUI(i18n.Get("error_fetching")) // Show error state
						if !isFetching {                           // Only enable button if not actively fetching
							dt.fetchDurationButton.Enable()
						} else {
							dt.fetchDurationButton.Disable()
						}
					}
				}
			} else {
				// Not exactly one URL
				dt.resetTrimUI(i18n.Get("fetch_to_enable_trim"))
				dt.fetchDurationButton.Disable() // Disable button if not single URL
			}
		}
	}
}

// UpdateStatus is a method for the DownloadTab to update its own status label, if it had one.
// Currently, status updates are handled globally by the App.
// Add a statusLabel field to DownloadTab if you want tab-specific status.
func (dt *DownloadTab) UpdateStatus(status string) {
	// If dt had its own statusLabel:
	// dt.statusLabel.SetText(status)

	// For now, delegate to the app-level status update
	if dt.updateStatusFunc != nil {
		dt.updateStatusFunc(status)
	} else {
		fmt.Println("DownloadTab Status:", status) // Fallback if app func is nil
	}
}

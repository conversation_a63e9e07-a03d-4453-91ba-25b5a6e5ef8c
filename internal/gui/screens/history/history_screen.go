// File: internal/gui/tabs/history_tab.go

package history

import (
	"errors"
	"fmt"
	"sort"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"

	// "fyne.io/fyne/v2/theme" // Theme import no longer needed here
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
)

// HistoryTab holds the UI elements and logic for the history tab.
type HistoryTab struct {
	// --- Dependencies ---
	window           fyne.Window
	historyManager   *history.HistoryManager
	logger           *logger.Logger
	loadURLFunc      func([]string) // Callback to load URLs into download tab
	updateStatusFunc func(string)   // Callback to update shared status

	// --- UI Elements ---
	table       *widget.Table
	loadButton  *widget.Button
	openButton  *widget.Button // New button to open URL in download tab
	clearButton *widget.Button

	// --- State ---
	lastSelectedRow int // Tracks the index in the *sorted* data source

	// --- Root container ---
	content fyne.CanvasObject
}

// NewHistoryTab creates a new instance of the HistoryTab.
func NewHistoryTab(app common.AppInterface) *HistoryTab {
	ht := &HistoryTab{
		window:           app.GetWindow(),
		historyManager:   app.GetHistory().(*history.HistoryManager),
		logger:           app.GetLogger().(*logger.Logger),
		loadURLFunc:      func(urls []string) {},
		updateStatusFunc: func(s string) { app.UpdateStatus(s) },
		lastSelectedRow:  -1, // No selection initially
	}
	ht.buildUI()
	return ht
}

// Content returns the root container for the history tab.
func (ht *HistoryTab) Content() fyne.CanvasObject {
	return ht.content
}

// buildUI creates and lays out the widgets for the history tab.
func (ht *HistoryTab) buildUI() {
	ht.table = widget.NewTable(
		ht.tableDataLength, // Length func
		ht.tableCreateCell, // Create func
		ht.tableUpdateCell, // Update func
	)

	// Store selection
	ht.table.OnSelected = func(id widget.TableCellID) {
		if id.Row > 0 { // Ignore header row selection
			ht.lastSelectedRow = id.Row - 1
			ht.openButton.Enable()
			ht.loadButton.Enable()
		} else {
			ht.lastSelectedRow = -1
			ht.table.Unselect(id)
			ht.openButton.Disable()
			ht.loadButton.Disable()
		}
	}

	// Adjust column widths to prevent overlap - use more compact widths
	ht.table.SetColumnWidth(0, 200) // Title
	ht.table.SetColumnWidth(1, 60)  // Format
	ht.table.SetColumnWidth(2, 80)  // Quality
	ht.table.SetColumnWidth(3, 300) // Path
	ht.table.SetColumnWidth(4, 120) // Date
	ht.table.SetColumnWidth(5, 200) // URL

	// --- Buttons ---
	ht.openButton = widget.NewButton(i18n.Get("open_in_download_tab"), ht.openInDownloadTab)
	ht.openButton.Importance = widget.HighImportance
	ht.openButton.Disable() // Initially disabled until selection

	ht.loadButton = widget.NewButton(i18n.Get("load_selected"), ht.loadSelection)
	ht.loadButton.Disable() // Initially disabled until selection

	ht.clearButton = widget.NewButton(i18n.Get("clear_history"), ht.clearHistory)
	ht.clearButton.Importance = widget.DangerImportance

	// --- Layout ---
	buttonContainer := container.NewHBox(layout.NewSpacer(), ht.openButton, ht.loadButton, ht.clearButton)
	ht.content = container.NewBorder(nil, buttonContainer, nil, nil, ht.table)
}

// getSortedRecords retrieves and sorts history records for display.
func (ht *HistoryTab) getSortedRecords() []history.DownloadRecord {
	records := ht.historyManager.GetRecords()
	sort.SliceStable(records, func(i, j int) bool {
		if records[i].Timestamp.IsZero() && !records[j].Timestamp.IsZero() {
			return false
		}
		if !records[i].Timestamp.IsZero() && records[j].Timestamp.IsZero() {
			return true
		}
		return records[i].Timestamp.After(records[j].Timestamp)
	})
	return records
}

// tableDataLength returns the number of rows and columns for the table.
func (ht *HistoryTab) tableDataLength() (int, int) {
	return len(ht.historyManager.GetRecords()) + 1, 6 // 6 columns
}

// tableCreateCell creates a template cell for the table.
func (ht *HistoryTab) tableCreateCell() fyne.CanvasObject {
	label := widget.NewLabel("")
	label.Truncation = fyne.TextTruncateEllipsis
	label.Alignment = fyne.TextAlignLeading
	return label
}

// truncateText handles text truncation for different types of content
func truncateText(text string, maxLength int, column int) string {
	if len(text) <= maxLength {
		return text
	}

	// For path and URL columns, show the end part which is often more important
	if column == 3 || column == 5 { // Path or URL column
		return "..." + text[len(text)-(maxLength-3):]
	}

	// For other columns (like title), show the beginning
	return text[:maxLength-3] + "..."
}

// tableUpdateCell updates the content of a specific cell.
func (ht *HistoryTab) tableUpdateCell(id widget.TableCellID, cell fyne.CanvasObject) {
	label, ok := cell.(*widget.Label)
	if !ok {
		return
	}

	records := ht.getSortedRecords() // Get sorted records for consistent indexing

	if id.Row == 0 { // Header Row
		headers := []string{
			i18n.Get("title"), i18n.Get("format"), i18n.Get("quality"),
			i18n.Get("path"), i18n.Get("date"), i18n.Get("url"),
		}
		var headerText string
		if id.Col >= 0 && id.Col < len(headers) {
			headerText = headers[id.Col]
		} else {
			headerText = ""
		}
		label.SetText(headerText)
		label.TextStyle = fyne.TextStyle{Bold: true}
	} else { // Data Row
		recordIndex := id.Row - 1 // Adjust for header row
		var value string
		if recordIndex >= 0 && recordIndex < len(records) {
			record := records[recordIndex]
			switch id.Col {
			case 0: // Title
				value = truncateText(record.Title, 30, id.Col)
			case 1: // Format
				value = record.Format
			case 2: // Quality
				value = truncateText(record.Quality, 10, id.Col)
			case 3: // Path
				value = truncateText(record.Path, 40, id.Col)
			case 4: // Date
				if !record.Timestamp.IsZero() {
					value = record.Timestamp.Format("2006-01-02 15:04") // Shorter format
				} else {
					value = "N/A"
				}
			case 5: // URL
				value = truncateText(record.URL, 30, id.Col)
			default:
				value = ""
			}
		} else {
			value = "" // Should not happen with correct length func
		}
		label.SetText(value)
		label.TextStyle = fyne.TextStyle{} // Reset to normal style
	}
}

// --- Rest of the functions (loadSelection, openInDownloadTab, etc.) remain unchanged ---

// loadSelection gets the URL from the selected row and sends it to the download tab.
func (ht *HistoryTab) loadSelection() {
	if ht.lastSelectedRow >= 0 {
		records := ht.getSortedRecords()
		if ht.lastSelectedRow < len(records) {
			selectedURL := records[ht.lastSelectedRow].URL
			ht.loadURLFunc([]string{selectedURL}) // Use callback to send URL(s)
			ht.updateStatusFunc(fmt.Sprintf(i18n.Get("status_url_loaded"), selectedURL))
		} else {
			if ht.logger != nil {
				ht.logger.Info(fmt.Sprintf("Selected row index %d out of bounds (history size %d)", ht.lastSelectedRow, len(records)))
			}
			ht.lastSelectedRow = -1 // Reset selection
			ht.table.UnselectAll()
			ht.openButton.Disable()
			ht.loadButton.Disable()
		}
	} else {
		dialog.ShowInformation(i18n.Get("selection"), i18n.Get("select_history_item"), ht.window)
	}
}

// openInDownloadTab gets the URL from the selected row, sends it to the download tab, and switches to the download tab.
func (ht *HistoryTab) openInDownloadTab() {
	if ht.lastSelectedRow >= 0 {
		records := ht.getSortedRecords()
		if ht.lastSelectedRow < len(records) {
			selectedURL := records[ht.lastSelectedRow].URL
			ht.loadURLFunc([]string{selectedURL})
			ht.lastSelectedRow = -1
			ht.table.UnselectAll()
			ht.openButton.Disable()
			ht.loadButton.Disable()
		} else {
			if ht.logger != nil {
				ht.logger.Info(fmt.Sprintf("Selected row index %d out of bounds (history size %d)", ht.lastSelectedRow, len(records)))
			}
			ht.lastSelectedRow = -1 // Reset selection
			ht.table.UnselectAll()
			ht.openButton.Disable()
			ht.loadButton.Disable()
		}
	} else {
		dialog.ShowInformation(i18n.Get("selection"), i18n.Get("select_history_item"), ht.window)
	}
}

// clearHistory prompts the user and clears the download history if confirmed.
func (ht *HistoryTab) clearHistory() {
	dialog.ShowConfirm(
		i18n.Get("confirm_clear_history_title"),
		i18n.Get("confirm_clear_history_message"),
		func(confirm bool) {
			if confirm {
				if err := ht.historyManager.ClearHistory(); err != nil {
					errMsg := fmt.Sprintf("%s: %v", i18n.Get("error_clearing_history"), err)
					dialog.ShowError(errors.New(errMsg), ht.window)
					if ht.logger != nil {
						ht.logger.Error(fmt.Sprintf("Failed to clear history: %v", err))
					}
				} else {
					ht.Refresh()
					ht.updateStatusFunc(i18n.Get("history_cleared"))
					ht.lastSelectedRow = -1
					ht.openButton.Disable()
					ht.loadButton.Disable()
				}
			}
		},
		ht.window,
	)
}

// Refresh triggers a refresh of the history table data.
func (ht *HistoryTab) Refresh() {
	if ht.table != nil {
		ht.table.Refresh()
	}
}

// UpdateLanguage refreshes the text of UI elements in the history tab.
func (ht *HistoryTab) UpdateLanguage() {
	ht.openButton.SetText(i18n.Get("open_in_download_tab"))
	ht.loadButton.SetText(i18n.Get("load_selected"))
	ht.clearButton.SetText(i18n.Get("clear_history"))
	ht.Refresh() // Table headers are updated via tableUpdateCell during refresh
}

// RefreshHistory refreshes the history table
func (ht *HistoryTab) RefreshHistory() {
	ht.Refresh()
}

// SetLoadURLFunc sets the callback function for loading URLs into the download tab
func (ht *HistoryTab) SetLoadURLFunc(loadFunc func([]string)) {
	ht.loadURLFunc = loadFunc
}

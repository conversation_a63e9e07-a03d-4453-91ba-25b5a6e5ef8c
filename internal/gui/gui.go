// File: internal/gui/gui.go

package gui

import (
	"fmt"
	"os"
	"path/filepath"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/dialog"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/app"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// InitializeGUI creates and runs the application GUI
func InitializeGUI() {
	// Add panic recovery
	defer func() {
		if r := recover(); r != nil {
			fmt.Fprintf(os.Stderr, "PANIC in InitializeGUI: %v\n", r)
			os.Exit(1)
		}
	}()

	// Create the application
	fmt.Println("Creating application...")
	application := app.NewApp()
	if application == nil {
		fmt.Println("Failed to initialize application")
		os.Exit(1)
	}

	// Run the application
	fmt.Println("Running application...")
	application.Run()
}

// GetAppVersion returns the application version
func GetAppVersion() string {
	return "1.0.0" // Replace with actual version
}

// ShowErrorDialog shows an error dialog
func ShowErrorDialog(err error, window fyne.Window) {
	if err == nil {
		return
	}

	dialog.ShowError(err, window)
}

// ShowInfoDialog shows an information dialog
func ShowInfoDialog(title, message string, window fyne.Window) {
	dialog.ShowInformation(title, message, window)
}

// GetDownloadDirectory returns the default download directory
func GetDownloadDirectory() string {
	// Get user's home directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		// Fallback to current directory if home directory can't be determined
		cwd, cwdErr := os.Getwd()
		if cwdErr != nil {
			return "."
		}
		return cwd
	}

	// Use Downloads folder if it exists
	downloadsDir := filepath.Join(homeDir, "Downloads")
	if _, err := os.Stat(downloadsDir); err == nil {
		return downloadsDir
	}

	// Fallback to home directory
	return homeDir
}

// GetSupportedFormats returns the supported download formats
func GetSupportedFormats() []string {
	return []string{"MP4", "MP3", "M4A", "WEBM"}
}

// GetSupportedQualities returns the supported video qualities
func GetSupportedQualities() []string {
	return []string{"1080p", "720p", "480p", "360p", "240p", "144p"}
}

// GetSupportedLanguages returns the supported languages
func GetSupportedLanguages() map[string]string {
	return i18n.GetAvailableLanguages()
}

package sponsorblock

import (
	"fmt"

	"fyne.io/fyne/v2/dialog"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// ToggleSponsorBlock toggles SponsorBlock integration
func ToggleSponsorBlock(a common.AppInterface) {
	// Get config manager
	configManager := a.GetConfig().(*config.Manager)

	// Get current state from config
	currentState := configManager.GetSponsorBlock()

	// Toggle state
	newState := !currentState

	// Update config
	err := configManager.SetSponsorBlock(newState)
	if err != nil {
		dialog.ShowError(fmt.Errorf("Failed to save SponsorBlock setting: %v", err), a.GetWindow())
		return
	}

	// Update downloader
	downloader := a.GetDownloader().(*downloader.Downloader)
	downloader.SetSponsorBlock(newState)

	// Show confirmation
	stateStr := i18n.Get("disabled")
	if newState {
		stateStr = i18n.Get("enabled")
	}

	a.UpdateStatus(fmt.Sprintf(i18n.Get("status_sponsorblock"), stateStr))
	dialog.ShowInformation(i18n.Get("dialog_sponsorblock_title"), fmt.Sprintf(i18n.Get("dialog_sponsorblock_message"), stateStr), a.GetWindow())
}

// UpdateSponsorBlockCheckbox updates the SponsorBlock checkbox in the download tab
type SponsorBlockCheckboxUpdater interface {
	UpdateSponsorBlockCheckbox(enabled bool)
}

// UpdateSponsorBlockUI updates all UI elements related to SponsorBlock
func UpdateSponsorBlockUI(a common.AppInterface, checkboxUpdater SponsorBlockCheckboxUpdater) {
	// Get current SponsorBlock state from config
	configManager := a.GetConfig().(*config.Manager)
	enabled := configManager.GetSponsorBlock()
	
	// Update the downloader
	downloader := a.GetDownloader().(*downloader.Downloader)
	downloader.SetSponsorBlock(enabled)
	
	// Update the checkbox in the download tab if available
	if checkboxUpdater != nil {
		checkboxUpdater.UpdateSponsorBlockCheckbox(enabled)
	}
}

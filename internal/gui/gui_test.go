package gui

import (
	"os"
	"path/filepath"
	"testing"
)

// TestInitializeGUI tests initializing the GUI
func TestInitializeGUI(t *testing.T) {
	// Skip this test if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	// Set a flag to indicate we're testing
	os.Setenv("GOTUBE_TEST", "1")

	// We can't actually call InitializeGUI() because it would start the GUI
	// and block the test, but we can test that the package imports correctly
	t.Log("GUI package imports correctly")
}

// TestGetAppVersion tests the GetAppVersion function
func TestGetAppVersion(t *testing.T) {
	version := GetAppVersion()
	if version == "" {
		t.Error("GetAppVersion returned empty string")
	}
}

// TestGetDownloadDirectory tests the GetDownloadDirectory function
func TestGetDownloadDirectory(t *testing.T) {
	// Save original home directory
	originalHome := os.Getenv("HOME")
	defer os.Setenv("HOME", originalHome)

	// Test with a temporary directory
	tmpDir := t.TempDir()
	os.Setenv("HOME", tmpDir)

	// Create a Downloads directory
	downloadsDir := filepath.Join(tmpDir, "Downloads")
	err := os.Mkdir(downloadsDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create test Downloads directory: %v", err)
	}

	// Test that GetDownloadDirectory returns the Downloads directory
	result := GetDownloadDirectory()
	if result != downloadsDir {
		t.Errorf("Expected download directory %s, got %s", downloadsDir, result)
	}

	// Test without Downloads directory
	os.RemoveAll(downloadsDir)
	result = GetDownloadDirectory()
	if result != tmpDir {
		t.Errorf("Expected home directory %s, got %s", tmpDir, result)
	}
}

// TestGetSupportedFormats tests the GetSupportedFormats function
func TestGetSupportedFormats(t *testing.T) {
	formats := GetSupportedFormats()
	if len(formats) == 0 {
		t.Error("GetSupportedFormats returned empty slice")
	}

	// Check for expected formats
	expectedFormats := map[string]bool{
		"MP4":  false,
		"MP3":  false,
		"M4A":  false,
		"WEBM": false,
	}

	for _, format := range formats {
		if _, ok := expectedFormats[format]; ok {
			expectedFormats[format] = true
		} else {
			t.Errorf("Unexpected format: %s", format)
		}
	}

	// Check that all expected formats were found
	for format, found := range expectedFormats {
		if !found {
			t.Errorf("Expected format %s not found", format)
		}
	}
}

// TestGetSupportedQualities tests the GetSupportedQualities function
func TestGetSupportedQualities(t *testing.T) {
	qualities := GetSupportedQualities()
	if len(qualities) == 0 {
		t.Error("GetSupportedQualities returned empty slice")
	}

	// Check for expected qualities
	expectedQualities := map[string]bool{
		"1080p": false,
		"720p":  false,
		"480p":  false,
		"360p":  false,
		"240p":  false,
		"144p":  false,
	}

	for _, quality := range qualities {
		if _, ok := expectedQualities[quality]; ok {
			expectedQualities[quality] = true
		} else {
			t.Errorf("Unexpected quality: %s", quality)
		}
	}

	// Check that all expected qualities were found
	for quality, found := range expectedQualities {
		if !found {
			t.Errorf("Expected quality %s not found", quality)
		}
	}
}

// TestGetSupportedLanguages tests the GetSupportedLanguages function
func TestGetSupportedLanguages(t *testing.T) {
	languages := GetSupportedLanguages()
	if len(languages) == 0 {
		t.Error("GetSupportedLanguages returned empty map")
	}

	// Check that English is supported
	if _, ok := languages["en"]; !ok {
		t.Error("English language not found in supported languages")
	}
}

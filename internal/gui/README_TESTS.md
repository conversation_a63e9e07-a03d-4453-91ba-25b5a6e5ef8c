# Integration Tests for GoTube Video Downloader

This directory contains integration tests for the GoTube Video Downloader application. These tests verify that the different components of the application work together correctly and that the application behaves as expected in various scenarios.

## Test Files

### 1. `integration_test.go`

Basic integration tests that verify the core functionality of the application:

- `TestURLEntryToPreviewIntegration`: Tests the integration between URL entry and preview area
- `TestDownloadProcessIntegration`: Tests the download process integration
- `TestHistoryIntegration`: Tests the integration with the history tab
- `TestEndToEndFlow`: Tests the end-to-end flow from URL entry to download completion

### 2. `user_simulation_test.go`

Simulates a complete user interaction with the application:

- `TestUserSimulation`: Simulates a user entering a URL, downloading a video, checking the history, and downloading another video

### 3. `error_handling_test.go`

Tests how the application handles various error conditions:

- `TestErrorHandling`: Tests handling of invalid URLs, network errors, and download cancellation
- `TestMultipleURLs`: Tests how the application handles multiple URLs
- `TestTrimControls`: Tests the video trimming controls

### 4. `state_persistence_test.go`

Tests the application's state management and persistence:

- `TestConfigPersistence`: Tests saving and loading configuration settings
- `TestHistoryPersistence`: Tests saving and loading download history
- `TestLanguageChange`: Tests changing the application language
- `TestThemeChange`: Tests changing the application theme
- `TestDatabasePersistence`: Tests database operations and persistence

### 5. `performance_test.go`

Tests the application's performance and resource usage:

- `TestConcurrentDownloads`: Tests how the application handles multiple concurrent downloads
- `TestLargeHistoryPerformance`: Tests how the application handles a large number of history entries
- `TestLargeVideoFileHandling`: Tests how the application handles large video files
- `TestMemoryUsageDuringDownloads`: Tests memory usage during downloads
- `TestLongRunningDownload`: Tests how the application handles a long-running download

## Running the Tests

To run all tests:

```bash
go test -v ./internal/gui/...
```

To run only the basic integration tests:

```bash
go test -v ./internal/gui -run TestURLEntryToPreviewIntegration
```

To run tests in short mode (skipping long-running tests):

```bash
go test -v ./internal/gui/... -short
```

## Test Design

The tests use mock implementations of the core components to simulate the application behavior without actually downloading videos or accessing external services. This makes the tests fast, reliable, and independent of external factors.

### Mock Components

- `mockMetadataProvider`: Simulates the metadata provider that fetches video information
- `mockDownloader`: Simulates the downloader that downloads videos
- `mockConfig`: Simulates the configuration manager
- `mockApp`: Simulates the application with all its components

### Test Helpers

Several helper functions are provided to find UI components in the application:

- `findURLEntry`: Finds the URL entry field
- `findFormatSelect`: Finds the format selection dropdown
- `findQualitySelect`: Finds the quality selection dropdown
- `findCancelButton`: Finds the cancel button
- `findTrimStartSlider`: Finds the trim start slider
- `findTrimEndSlider`: Finds the trim end slider

## Notes

- Some tests may be skipped in short mode as they can take a long time to run
- The tests use a temporary directory for storing files, which is automatically cleaned up after the tests
- The tests simulate user interactions by directly calling methods on the UI components, as Fyne's test package doesn't fully support simulated UI interactions
- Some assertions are commented out as they would require access to internal state that is not exposed in the current implementation

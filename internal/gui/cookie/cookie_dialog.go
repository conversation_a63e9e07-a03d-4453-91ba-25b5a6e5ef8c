// File: internal/gui/cookie/cookie_dialog.go

package cookie

import (
	"fmt"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// ShowCookieImportDialog shows a dialog for importing cookies from text
func ShowCookieImportDialog(app common.AppInterface) {
	// Create a multiline entry for the cookie text
	cookieEntry := widget.NewMultiLineEntry()
	cookieEntry.SetPlaceHolder("# Netscape HTTP Cookie File\ndomain\tTRUE\tpath\tFALSE\texpires\tname\tvalue")
	cookieEntry.Wrapping = fyne.TextWrapWord
	cookieEntry.SetMinRowsVisible(10)

	// Create a help text
	helpText := widget.NewLabel(i18n.Get("cookie_import_help"))
	helpText.Wrapping = fyne.TextWrapWord

	// Create the content container
	content := container.NewVBox(
		helpText,
		widget.NewSeparator(),
		cookieEntry,
	)

	// Create the dialog
	dlg := dialog.NewCustomConfirm(
		i18n.Get("cookie_import_title"),
		i18n.Get("import"),
		i18n.Get("cancel"),
		content,
		func(confirmed bool) {
			if confirmed {
				cookieText := cookieEntry.Text
				if cookieText == "" {
					dialog.ShowError(fmt.Errorf(i18n.Get("cookie_import_empty")), app.GetWindow())
					return
				}

				// Add the Netscape header if it's missing
				if !strings.HasPrefix(cookieText, "# Netscape HTTP Cookie File") {
					cookieText = "# Netscape HTTP Cookie File\n" + cookieText
				}

				// Import the cookies
				err := app.SetCookiesFromText(cookieText)
				if err != nil {
					dialog.ShowError(fmt.Errorf(i18n.Get("cookie_import_error")+": %v", err), app.GetWindow())
					return
				}

				// Show success message
				dialog.ShowInformation(
					i18n.Get("cookie_import_success_title"),
					i18n.Get("cookie_import_success"),
					app.GetWindow(),
				)
			}
		},
		app.GetWindow(),
	)

	// Set the dialog size
	dlg.Resize(fyne.NewSize(600, 400))
	dlg.Show()
}
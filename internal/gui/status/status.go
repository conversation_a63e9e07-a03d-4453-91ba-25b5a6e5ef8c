// File: internal/gui/status/status.go

package status

import (
	"fmt"
	"sync"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
)

// StatusManager handles status updates and progress tracking
type StatusManager struct {
	app            common.AppInterface
	statusLabel    *widget.Label
	progressBar    *widget.ProgressBar
	statusMutex    sync.Mutex
	progressMutex  sync.Mutex
	lastUpdateTime time.Time
}

// NewStatusManager creates a new status manager
func NewStatusManager(app common.AppInterface, statusLabel *widget.Label, progressBar *widget.ProgressBar) *StatusManager {
	return &StatusManager{
		app:            app,
		statusLabel:    statusLabel,
		progressBar:    progressBar,
		lastUpdateTime: time.Now(),
	}
}

// UpdateStatus updates the status label
func (sm *StatusManager) UpdateStatus(status string) {
	sm.statusMutex.Lock()
	defer sm.statusMutex.Unlock()

	// Log the status
	loggerInstance := sm.app.GetLogger()
	if loggerInstance != nil {
		if logger, ok := loggerInstance.(*logger.Logger); ok {
			logger.Info("Status: %s", status)
		}
	}

	// Update the status label
	sm.statusLabel.SetText(status)

	// Update last update time
	sm.lastUpdateTime = time.Now()
}

// UpdateProgress updates the progress bar
func (sm *StatusManager) UpdateProgress(progress float64) {
	sm.progressMutex.Lock()
	defer sm.progressMutex.Unlock()

	// Update the progress bar
	sm.progressBar.SetValue(progress)
}

// ResetProgress resets the progress bar
func (sm *StatusManager) ResetProgress() {
	sm.progressMutex.Lock()
	defer sm.progressMutex.Unlock()

	// Reset the progress bar
	sm.progressBar.SetValue(0)
}

// ShowError shows an error dialog and updates the status
func (sm *StatusManager) ShowError(err error, title string) {
	if err == nil {
		return
	}

	// Update status
	sm.UpdateStatus(fmt.Sprintf("%s: %v", title, err))

	// Show error dialog
	dialog.ShowError(err, sm.app.GetWindow())
}

// ShowInfo shows an information dialog and updates the status
func (sm *StatusManager) ShowInfo(message, title string) {
	// Update status
	sm.UpdateStatus(message)

	// Show info dialog
	dialog.ShowInformation(title, message, sm.app.GetWindow())
}

// ShowConfirm shows a confirmation dialog
func (sm *StatusManager) ShowConfirm(message, title string, callback func(bool)) {
	// Show confirmation dialog
	dialog.ShowConfirm(title, message, callback, sm.app.GetWindow())
}

// ShowCustomConfirm shows a custom confirmation dialog
func (sm *StatusManager) ShowCustomConfirm(title, confirm, dismiss string, content fyne.CanvasObject, callback func(bool)) {
	// Show custom confirmation dialog
	dialog.ShowCustomConfirm(title, confirm, dismiss, content, callback, sm.app.GetWindow())
}

// CreateStatusBar creates a status bar with a status label and progress bar
func CreateStatusBar() (*widget.Label, *widget.ProgressBar, *fyne.Container) {
	// Create status label
	statusLabel := widget.NewLabel(i18n.Get("status_ready"))
	statusLabel.Alignment = fyne.TextAlignLeading

	// Create progress bar
	progressBar := widget.NewProgressBar()
	progressBar.Min = 0
	progressBar.Max = 1
	progressBar.SetValue(0)

	// Create status bar container
	statusBar := container.NewBorder(
		nil, nil, statusLabel, progressBar,
	)

	return statusLabel, progressBar, statusBar
}

// File: internal/gui/preview/preview.go

package preview

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"image/color"
	"os"
	"path/filepath"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"

	_ "image/jpeg" // Register JPEG format
	_ "image/png"  // Register PNG format

	_ "golang.org/x/image/webp" // Register WebP format
)

// ShowVideoPreview fetches and displays a video thumbnail and information, using cache first.
func ShowVideoPreview(a common.AppInterface, url string, previewArea *PreviewArea) {
	// Cache directories
	thumbnailCacheDir := filepath.Join("cache", "thumbnails")
	metadataCacheDir := filepath.Join("cache", "metadata")

	// Create cache directories if they don't exist
	_ = os.MkdirAll(thumbnailCacheDir, 0755) // Ignore errors for now
	_ = os.MkdirAll(metadataCacheDir, 0755)  // Ignore errors for now

	// Create a hash of the URL for the cache key
	urlHash := fmt.Sprintf("%x", md5.Sum([]byte(url)))
	metadataFile := filepath.Join(metadataCacheDir, urlHash+".json")
	thumbnailFilePattern := filepath.Join(thumbnailCacheDir, urlHash+".*") // Use pattern for various extensions

	// --- Attempt to load metadata from cache first ---
	var cachedMeta *metadata.VideoMetadata
	if data, err := os.ReadFile(metadataFile); err == nil {
		var meta metadata.VideoMetadata
		if err := json.Unmarshal(data, &meta); err == nil {
			cachedMeta = &meta
			a.UpdateStatus(fmt.Sprintf("Loaded cached metadata for: %s", meta.Title))
			previewArea.UpdateWithMetadata(cachedMeta) // Update text info immediately

			// --- Attempt to load thumbnail from cache ---
			// Search for cached thumbnail using pattern
			matches, _ := filepath.Glob(thumbnailFilePattern)
			if len(matches) > 0 {
				cachedThumbnailPath := matches[0] // Use the first match
				// Check if file is reasonably recent (e.g., < 7 days)
				fileInfo, statErr := os.Stat(cachedThumbnailPath)
				if statErr == nil && time.Since(fileInfo.ModTime()) < 7*24*time.Hour {
					// Load image asynchronously
					go func(thumbPath string) {
						img := LoadImage(thumbPath, 200, 150)
						if img != nil {
							previewArea.SetThumbnail(img)
						} else {
							// If cached image fails to load, trigger live fetch for thumbnail
							fmt.Printf("Warning: Failed to load cached thumbnail %s. Fetching live.\n", thumbPath)
							os.Remove(thumbPath) // Remove potentially corrupt cache file
							fetchLiveThumbnail(cachedMeta.Thumbnail, thumbnailCacheDir, previewArea)
						}
					}(cachedThumbnailPath)
					return // Cache hit for both metadata and thumbnail (or loading async)
				} else if statErr != nil {
					fmt.Printf("Warning: Failed to stat cached thumbnail %s: %v\n", cachedThumbnailPath, statErr)
				} else {
					fmt.Printf("Info: Cached thumbnail %s is older than 7 days. Will fetch live.\n", cachedThumbnailPath)
					os.Remove(cachedThumbnailPath) // Remove old thumbnail
				}
			}
			// Metadata cache hit, but thumbnail cache miss/old/invalid, fetch live thumbnail
			fetchLiveThumbnail(cachedMeta.Thumbnail, thumbnailCacheDir, previewArea)
			return // Metadata was from cache, only thumbnail needed fetching
		} else {
			fmt.Printf("Warning: Failed to parse cached metadata file %s: %v\n", metadataFile, err)
			os.Remove(metadataFile) // Remove corrupt cache file
		}
	}

	// --- Cache miss or invalid cache, fetch live data ---
	a.UpdateStatus(fmt.Sprintf("Fetching metadata for: %s", url))
	previewArea.ShowLoadingThumbnail() // Show loading in thumbnail area
	previewArea.ClearInfo()            // Clear text info while loading

	go func() {
		dlInterface := a.GetDownloader()
		if dlInterface == nil {
			a.UpdateStatus("Error: Downloader not available for metadata fetch")
			previewArea.Clear() // Clear preview on critical error
			return
		}
		dl, ok := dlInterface.(*downloader.Downloader)
		if !ok || dl == nil {
			a.UpdateStatus("Error: Invalid Downloader type for metadata fetch")
			previewArea.Clear() // Clear preview on critical error
			return
		}

		videoMeta, err := dl.MetadataProvider.Get(url)
		if err != nil {
			errMsg := fmt.Sprintf(i18n.Get("error_fetch_metadata"), err)
			a.UpdateStatus(errMsg)
			fmt.Println(errMsg) // Also print to console for debugging
			previewArea.Clear() // Clear preview on error
			return
		}

		// --- Live fetch successful ---
		a.UpdateStatus(fmt.Sprintf(i18n.Get("status_metadata_fetched"), videoMeta.Title))
		previewArea.UpdateWithMetadata(videoMeta)

		// Save fetched metadata to cache
		jsonData, jsonErr := json.MarshalIndent(videoMeta, "", "  ")
		if jsonErr == nil {
			writeErr := os.WriteFile(metadataFile, jsonData, 0644)
			if writeErr != nil {
				fmt.Printf("Warning: Failed to write metadata cache file %s: %v\n", metadataFile, writeErr)
			}
		} else {
			fmt.Printf("Warning: Failed to marshal metadata for caching: %v\n", jsonErr)
		}

		// Download and display thumbnail (asynchronously)
		fetchLiveThumbnail(videoMeta.Thumbnail, thumbnailCacheDir, previewArea)
	}()
}

// fetchLiveThumbnail downloads and sets the thumbnail image.
func fetchLiveThumbnail(thumbURL, cacheDir string, previewArea *PreviewArea) {
	if thumbURL == "" {
		previewArea.ClearThumbnail() // Clear if no thumbnail URL
		return
	}
	go func() {
		thumbnailPath, thumbErr := DownloadThumbnail(thumbURL, cacheDir)
		if thumbErr == nil {
			img := LoadImage(thumbnailPath, 200, 150)
			if img != nil {
				previewArea.SetThumbnail(img)
			} else {
				previewArea.ClearThumbnail() // Clear if image loading fails
			}
		} else {
			fmt.Printf("Error downloading/loading thumbnail after live fetch: %v\n", thumbErr)
			previewArea.ClearThumbnail() // Clear on download error
		}
	}()
}

// PreviewArea represents the UI component that displays video preview information
type PreviewArea struct {
	container          *fyne.Container
	thumbnailArea      *fyne.Container // Padded container for thumbnail stack
	thumbnailContainer *fyne.Container // Stack for image/placeholder
	infoArea           *fyne.Container // VBox for text info
	titleLabel         *widget.Label
	durationLabel      *widget.Label
	authorLabel        *widget.Label
	sizeLabel          *widget.Label
	isVisible          bool
}

// NewPreviewArea creates a new preview area
func NewPreviewArea(dl interface{}) *PreviewArea { // dl parameter kept for potential future use
	// Create labels with empty text
	titleLabel := widget.NewLabel("")
	titleLabel.Wrapping = fyne.TextWrapWord
	titleLabel.TextStyle = fyne.TextStyle{Bold: true}

	durationLabel := widget.NewLabel("")
	authorLabel := widget.NewLabel("")
	sizeLabel := widget.NewLabel("")

	// Create thumbnail area with fixed size container for alignment
	thumbPlaceholder := canvas.NewRectangle(color.Transparent) // Transparent placeholder
	thumbPlaceholder.SetMinSize(fyne.NewSize(200, 150))        // Desired thumbnail size
	thumbnailContainer := container.NewStack(thumbPlaceholder) // Use Stack to overlay image/loading indicator

	thumbnailArea := container.NewPadded(thumbnailContainer) // Add padding around the thumbnail
	thumbnailArea.Hide()                                     // Initially hidden

	// Create info area (already a VBox)
	infoArea := container.NewVBox(
		titleLabel,
		durationLabel,
		authorLabel,
		sizeLabel,
	)
	infoArea.Hide() // Initially hidden

	// Stack thumbnail on top of info area, separated by a line
	mainContainer := container.NewVBox(
		thumbnailArea,         // Thumbnail area (padded stack) at the top
		widget.NewSeparator(), // Visual separator
		infoArea,              // Text info below
	)

	mainContainer.Hide() // Initially hide the whole preview area

	return &PreviewArea{
		container:          mainContainer,
		thumbnailArea:      thumbnailArea,
		thumbnailContainer: thumbnailContainer,
		infoArea:           infoArea,
		titleLabel:         titleLabel,
		durationLabel:      durationLabel,
		authorLabel:        authorLabel,
		sizeLabel:          sizeLabel,
		isVisible:          false,
	}
}

// Container returns the fyne container for this preview area
func (p *PreviewArea) Container() *fyne.Container {
	return p.container
}

// UpdateWithMetadata updates the preview area with video metadata
func (p *PreviewArea) UpdateWithMetadata(meta *metadata.VideoMetadata) {
	if meta == nil {
		p.Clear() // Clear preview if metadata is nil
		return
	}

	// Fyne handles thread safety for these calls
	p.titleLabel.SetText(meta.Title)
	p.durationLabel.SetText(fmt.Sprintf("%s: %s", i18n.Get("duration"), formatDurationFromInt(meta.Duration)))
	p.authorLabel.SetText(fmt.Sprintf("%s: %s", i18n.Get("author"), meta.Author))

	if meta.Filesize > 0 {
		p.sizeLabel.SetText(fmt.Sprintf("%s: %s", i18n.Get("filesize"), formatFileSize(meta.Filesize)))
		p.sizeLabel.Show()
	} else {
		p.sizeLabel.Hide()
	}

	p.infoArea.Show()
	p.container.Show() // Ensure main container is visible
	p.isVisible = true
}

// ShowLoadingThumbnail displays a loading placeholder for the thumbnail
func (p *PreviewArea) ShowLoadingThumbnail() {
	// Fyne handles thread safety
	loadingText := widget.NewLabel("Loading Thumb...") // Shorter text
	loadingText.Alignment = fyne.TextAlignCenter

	// Use SetObjects to replace content in the stack
	p.thumbnailContainer.Objects = []fyne.CanvasObject{loadingText}
	p.thumbnailContainer.Refresh()
	p.thumbnailArea.Show() // Ensure the padded area is visible
	p.container.Show()     // Ensure main container is visible
	p.isVisible = true
}

// SetThumbnail sets the thumbnail image
func (p *PreviewArea) SetThumbnail(img *canvas.Image) {
	if img == nil {
		p.ClearThumbnail()
		return
	}
	// Fyne handles thread safety
	img.SetMinSize(fyne.NewSize(200, 150)) // Ensure image respects size
	img.FillMode = canvas.ImageFillContain // Contain ensures aspect ratio is kept

	// Use SetObjects to replace content in the stack
	p.thumbnailContainer.Objects = []fyne.CanvasObject{img}
	p.thumbnailContainer.Refresh()
	p.thumbnailArea.Show()
	p.container.Show()
	p.isVisible = true
}

// Hide hides the preview area
func (p *PreviewArea) Hide() {
	// Fyne handles thread safety
	p.container.Hide()
	p.isVisible = false
}

// Show shows the preview area
func (p *PreviewArea) Show() {
	// Fyne handles thread safety
	p.container.Show()
	p.isVisible = true
}

// IsVisible returns whether the preview area is visible
func (p *PreviewArea) IsVisible() bool {
	return p.isVisible
}

// Clear clears the preview area text and thumbnail
func (p *PreviewArea) Clear() {
	// Fyne handles thread safety
	p.ClearInfo()
	p.ClearThumbnail()
	// Optionally hide the whole container, or just leave empty space
	// p.Hide()
}

// ClearInfo clears only the text labels in the info area
func (p *PreviewArea) ClearInfo() {
	p.titleLabel.SetText("")
	p.durationLabel.SetText("")
	p.authorLabel.SetText("")
	p.sizeLabel.SetText("")
	p.infoArea.Hide() // Hide info area when cleared
}

// ClearThumbnail removes the image/loading indicator from the thumbnail area
func (p *PreviewArea) ClearThumbnail() {
	thumbPlaceholder := canvas.NewRectangle(color.Transparent)
	thumbPlaceholder.SetMinSize(fyne.NewSize(200, 150))
	p.thumbnailContainer.Objects = []fyne.CanvasObject{thumbPlaceholder} // Show placeholder
	p.thumbnailContainer.Refresh()
	p.thumbnailArea.Hide() // Hide the area when cleared
}

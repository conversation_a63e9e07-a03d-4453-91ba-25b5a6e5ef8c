// File: internal/gui/preview/utils.go

package preview

import (
	"fmt"
	"hash/fnv"
	"image"
	_ "image/jpeg" // Register JPEG format
	_ "image/png"  // Register PNG format
	// Import color for placeholder
	"io"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	_ "golang.org/x/image/webp" // Register WebP format

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
)

// Cache for thumbnail URLs to avoid redundant downloads
var thumbnailCache = make(map[string]string)
var cacheMutex sync.Mutex

// DownloadThumbnail downloads a thumbnail image from a URL
func DownloadThumbnail(url, cacheDir string) (string, error) {
	// Check in-memory cache first
	cacheMutex.Lock()
	if cachedPath, exists := thumbnailCache[url]; exists {
		// Verify the file still exists
		if _, err := os.Stat(cachedPath); err == nil {
			cacheMutex.Unlock()
			return cachedPath, nil
		}
		// If file doesn't exist, remove from cache and continue
		delete(thumbnailCache, url)
	}
	cacheMutex.Unlock()

	// Create a shorter hash of the URL for the filename
	h := fnv.New32a()
	h.Write([]byte(url))
	filename := fmt.Sprintf("%x.img", h.Sum32()) // Use a generic extension or detect mime type
	cachePath := filepath.Join(cacheDir, filename)

	// Check if the file already exists in cache
	if _, err := os.Stat(cachePath); err == nil {
		// Check if the file is not too old (e.g., less than 7 days)
		fileInfo, err := os.Stat(cachePath)
		if err == nil && time.Since(fileInfo.ModTime()) < 7*24*time.Hour {
			// Add to in-memory cache
			cacheMutex.Lock()
			thumbnailCache[url] = cachePath
			cacheMutex.Unlock()
			return cachePath, nil
		}
	}

	// Create cache directory if it doesn't exist
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create cache directory: %w", err)
	}

	// Set up HTTP client with timeout
	client := &http.Client{
		Timeout: 15 * time.Second, // Increased timeout for slow connections
	}

	// Download the image
	resp, err := client.Get(url)
	if err != nil {
		return "", fmt.Errorf("failed to download thumbnail: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to download thumbnail: HTTP status %d", resp.StatusCode)
	}

	// Create the file
	file, err := os.Create(cachePath)
	if err != nil {
		return "", fmt.Errorf("failed to create thumbnail file: %w", err)
	}
	defer file.Close()

	// Copy the image data to the file
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		// Attempt to remove partially written file on error
		os.Remove(cachePath)
		return "", fmt.Errorf("failed to save thumbnail: %w", err)
	}

	// Add to in-memory cache
	cacheMutex.Lock()
	thumbnailCache[url] = cachePath
	cacheMutex.Unlock()

	return cachePath, nil
}

// Cache for loaded images to avoid redundant decoding
var imageCache = make(map[string]*canvas.Image)
var imageCacheMutex sync.Mutex

// LoadImage loads an image from a file and returns a canvas.Image
func LoadImage(path string, width, height float32) *canvas.Image {
	// Check in-memory cache first
	imageCacheMutex.Lock()
	if cachedImg, exists := imageCache[path]; exists {
		imageCacheMutex.Unlock()
		// Create a new instance from the cached resource to avoid modifying shared state
		// This assumes the StaticResource part is immutable enough. For complex Images, might need deeper copy.
		// Re-apply settings as they might be widget specific
		newImg := canvas.NewImageFromResource(cachedImg.Resource)
		newImg.FillMode = canvas.ImageFillContain
		newImg.SetMinSize(fyne.NewSize(width, height))
		return newImg
	}
	imageCacheMutex.Unlock()

	// Open the image file
	file, err := os.Open(path)
	if err != nil {
		fmt.Printf("Error opening image file %s: %v\n", path, err)
		return nil
	}
	defer file.Close()

	// Decode the image config first to check dimensions if needed (optional)
	// _, format, err := image.DecodeConfig(file)
	// if err != nil { ... }
	// file.Seek(0, 0) // Reset after DecodeConfig

	// Decode the full image
	img, format, err := image.Decode(file)
	if err != nil {
		fmt.Printf("Error decoding image %s (format: %s): %v\n", path, format, err)
		// Attempt to remove potentially corrupt cache file
		// os.Remove(path)
		return nil
	}

	// Create a canvas image
	canvasImg := canvas.NewImageFromImage(img)
	canvasImg.FillMode = canvas.ImageFillContain
	canvasImg.SetMinSize(fyne.NewSize(width, height))

	// Add to in-memory cache (consider cache eviction strategy for large numbers of images)
	imageCacheMutex.Lock()
	imageCache[path] = canvasImg
	imageCacheMutex.Unlock()

	return canvasImg
}

// formatDurationFromInt formats a duration in seconds as a string
func formatDurationFromInt(seconds int) string {
	if seconds <= 0 {
		return "N/A"
	}
	hours := seconds / 3600
	minutes := (seconds % 3600) / 60
	secs := seconds % 60
	if hours > 0 {
		return fmt.Sprintf("%d:%02d:%02d", hours, minutes, secs)
	}
	return fmt.Sprintf("%d:%02d", minutes, secs)
}

// FormatDurationSeconds formats a duration in seconds (float64) as a "M:SS" string.
// Note: Consider consolidating duration formatting if needed elsewhere.
func FormatDurationSeconds(totalSeconds float64) string {
	if totalSeconds < 0 {
		return "N/A" // Indicate invalid or unavailable duration
	}
	// Round to nearest second before calculation
	totalSeconds = math.Round(totalSeconds)
	minutes := int(totalSeconds) / 60
	seconds := int(totalSeconds) % 60
	return fmt.Sprintf("%d:%02d", minutes, seconds)
}

// formatFileSize formats a file size in bytes as a human-readable string (e.g., KiB, MiB)
func formatFileSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	// Ensure exp doesn't go out of bounds for the unit string
	if exp >= len("KMGTPE") {
		exp = len("KMGTPE") - 1
	}
	return fmt.Sprintf("%.1f %ciB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

package preview

import (
	"fmt"
	"image"
	"image/color"
	"os"
	"path/filepath"
	"testing"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/test"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
)

// mockMetadataProvider implements a mock metadata provider for testing
type mockMetadataProvider struct {
	metadata map[string]*metadata.VideoMetadata
	err      error
}

func (m *mockMetadataProvider) Get(url string) (*metadata.VideoMetadata, error) {
	if m.err != nil {
		return nil, m.err
	}
	return m.metadata[url], nil
}

func (m *mockMetadataProvider) IsPlaylist(url string) (bool, error) {
	return false, nil
}

func (m *mockMetadataProvider) GetPlaylistMetadata(url string) (*metadata.PlaylistMetadata, error) {
	return nil, fmt.Errorf("not a playlist")
}

func (m *mockMetadataProvider) GetFormats(url string) ([]metadata.Format, error) {
	return []metadata.Format{
		{
			FormatID:   "22",
			Extension:  "mp4",
			Resolution: "720p",
			Height:     720,
			Width:      1280,
		},
	}, nil
}

func (m *mockMetadataProvider) SetUseBrowserCookies(enabled bool) {
	// No-op for mock
}

func (m *mockMetadataProvider) SetBrowserName(browser string) {
	// No-op for mock
}

// mockDownloader implements a mock downloader for testing
type mockDownloader struct {
	MetadataProvider *mockMetadataProvider
}

// enhancedMockApp is a more sophisticated mock of the AppInterface
type enhancedMockApp struct {
	window     fyne.Window
	logger     interface{}
	downloader interface{}
	config     interface{}
	database   interface{}
	status     string

	// Track method calls
	statusUpdates []string
}

func (m *enhancedMockApp) GetWindow() fyne.Window {
	return m.window
}

func (m *enhancedMockApp) GetLogger() interface{} {
	return m.logger
}

func (m *enhancedMockApp) GetDownloader() interface{} {
	return m.downloader
}

func (m *enhancedMockApp) GetHistory() interface{} {
	return nil
}

func (m *enhancedMockApp) GetConfig() interface{} {
	return m.config
}

func (m *enhancedMockApp) GetDatabase() interface{} {
	return m.database
}

func (m *enhancedMockApp) UpdateStatus(status string) {
	m.status = status
	m.statusUpdates = append(m.statusUpdates, status)
}

// Menu-related methods
func (m *enhancedMockApp) SelectCustomCookies()            {}
func (m *enhancedMockApp) SetCookiesFromText(string) error { return nil }
func (m *enhancedMockApp) SetUseBrowserCookies(bool)       {}
func (m *enhancedMockApp) SetBrowserName(string)           {}
func (m *enhancedMockApp) ShowFilenameTemplateDialog()     {}
func (m *enhancedMockApp) ShowLanguageDialog(onFinish func()) {
	if onFinish != nil {
		onFinish()
	}
}
func (m *enhancedMockApp) ShowAboutDialog()  {}
func (m *enhancedMockApp) OpenLogDirectory() {}
func (m *enhancedMockApp) ShowCookieGuide()  {}

// Encryption-related methods
func (m *enhancedMockApp) ShowPasswordDialog(onFinish func()) {
	if onFinish != nil {
		onFinish()
	}
}
func (m *enhancedMockApp) ShowExistingDatabasePasswordDialog(callback func(string)) {
	if callback != nil {
		callback("test-password")
	}
}
func (m *enhancedMockApp) ShowChangePasswordDialog() {}
func (m *enhancedMockApp) ShowEnableEncryptionDialog(onFinish func()) {
	if onFinish != nil {
		onFinish()
	}
}
func (m *enhancedMockApp) ShowDisableEncryptionDialog() {}
func (m *enhancedMockApp) UpdateEncryptionMenu()        {}

// TestPreviewAreaWithWindow tests the preview area in a window
func TestPreviewAreaWithWindow(t *testing.T) {
	// Create a test app
	app := test.NewApp()
	defer app.Quit()

	// Create a preview area
	previewArea := NewPreviewArea(nil)

	// Create a window with the preview area
	w := test.NewWindow(previewArea.Container())
	defer w.Close()
	w.Resize(fyne.NewSize(400, 300))

	// Force a render
	// Note: test.Sync() is not available in all versions of Fyne
	// We'll skip this step for compatibility

	// Initially the preview area should be hidden
	if previewArea.IsVisible() {
		t.Error("Expected preview area to be hidden initially")
	}

	// Create sample metadata
	meta := &metadata.VideoMetadata{
		ID:       "video123",
		Title:    "Test Video with a Very Long Title That Should Wrap to Multiple Lines",
		Author:   "Test Author",
		Duration: 120,
		Filesize: 1024 * 1024, // 1 MB
	}

	// Update the preview area with metadata
	previewArea.UpdateWithMetadata(meta)

	// Check that the preview area is visible
	if !previewArea.IsVisible() {
		t.Error("Expected preview area to be visible after UpdateWithMetadata()")
	}

	// Check that the title label has text
	if previewArea.titleLabel.Text == "" {
		t.Error("Expected title label to have text")
	}

	// Test showing loading thumbnail
	previewArea.ShowLoadingThumbnail()

	// Test clearing the preview area
	previewArea.Clear()

	// Check that the preview area is cleared
	if previewArea.titleLabel.Text != "" {
		t.Errorf("Expected title label to be empty after Clear(), got '%s'", previewArea.titleLabel.Text)
	}
}

// TestPreviewAreaWithDifferentMetadata tests the preview area with different metadata
func TestPreviewAreaWithDifferentMetadata(t *testing.T) {
	// Create a test app
	app := test.NewApp()
	defer app.Quit()

	// Create a preview area
	previewArea := NewPreviewArea(nil)

	// Create a window with the preview area
	w := test.NewWindow(previewArea.Container())
	defer w.Close()
	w.Resize(fyne.NewSize(400, 300))

	// Force a render
	// Note: test.Sync() is not available in all versions of Fyne
	// We'll skip this step for compatibility

	// Test with different metadata
	testCases := []struct {
		name     string
		metadata *metadata.VideoMetadata
	}{
		{
			name: "Normal video",
			metadata: &metadata.VideoMetadata{
				ID:       "video123",
				Title:    "Test Video",
				Author:   "Test Author",
				Duration: 120,
				Filesize: 1024 * 1024, // 1 MB
			},
		},
		{
			name: "Long title video",
			metadata: &metadata.VideoMetadata{
				ID:       "video456",
				Title:    "This is a very long title that should wrap to multiple lines and test the wrapping behavior of the title label in the preview area",
				Author:   "Another Author",
				Duration: 3600,              // 1 hour
				Filesize: 1024 * 1024 * 100, // 100 MB
			},
		},
		{
			name: "Video with no filesize",
			metadata: &metadata.VideoMetadata{
				ID:       "video789",
				Title:    "Video with no filesize",
				Author:   "Unknown Author",
				Duration: 60,
				Filesize: 0, // No filesize
			},
		},
		{
			name: "Video with very long duration",
			metadata: &metadata.VideoMetadata{
				ID:       "video101112",
				Title:    "Very long video",
				Author:   "Long Author",
				Duration: 36000,              // 10 hours
				Filesize: 1024 * 1024 * 1024, // 1 GB
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Update the preview area with metadata
			previewArea.UpdateWithMetadata(tc.metadata)

			// Check that the preview area is visible
			if !previewArea.IsVisible() {
				t.Error("Expected preview area to be visible after UpdateWithMetadata()")
			}

			// Check that the title label was updated
			if previewArea.titleLabel.Text != tc.metadata.Title {
				t.Errorf("Expected title label to be '%s', got '%s'", tc.metadata.Title, previewArea.titleLabel.Text)
			}

			// Check filesize visibility
			if tc.metadata.Filesize > 0 {
				if !previewArea.sizeLabel.Visible() {
					t.Error("Expected size label to be visible when filesize > 0")
				}
			} else {
				if previewArea.sizeLabel.Visible() {
					t.Error("Expected size label to be hidden when filesize = 0")
				}
			}
		})
	}
}

// TestPreviewAreaWithMockThumbnail tests the preview area with a mock thumbnail
func TestPreviewAreaWithMockThumbnail(t *testing.T) {
	// Create a test app
	app := test.NewApp()
	defer app.Quit()

	// Create a preview area
	previewArea := NewPreviewArea(nil)

	// Create a window with the preview area
	w := test.NewWindow(previewArea.Container())
	defer w.Close()
	w.Resize(fyne.NewSize(400, 300))

	// Force a render
	// Note: test.Sync() is not available in all versions of Fyne
	// We'll skip this step for compatibility

	// Create a mock thumbnail image
	rect := canvas.NewRectangle(color.RGBA{R: 255, G: 0, B: 0, A: 255})
	rect.SetMinSize(fyne.NewSize(200, 150))
	img := canvas.NewImageFromImage(image.NewUniform(color.RGBA{R: 255, G: 0, B: 0, A: 255}))

	// Set the thumbnail
	previewArea.SetThumbnail(img)

	// Check that the preview area is visible
	if !previewArea.IsVisible() {
		t.Error("Expected preview area to be visible after SetThumbnail()")
	}

	// Check that the thumbnail area is visible
	if !previewArea.thumbnailArea.Visible() {
		t.Error("Expected thumbnail area to be visible after SetThumbnail()")
	}

	// Clear the thumbnail
	previewArea.ClearThumbnail()

	// Check that the thumbnail area is hidden
	if previewArea.thumbnailArea.Visible() {
		t.Error("Expected thumbnail area to be hidden after ClearThumbnail()")
	}
}

// TestShowVideoPreviewWithMocks tests the ShowVideoPreview function with mocks
func TestShowVideoPreviewWithMocks(t *testing.T) {
	// Skip this test as it requires complex mocking of file operations and HTTP requests
	t.Skip("Skipping TestShowVideoPreviewWithMocks as it requires complex mocking")

	// This is how the test would be structured if we could properly mock everything:

	// Create a test app
	app := test.NewApp()
	defer app.Quit()

	// Create a temporary directory for cache
	tempDir := t.TempDir()

	// Create cache subdirectories
	thumbnailCacheDir := filepath.Join(tempDir, "cache", "thumbnails")
	metadataCacheDir := filepath.Join(tempDir, "cache", "metadata")
	os.MkdirAll(thumbnailCacheDir, 0755)
	os.MkdirAll(metadataCacheDir, 0755)

	// Create a mock metadata provider
	mockProvider := &mockMetadataProvider{
		metadata: map[string]*metadata.VideoMetadata{
			"https://www.youtube.com/watch?v=test123": {
				ID:        "test123",
				Title:     "Test Video",
				Author:    "Test Author",
				Duration:  120,
				Filesize:  1024 * 1024,
				Thumbnail: "https://example.com/thumbnail.jpg",
			},
		},
	}

	// Create a mock downloader
	mockDl := &mockDownloader{
		MetadataProvider: mockProvider,
	}

	// Create an enhanced mock app
	mockApp := &enhancedMockApp{
		window:     test.NewWindow(nil),
		downloader: mockDl,
	}
	defer mockApp.window.Close()

	// Create a preview area
	previewArea := NewPreviewArea(nil)

	// Call ShowVideoPreview
	ShowVideoPreview(mockApp, "https://www.youtube.com/watch?v=test123", previewArea)

	// Wait for async operations to complete
	// This would require some way to wait for goroutines to complete

	// Check that the preview area was updated
	if !previewArea.IsVisible() {
		t.Error("Expected preview area to be visible after ShowVideoPreview()")
	}

	// Check that the status was updated
	if len(mockApp.statusUpdates) == 0 {
		t.Error("Expected status to be updated")
	}
}

// TestPreviewAreaLayout tests the layout of the preview area
func TestPreviewAreaLayout(t *testing.T) {
	// Create a test app
	app := test.NewApp()
	defer app.Quit()

	// Create a preview area
	previewArea := NewPreviewArea(nil)

	// Create a window with the preview area
	w := test.NewWindow(previewArea.Container())
	defer w.Close()

	// Test different window sizes
	sizes := []fyne.Size{
		{Width: 300, Height: 200},
		{Width: 400, Height: 300},
		{Width: 500, Height: 400},
	}

	// Create sample metadata
	meta := &metadata.VideoMetadata{
		ID:       "video123",
		Title:    "Test Video",
		Author:   "Test Author",
		Duration: 120,
		Filesize: 1024 * 1024, // 1 MB
	}

	// Update the preview area with metadata
	previewArea.UpdateWithMetadata(meta)

	// Create a mock thumbnail
	img := canvas.NewImageFromImage(image.NewUniform(color.RGBA{R: 255, G: 0, B: 0, A: 255}))
	previewArea.SetThumbnail(img)

	for _, size := range sizes {
		t.Run(fmt.Sprintf("%vx%v", size.Width, size.Height), func(t *testing.T) {
			// Resize the window
			w.Resize(size)

			// Check that the preview area is still visible
			if !previewArea.IsVisible() {
				t.Error("Expected preview area to be visible after resize")
			}

			// Check that the thumbnail has a reasonable size
			if previewArea.thumbnailContainer.Size().Width <= 0 ||
				previewArea.thumbnailContainer.Size().Height <= 0 {
				t.Error("Expected thumbnail to have a non-zero size")
			}

			// Check that the info area is still visible
			if !previewArea.infoArea.Visible() {
				t.Error("Expected info area to be visible after resize")
			}
		})
	}
}

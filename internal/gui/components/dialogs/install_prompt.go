// File: internal/gui/components/dialogs/install_prompt.go

package dialogs

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// ShowInstallDependencyDialog displays a confirmation dialog to install a missing dependency.
// If confirmed, it shows a progress dialog while running the installFunc and displays the result.
func ShowInstallDependencyDialog(
	parentWindow fyne.Window,
	dependencyName string, // e.g., "secretstorage"
	installFunc func(depName string) (string, error), // Function to run the installation
	statusUpdater func(string), // Function to update status bar
) {
	statusUpdater(i18n.Format("status_dependency_missing_prompt", dependencyName))

	// Create the confirmation dialog content
	message := i18n.Format("install_dependency_message_format", dependencyName, dependencyName)
	confirmDialog := dialog.NewConfirm(
		i18n.Get("install_dependency_title"),
		message,
		func(confirmed bool) {
			if !confirmed {
				statusUpdater(i18n.Get("status_idle")) // Reset status if cancelled
				return
			}

			// Show progress dialog
			progressLabel := widget.NewLabel(i18n.Format("installing_dependency_message", dependencyName))
			progressInf := widget.NewProgressBarInfinite()
			progressContent := container.NewVBox(progressLabel, progressInf)
			progressDialog := dialog.NewCustomWithoutButtons(i18n.Get("installing_dependency_title"), progressContent, parentWindow)
			progressDialog.Show()

			// Run installation in a goroutine
			go func() {
				output, err := installFunc(dependencyName)

				// Ensure UI updates run on the main thread (Fyne usually handles this, but safer)
				// fyne.CurrentApp().Driver().EnsureEventQueueRuns() // If needed

				progressDialog.Hide() // Hide progress first

				if err != nil {
					// Show error dialog
					errorMsg := i18n.Format("install_error_message_format", dependencyName, err, output)
					dialog.ShowError(fmt.Errorf(errorMsg), parentWindow)
					statusUpdater(i18n.Format("install_error_title") + ": " + dependencyName)
				} else {
					// Show success dialog
					successMsg := i18n.Format("install_success_message_format", dependencyName)
					dialog.ShowInformation(i18n.Get("install_success_title"), successMsg, parentWindow)
					statusUpdater(i18n.Format("install_success_title") + ": " + dependencyName)
				}
			}()
		},
		parentWindow,
	)

	// Set custom button text for confirmation
	confirmDialog.SetConfirmText(i18n.Get("install_dependency_confirm_button"))
	confirmDialog.Show()
}

// File: internal/gui/components/dialogs/playlist_dialog.go

package dialogs

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader/metadata"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// formatDurationFromInt formats a duration in seconds as a string
func formatDurationFromInt(seconds int) string {
	if seconds <= 0 {
		return "N/A"
	}

	hours := seconds / 3600
	minutes := (seconds % 3600) / 60
	secs := seconds % 60

	if hours > 0 {
		return fmt.Sprintf("%d:%02d:%02d", hours, minutes, secs)
	}
	return fmt.Sprintf("%d:%02d", minutes, secs)
}

// PlaylistDialog displays a dialog for selecting videos from a playlist
type PlaylistDialog struct {
	dialog      dialog.Dialog
	playlist    *metadata.PlaylistMetadata
	checkboxes  []*widget.Check
	selectAll   *widget.Check
	onConfirm   func([]metadata.VideoMetadata)
	selectedIDs map[string]bool
}

// NewPlaylistDialog creates a new playlist selection dialog
func NewPlaylistDialog(parent fyne.Window, playlist *metadata.PlaylistMetadata, onConfirm func([]metadata.VideoMetadata)) *PlaylistDialog {
	pd := &PlaylistDialog{
		playlist:    playlist,
		onConfirm:   onConfirm,
		selectedIDs: make(map[string]bool),
	}

	// Create the content
	title := widget.NewLabelWithStyle(
		fmt.Sprintf("%s (%d videos)", playlist.Title, len(playlist.Entries)),
		fyne.TextAlignCenter,
		fyne.TextStyle{Bold: true},
	)

	// Create select all checkbox
	pd.selectAll = widget.NewCheck(i18n.Get("select_all"), func(checked bool) {
		for _, cb := range pd.checkboxes {
			cb.SetChecked(checked)
		}
		// Update selected IDs
		for _, video := range playlist.Entries {
			pd.selectedIDs[video.ID] = checked
		}
	})
	pd.selectAll.SetChecked(true) // Default to all selected

	// Create checkboxes for each video
	pd.checkboxes = make([]*widget.Check, len(playlist.Entries))
	videoItems := make([]fyne.CanvasObject, len(playlist.Entries))

	for i, video := range playlist.Entries {
		index := i // Capture the index for the closure
		videoTitle := fmt.Sprintf("%d. %s (%s)", i+1, video.Title, formatDurationFromInt(video.Duration))

		pd.checkboxes[i] = widget.NewCheck(videoTitle, func(checked bool) {
			pd.selectedIDs[playlist.Entries[index].ID] = checked

			// Update "Select All" checkbox based on current selection
			allSelected := true
			for _, cb := range pd.checkboxes {
				if !cb.Checked {
					allSelected = false
					break
				}
			}
			pd.selectAll.SetChecked(allSelected)
		})

		// Default to selected
		pd.checkboxes[i].SetChecked(true)
		pd.selectedIDs[video.ID] = true

		videoItems[i] = pd.checkboxes[i]
	}

	// Create a scroll container for the video list
	videoList := container.NewVBox(videoItems...)
	scrollContainer := container.NewScroll(videoList)
	scrollContainer.SetMinSize(fyne.NewSize(500, 300))

	// Create buttons
	confirmButton := widget.NewButtonWithIcon(i18n.Get("download_selected"), theme.DownloadIcon(), func() {
		pd.confirm()
	})

	cancelButton := widget.NewButtonWithIcon(i18n.Get("cancel"), theme.CancelIcon(), func() {
		pd.dialog.Hide()
	})

	// Create the content layout
	content := container.NewVBox(
		title,
		widget.NewSeparator(),
		pd.selectAll,
		widget.NewSeparator(),
		scrollContainer,
		container.NewHBox(
			layout.NewSpacer(),
			cancelButton,
			confirmButton,
		),
	)

	// Create the dialog
	pd.dialog = dialog.NewCustom(i18n.Get("select_videos"), "", content, parent)
	pd.dialog.Resize(fyne.NewSize(600, 400))

	return pd
}

// Show displays the playlist dialog
func (pd *PlaylistDialog) Show() {
	pd.dialog.Show()
}

// confirm processes the selected videos and calls the onConfirm callback
func (pd *PlaylistDialog) confirm() {
	var selectedVideos []metadata.VideoMetadata

	for _, video := range pd.playlist.Entries {
		if pd.selectedIDs[video.ID] {
			selectedVideos = append(selectedVideos, video)
		}
	}

	pd.dialog.Hide()
	pd.onConfirm(selectedVideos)
}

// Utility functions are now in utils.go

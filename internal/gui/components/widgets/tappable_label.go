// Package widgets provides custom UI widgets for the GoTube application.
package widgets

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/driver/desktop" // Needed for Cursor
	"fyne.io/fyne/v2/widget"
)

// TappableLabel is a label widget that can be tapped.
type TappableLabel struct {
	widget.Label        // Embed the standard Label
	OnTapped     func() `json:"-"` // Callback function for tap events
	disabled     bool   // Add disabled state
}

// NewTappableLabel creates a new TappableLabel widget.
func NewTappableLabel(text string, tapped func()) *TappableLabel {
	l := &TappableLabel{
		OnTapped: tapped,
		disabled: false,
	}
	l.Text = text         // Set initial text
	l.ExtendBaseWidget(l) // Essential for custom widgets
	return l
}

// Disabled returns whether the label is currently disabled
func (l *TappableLabel) Disabled() bool {
	return l.disabled
}

// Enable enables the label
func (l *TappableLabel) Enable() {
	l.disabled = false
	l.Refresh()
}

// Disable disables the label
func (l *TappableLabel) Disable() {
	l.disabled = true
	l.Refresh()
}

// Tapped is called when the label is tapped (primary button).
func (l *TappableLabel) Tapped(_ *fyne.PointEvent) {
	if l.Disabled() { // Now this will work
		return
	}
	if l.OnTapped != nil {
		l.OnTapped() // Execute the callback
	}
}

// TappedSecondary is called for secondary tap (right-click). Not used here.
func (l *TappableLabel) TappedSecondary(_ *fyne.PointEvent) {}

// Cursor returns the pointing hand cursor when hovering over the label.
func (l *TappableLabel) Cursor() desktop.Cursor {
	if l.Disabled() { // Now this will work
		return desktop.DefaultCursor
	}
	return desktop.PointerCursor
}
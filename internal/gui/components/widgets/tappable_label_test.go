package widgets

import (
	"testing"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/driver/desktop"
	"fyne.io/fyne/v2/test"
)

// TestNewTappableLabel tests the creation of a new TappableLabel
func TestNewTappableLabel(t *testing.T) {
	// Test with callback
	callback := func() {
		// Do nothing for test
	}

	label := NewTappableLabel("Test Label", callback)

	// Check initial state
	if label.Text != "Test Label" {
		t.<PERSON>rf("Expected label text to be 'Test Label', got '%s'", label.Text)
	}

	if label.Disabled() {
		t.Error("Expected label to be enabled by default")
	}

	// Test without callback
	label2 := NewTappableLabel("No Callback", nil)
	if label2.OnTapped != nil {
		t.Error("Expected OnTapped to be nil when no callback provided")
	}
}

// TestTappableLabelDisabled tests the disabled state of TappableLabel
func TestTappableLabelDisabled(t *testing.T) {
	label := NewTappableLabel("Test", nil)

	// Test initial state
	if label.Disabled() {
		t.Error("Expected label to be enabled by default")
	}

	// Test disabling
	label.Disable()
	if !label.Disabled() {
		t.Error("Expected label to be disabled after Disable()")
	}

	// Test enabling
	label.Enable()
	if label.Disabled() {
		t.Error("Expected label to be enabled after Enable()")
	}
}

// TestTappableLabelTapped tests the tapped functionality
func TestTappableLabelTapped(t *testing.T) {
	tapped := false
	callback := func() {
		tapped = true
	}

	label := NewTappableLabel("Test", callback)

	// Simulate tap
	label.Tapped(&fyne.PointEvent{})

	if !tapped {
		t.Error("Expected callback to be called when label is tapped")
	}

	// Test when disabled
	tapped = false
	label.Disable()
	label.Tapped(&fyne.PointEvent{})

	if tapped {
		t.Error("Expected callback not to be called when label is disabled")
	}

	// Test with nil callback
	label2 := NewTappableLabel("No Callback", nil)
	// This should not panic
	label2.Tapped(&fyne.PointEvent{})
}

// TestTappableLabelCursor tests the cursor functionality
func TestTappableLabelCursor(t *testing.T) {
	label := NewTappableLabel("Test", nil)

	// Test cursor when enabled
	if label.Cursor() != desktop.PointerCursor {
		t.Errorf("Expected cursor to be PointerCursor when enabled, got %v", label.Cursor())
	}

	// Test cursor when disabled
	label.Disable()
	if label.Cursor() != desktop.DefaultCursor {
		t.Errorf("Expected cursor to be DefaultCursor when disabled, got %v", label.Cursor())
	}
}

// TestTappableLabelRendering tests the rendering of the label
func TestTappableLabelRendering(t *testing.T) {
	// Create a test app for rendering
	app := test.NewApp()
	defer app.Quit()

	label := NewTappableLabel("Test Label", nil)

	// Create a test window and set content
	w := test.NewWindow(label)
	defer w.Close()

	// Force a render
	// Note: test.Sync() is not available in all versions of Fyne

	// We can't easily test the actual rendering, but we can ensure it doesn't crash
	// and that the widget has a minimum size
	minSize := label.MinSize()
	if minSize.Width <= 0 || minSize.Height <= 0 {
		t.Errorf("Expected non-zero minimum size, got %v", minSize)
	}
}

// File: internal/gui/components/settings/filename_template.go

package settings

import (
	"fmt"
	"strings"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// ShowFilenameTemplateDialog displays a dialog for configuring custom filename templates
func (s *AppSettings) ShowFilenameTemplateDialog() {
	// Create template entry
	configManager := s.app.GetConfig()
	templateEntry := widget.NewEntry()
	if configManager != nil {
		if config, ok := configManager.(*config.Manager); ok {
			templateEntry.SetText(config.GetFilenameTemplate())
		}
	}
	templateEntry.SetPlaceHolder("%(title)s-%(id)s.%(ext)s")

	// Create help text
	helpText := widget.NewLabel("Available variables:\n" +
		"%(title)s - Video title\n" +
		"%(id)s - Video ID\n" +
		"%(uploader)s - Uploader name\n" +
		"%(upload_date)s - Upload date (YYYYMMDD)\n" +
		"%(ext)s - File extension\n" +
		"%(resolution)s - Video resolution\n" +
		"%(format)s - Format code\n" +
		"%(duration)s - Duration in seconds\n")
	helpText.Wrapping = fyne.TextWrapWord

	// Create example output
	exampleLabel := widget.NewLabel("Example output: video-title-abc123.mp4")
	exampleLabel.Wrapping = fyne.TextWrapWord

	// Update example when template changes
	templateEntry.OnChanged = func(text string) {
		if text == "" {
			text = "%(title)s-%(id)s.%(ext)s"
		}
		// Show a simplified example
		example := text
		example = strings.Replace(example, "%(title)s", "video-title", -1)
		example = strings.Replace(example, "%(id)s", "abc123", -1)
		example = strings.Replace(example, "%(uploader)s", "channel-name", -1)
		example = strings.Replace(example, "%(upload_date)s", "20230101", -1)
		example = strings.Replace(example, "%(ext)s", "mp4", -1)
		example = strings.Replace(example, "%(resolution)s", "1080p", -1)
		example = strings.Replace(example, "%(format)s", "mp4", -1)
		example = strings.Replace(example, "%(duration)s", "300", -1)
		exampleLabel.SetText(fmt.Sprintf("Example output: %s", example))
	}

	// Create content
	content := container.NewVBox(
		widget.NewLabel(i18n.Get("filename_template")),
		templateEntry,
		widget.NewSeparator(),
		helpText,
		widget.NewSeparator(),
		exampleLabel,
	)

	// Create dialog
	dlg := dialog.NewCustomConfirm(
		i18n.Get("custom_filename"),
		i18n.Get("save"),
		i18n.Get("cancel"),
		content,
		func(confirmed bool) {
			if confirmed {
				template := templateEntry.Text
				if template == "" {
					template = "%(title)s-%(id)s.%(ext)s"
				}

				// Save the template to config
				configManager := s.app.GetConfig()
				if configManager != nil {
					if config, ok := configManager.(*config.Manager); ok {
						config.SetFilenameTemplate(template)
						s.UpdateStatus(fmt.Sprintf("Filename template updated: %s", template))
					}
				}
			}
		},
		s.app.GetWindow(),
	)

	dlg.Resize(fyne.NewSize(500, 400))
	dlg.Show()
}

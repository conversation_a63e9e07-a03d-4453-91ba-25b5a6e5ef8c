// File: internal/gui/components/settings/settings.go

package settings

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/screens/download"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/screens/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
)

// AppSettings provides settings-related functionality for the application
type AppSettings struct {
	app         common.AppInterface
	currentLang string
	tabs        *container.AppTabs
	downloadTab *download.DownloadTab
	historyTab  *history.HistoryTab
}

// NewAppSettings creates a new AppSettings instance
func NewAppSettings(app common.AppInterface, tabs *container.AppTabs, downloadTab *download.DownloadTab, historyTab *history.HistoryTab) *AppSettings {
	return &AppSettings{
		app:         app,
		currentLang: "en", // Default language
		tabs:        tabs,
		downloadTab: downloadTab,
		historyTab:  historyTab,
	}
}

// UpdateUILanguage updates the UI with the current language
func (s *AppSettings) UpdateUILanguage() {
	// Reinitialize i18n with the current language
	i18n.SetLanguage(s.currentLang)

	// Update window title
	s.app.GetWindow().SetTitle("GoTube Video Downloader")

	// Update tab labels if tabs are available
	if s.tabs != nil && len(s.tabs.Items) >= 2 {
		s.tabs.Items[0].Text = i18n.Get("tab_download")
		s.tabs.Items[1].Text = i18n.Get("tab_history")
		s.tabs.Refresh()
	}

	// Update download tab if available
	if s.downloadTab != nil {
		s.downloadTab.UpdateLanguage()
	}

	// Update history tab if available
	if s.historyTab != nil {
		s.historyTab.UpdateLanguage()
	}
}

// ShowFilenameTemplateDialogOld is a legacy method, use the one in filename_template.go instead
func (s *AppSettings) ShowFilenameTemplateDialogOld() {
	// This method is kept for reference but should not be used
	// Use the implementation in filename_template.go instead
}

// GetLogger returns the application logger
func (s *AppSettings) GetLogger() interface{} {
	return s.app.GetLogger()
}

// GetDownloader returns the downloader instance
func (s *AppSettings) GetDownloader() interface{} {
	return s.app.GetDownloader()
}

// GetHistory returns the history manager
func (s *AppSettings) GetHistory() interface{} {
	return s.app.GetHistory()
}

// GetConfig returns the config manager
func (s *AppSettings) GetConfig() interface{} {
	return s.app.GetConfig()
}

// GetWindow returns the main window
func (s *AppSettings) GetWindow() fyne.Window {
	return s.app.GetWindow()
}

// UpdateStatus updates the status label
func (s *AppSettings) UpdateStatus(status string) {
	// Forward to the app's UpdateStatus method
	s.app.UpdateStatus(status)

	// Also log locally
	loggerInstance := s.app.GetLogger()
	if loggerInstance != nil {
		if logger, ok := loggerInstance.(*logger.Logger); ok {
			logger.Info("Settings Status: %s", status)
		}
	}

	// Update the status label in the download tab if available
	if s.downloadTab != nil {
		// Just log the status for now
		fmt.Println("Settings Status:", status)
	}
}

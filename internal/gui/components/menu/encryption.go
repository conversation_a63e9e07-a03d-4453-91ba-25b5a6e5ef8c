package menu

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config" // Import config
	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history" // Import history
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// ShowExistingDatabasePasswordDialogImpl shows a password dialog for an existing encrypted database
func ShowExistingDatabasePasswordDialogImpl(a common.AppInterface, callback func(string)) {
	// Create password entry
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.PlaceHolder = i18n.Get("encryption_password_current")

	// Create content
	content := container.NewVBox(
		widget.NewLabel(i18n.Get("encryption_password_existing_prompt")),
		widget.NewSeparator(),
		widget.NewLabel(i18n.Get("encryption_password_current")),
		passwordEntry,
	)

	// Create dialog
	dlg := dialog.NewCustomConfirm(
		i18n.Get("encryption_password_title"),
		i18n.Get("unlock"),
		i18n.Get("cancel"), // Use translation for Cancel
		content,
		func(confirmed bool) {
			if !confirmed {
				// Handle cancel during startup: Maybe exit or proceed without DB?
				// For now, just log and the callback won't be called.
				fmt.Println("Database unlock cancelled by user during startup.")
				// Potentially call a function to proceed in a degraded state or exit
				// For example: os.Exit(0) or proceedWithoutDB()
				return
			}

			// Get password
			password := passwordEntry.Text

			if password == "" {
				// Re-show the dialog or show error and potentially exit
				dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_empty")), a.GetWindow())
				// Re-prompt immediately
				ShowExistingDatabasePasswordDialogImpl(a, callback)
				return
			}

			// Call callback with password (callback handles DB reopening and loading)
			callback(password)
		},
		a.GetWindow(),
	)

	// Show dialog
	dlg.SetDismissText(i18n.Get("cancel")) // Ensure dismiss maps to cancel
	dlg.Show()
}

// ShowPasswordDialogImpl shows the initial password dialog for a new database.
// Takes an onFinish callback to be called after the dialog is closed.
func ShowPasswordDialogImpl(a common.AppInterface, onFinish func()) {
	// Create password entry
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.PlaceHolder = i18n.Get("encryption_password_new")

	// Create confirm password entry
	confirmPasswordEntry := widget.NewPasswordEntry()
	confirmPasswordEntry.PlaceHolder = i18n.Get("encryption_password_confirm")

	// Create content
	content := container.NewVBox(
		widget.NewLabel(i18n.Get("encryption_password_prompt")),
		widget.NewSeparator(),
		widget.NewLabel(i18n.Get("encryption_password_new")),
		passwordEntry,
		widget.NewLabel(i18n.Get("encryption_password_confirm")),
		confirmPasswordEntry,
	)

	// Create dialog with custom buttons
	dlg := dialog.NewCustom(
		i18n.Get("encryption_password_title"),
		"", // No default button text needed here
		content,
		a.GetWindow(),
	)

	// Add enable button
	enableBtn := widget.NewButton(i18n.Get("encryption_enable"), func() {
		// Validate passwords
		password := passwordEntry.Text
		confirmPassword := confirmPasswordEntry.Text

		if password == "" {
			dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_empty")), a.GetWindow())
			return // Keep dialog open for correction
		}

		if password != confirmPassword {
			dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_mismatch")), a.GetWindow())
			return // Keep dialog open for correction
		}

		// Get database
		db, ok := a.GetDatabase().(*database.DB)
		if !ok || db == nil {
			dialog.ShowError(fmt.Errorf("database not available"), a.GetWindow())
			// Close dialog, but call onFinish to signal completion
			dlg.Hide()
			if onFinish != nil {
				onFinish()
			}
			return
		}

		// Enable encryption
		err := db.EnableEncryption(password)
		if err != nil {
			dialog.ShowError(fmt.Errorf(i18n.Format("encryption_error", err)), a.GetWindow())
			// Close dialog, but call onFinish to signal completion
			dlg.Hide()
			if onFinish != nil {
				onFinish()
			}
			return
		}

		// --- Explicitly load data after enabling encryption ---
		loadConfigAndHistory(a) // Use helper

		// Show success message
		dialog.ShowInformation(
			i18n.Get("encryption_password_title"),
			i18n.Get("encryption_enabled"),
			a.GetWindow(),
		)

		// Update encryption menu
		a.UpdateEncryptionMenu()

		// Close dialog
		dlg.Hide()

		// Call onFinish callback *after* successful completion
		if onFinish != nil {
			onFinish()
		}
	})
	enableBtn.Importance = widget.HighImportance // Make it visually distinct

	// Add skip button
	skipBtn := widget.NewButton(i18n.Get("encryption_skip"), func() {
		// Just close the dialog without enabling encryption
		dlg.Hide()

		// Show information message
		dialog.ShowInformation(
			i18n.Get("encryption_password_title"),
			i18n.Get("encryption_skipped"),
			a.GetWindow(),
		)
		// Load data now since we skipped encryption
		loadConfigAndHistory(a) // Use helper

		// Call onFinish callback *after* skipping
		if onFinish != nil {
			onFinish()
		}
	})

	// Add both buttons to the dialog
	dlg.SetButtons([]fyne.CanvasObject{skipBtn, enableBtn}) // Skip first, then Enable

	// Show dialog
	dlg.Show()
}

// ShowChangePasswordDialogImpl shows the change password dialog
func ShowChangePasswordDialogImpl(a common.AppInterface) {
	// Get database
	db, ok := a.GetDatabase().(*database.DB)
	if !ok || db == nil {
		dialog.ShowError(fmt.Errorf("database not available"), a.GetWindow())
		return
	}

	// Check if encryption is enabled
	if !db.IsEncrypted() {
		dialog.ShowInformation(
			i18n.Get("menu_change_password"),        // Use specific title
			i18n.Get("encryption_already_disabled"), // More accurate message
			a.GetWindow(),
		)
		return
	}

	// Create current password entry
	currentPasswordEntry := widget.NewPasswordEntry()
	currentPasswordEntry.PlaceHolder = i18n.Get("encryption_password_current")

	// Create new password entry
	newPasswordEntry := widget.NewPasswordEntry()
	newPasswordEntry.PlaceHolder = i18n.Get("encryption_password_new")

	// Create confirm password entry
	confirmPasswordEntry := widget.NewPasswordEntry()
	confirmPasswordEntry.PlaceHolder = i18n.Get("encryption_password_confirm")

	// Create content
	content := container.NewVBox(
		widget.NewLabel(i18n.Get("encryption_password_current")),
		currentPasswordEntry,
		widget.NewLabel(i18n.Get("encryption_password_new")),
		newPasswordEntry,
		widget.NewLabel(i18n.Get("encryption_password_confirm")),
		confirmPasswordEntry,
	)

	// Create dialog
	dlg := dialog.NewCustomConfirm(
		i18n.Get("menu_change_password"), // Title specific to action
		i18n.Get("save"),
		i18n.Get("cancel"),
		content,
		func(confirmed bool) {
			if !confirmed {
				return
			}

			// Validate passwords
			currentPassword := currentPasswordEntry.Text
			newPassword := newPasswordEntry.Text
			confirmPassword := confirmPasswordEntry.Text

			if currentPassword == "" || newPassword == "" {
				dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_empty")), a.GetWindow())
				return // Keep dialog open
			}

			if newPassword != confirmPassword {
				dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_mismatch")), a.GetWindow())
				return // Keep dialog open
			}

			// Change password
			err := db.ChangePassword(currentPassword, newPassword)
			if err != nil {
				// Check for specific "incorrect current password" error
				if err.Error() == "incorrect current password" {
					dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_incorrect")), a.GetWindow()) // Use specific translation
				} else {
					dialog.ShowError(fmt.Errorf(i18n.Format("encryption_error", err)), a.GetWindow())
				}
				return // Keep dialog open on error
			}

			// Show success message
			dialog.ShowInformation(
				i18n.Get("menu_change_password"),
				i18n.Get("encryption_password_changed"),
				a.GetWindow(),
			)

			// Update encryption menu (state hasn't changed, but good practice)
			a.UpdateEncryptionMenu()
			// No need to call onFinish here as UI is already running
		},
		a.GetWindow(),
	)

	// Show dialog
	dlg.Show()
}

// ShowEnableEncryptionDialogImpl shows the enable encryption dialog (when called from menu).
// Takes an optional onFinish callback for startup scenarios.
func ShowEnableEncryptionDialogImpl(a common.AppInterface, onFinish func()) {
	// Get database
	db, ok := a.GetDatabase().(*database.DB)
	if !ok || db == nil {
		dialog.ShowError(fmt.Errorf("database not available"), a.GetWindow())
		if onFinish != nil {
			onFinish() // Call finish even if DB is not available
		}
		return
	}

	// Check if encryption is already enabled
	if db.IsEncrypted() {
		dialog.ShowInformation(
			i18n.Get("menu_enable_encryption"),
			i18n.Get("encryption_already_enabled"),
			a.GetWindow(),
		)
		if onFinish != nil {
			onFinish() // Call finish if already enabled
		}
		return
	}

	// Re-use the initial password dialog logic, passing the onFinish callback
	ShowPasswordDialogImpl(a, onFinish) // This handles the password entry and enabling logic
}

// ShowDisableEncryptionDialogImpl shows the disable encryption dialog
func ShowDisableEncryptionDialogImpl(a common.AppInterface) {
	// Get database
	db, ok := a.GetDatabase().(*database.DB)
	if !ok || db == nil {
		dialog.ShowError(fmt.Errorf("database not available"), a.GetWindow())
		return
	}

	// Check if encryption is already disabled
	if !db.IsEncrypted() {
		dialog.ShowInformation(
			i18n.Get("menu_disable_encryption"),
			i18n.Get("encryption_already_disabled"),
			a.GetWindow(),
		)
		return
	}

	// Create password entry
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.PlaceHolder = i18n.Get("encryption_password_current")

	// Create content
	content := container.NewVBox(
		widget.NewLabel(i18n.Get("encryption_disable_warning")),
		widget.NewSeparator(),
		widget.NewLabel(i18n.Get("encryption_password_current")),
		passwordEntry,
	)

	// Create dialog
	dlg := dialog.NewCustomConfirm(
		i18n.Get("menu_disable_encryption"),
		i18n.Get("encryption_disable"), // Button text
		i18n.Get("cancel"),
		content,
		func(confirmed bool) {
			if !confirmed {
				return
			}

			// Validate password
			password := passwordEntry.Text

			if password == "" {
				dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_empty")), a.GetWindow())
				return // Keep dialog open
			}

			// Disable encryption
			err := db.DisableEncryption(password)
			if err != nil {
				if err.Error() == "incorrect password" {
					dialog.ShowError(fmt.Errorf(i18n.Get("encryption_password_incorrect")), a.GetWindow())
				} else {
					dialog.ShowError(fmt.Errorf(i18n.Format("encryption_error", err)), a.GetWindow())
				}
				return // Keep dialog open
			}

			// Show success message
			dialog.ShowInformation(
				i18n.Get("menu_disable_encryption"),
				i18n.Get("encryption_disabled"),
				a.GetWindow(),
			)

			// Update encryption menu
			a.UpdateEncryptionMenu()
			// No need to call onFinish here as UI is running
		},
		a.GetWindow(),
	)
	dlg.SetConfirmImportance(widget.DangerImportance) // Make disable button red
	dlg.Show()
}

// Helper function to load config and history, showing errors
func loadConfigAndHistory(a common.AppInterface) {
	// --- Explicitly load data ---
	if histMgrInterface := a.GetHistory(); histMgrInterface != nil {
		if histMgr, ok := histMgrInterface.(*history.HistoryManager); ok {
			if err := histMgr.Load(); err != nil {
				fmt.Printf("Error loading history: %v\n", err)
				dialog.ShowError(fmt.Errorf("Failed to load history: %v", err), a.GetWindow())
			}
		}
	}
	if cfgMgrInterface := a.GetConfig(); cfgMgrInterface != nil {
		if cfgMgr, ok := cfgMgrInterface.(*config.Manager); ok {
			if err := cfgMgr.Load(); err != nil {
				fmt.Printf("Error loading config: %v\n", err)
				dialog.ShowError(fmt.Errorf("Failed to load configuration: %v", err), a.GetWindow())
			}
		}
	}
	// --- End explicit load ---
}

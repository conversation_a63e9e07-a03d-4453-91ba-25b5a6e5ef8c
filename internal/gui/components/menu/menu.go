// ---- File: internal/gui/menu/menu.go ----

package menu

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath" // Import filepath
	"runtime"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/storage"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/cookie"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// Import cookie dialog
func ImportCookiesImpl(a common.AppInterface) {
	cookie.ShowCookieImportDialog(a)
}

// Show browser cookies dialog
func ShowBrowserCookiesDialogImpl(a common.AppInterface) {
	// Create a dialog to enable/disable browser cookies and select browser
	browsers := []string{"chrome", "firefox", "edge", "safari", "opera", "brave", "vivaldi"}
	browserSelect := widget.NewSelect(browsers, func(selected string) {
		a.SetBrowserName(selected)
	})

	// Set initial selection based on current downloader setting
	currentBrowser := "chrome" // Default
	if dlInterface := a.GetDownloader(); dlInterface != nil {
		if dl, ok := dlInterface.(*downloader.Downloader); ok {
			currentBrowser = dl.GetBrowserName()
		}
	}
	// Find index of current browser
	selectedIndex := 0
	for i, b := range browsers {
		if b == currentBrowser {
			selectedIndex = i
			break
		}
	}
	browserSelect.SetSelectedIndex(selectedIndex)

	enableCheck := widget.NewCheck(i18n.Get("use_browser_cookies"), func(checked bool) {
		a.SetUseBrowserCookies(checked)
	})

	// Set initial checked state based on current config/downloader setting
	useBrowserCookies := false
	if cfgInterface := a.GetConfig(); cfgInterface != nil {
		if cfg, ok := cfgInterface.(*config.Manager); ok {
			useBrowserCookies = cfg.GetBool("use_browser_cookies")
		}
	}
	enableCheck.SetChecked(useBrowserCookies)

	content := container.NewVBox(
		widget.NewLabel(i18n.Get("browser_cookies_help")),
		widget.NewSeparator(),
		enableCheck,
		widget.NewLabel(i18n.Get("select_browser")),
		browserSelect,
	)

	dialog.ShowCustom(
		i18n.Get("browser_cookies_title"),
		i18n.Get("close"),
		content,
		a.GetWindow(),
	)
}

// SetupMenu creates the application menu
func SetupMenu(a common.AppInterface) {
	// Create main menu
	mainMenu := fyne.NewMainMenu(
		// File menu
		fyne.NewMenu(i18n.Get("menu_file"),
			fyne.NewMenuItem(i18n.Get("menu_custom_cookies"), func() {
				a.SelectCustomCookies()
			}),
			fyne.NewMenuItem(i18n.Get("menu_import_cookies"), func() {
				ImportCookiesImpl(a)
			}),
			fyne.NewMenuItem(i18n.Get("menu_use_browser_cookies"), func() {
				ShowBrowserCookiesDialogImpl(a)
			}),
			fyne.NewMenuItemSeparator(),
			fyne.NewMenuItem(i18n.Get("menu_filename_template"), func() {
				a.ShowFilenameTemplateDialog()
			}),
			// Add Exit option
			fyne.NewMenuItemSeparator(),
			fyne.NewMenuItem(i18n.Get("menu_exit"), func() {
				a.GetWindow().Close() // Use the standard window close mechanism
			}),
		),
		// Settings menu
		fyne.NewMenu(i18n.Get("menu_settings"),
			fyne.NewMenuItem(i18n.Get("menu_language"), func() {
				// Provide a no-op callback as UI is already running
				a.ShowLanguageDialog(func() {})
			}),
		),
		// Encryption menu - with dynamic items based on encryption status
		CreateEncryptionMenu(a),
		// Help menu
		fyne.NewMenu(i18n.Get("menu_help"),
			fyne.NewMenuItem(i18n.Get("menu_about"), func() {
				a.ShowAboutDialog()
			}),
			fyne.NewMenuItem(i18n.Get("menu_logs"), func() {
				a.OpenLogDirectory()
			}),
			fyne.NewMenuItem(i18n.Get("menu_cookie_guide"), func() {
				a.ShowCookieGuide()
			}),
		),
	)

	// Set the main menu
	a.GetWindow().SetMainMenu(mainMenu)
}

// SelectCustomCookiesImpl shows a file dialog to select a custom cookie file
func SelectCustomCookiesImpl(a common.AppInterface) {
	// Create file dialog
	fd := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			if err.Error() != "operation cancelled" {
				dialog.ShowError(err, a.GetWindow())
			}
			return
		}
		if reader == nil {
			return
		}
		defer reader.Close()

		path := reader.URI().Path()

		dlInterface := a.GetDownloader()
		if dlInterface == nil {
			dialog.ShowError(fmt.Errorf("Downloader not available"), a.GetWindow())
			return
		}
		downloaderInstance, ok := dlInterface.(*downloader.Downloader)
		if !ok {
			dialog.ShowError(fmt.Errorf("Invalid downloader type"), a.GetWindow())
			return
		}

		err = downloaderInstance.SetCustomCookies(path)
		if err != nil {
			// Provide clearer error message since auto-extraction is gone
			errMsg := fmt.Sprintf(i18n.Get("error_custom_cookies_dialog")+"\n\nEnsure the file exists, is readable, and is in Netscape format.", err)
			dialog.ShowError(fmt.Errorf(errMsg), a.GetWindow())
			a.UpdateStatus(fmt.Sprintf(i18n.Get("error_custom_cookies"), err))
			return
		}

		cfgInterface := a.GetConfig()
		statusMsg := ""
		if cfgInterface != nil {
			if configManager, ok := cfgInterface.(*config.Manager); ok {
				saveErr := configManager.SetCustomCookiePath(path)
				if saveErr != nil {
					errMsg := fmt.Sprintf("Cookie file set, but failed to save path to config: %v", saveErr)
					a.UpdateStatus(errMsg)
					dialog.ShowInformation("Config Save Warning", errMsg, a.GetWindow())
					statusMsg = fmt.Sprintf(i18n.Get("status_custom_cookies_set"), path) // Use 'set' status if save failed
				} else {
					statusMsg = fmt.Sprintf(i18n.Get("status_custom_cookies_saved"), path) // Use 'saved' status on success
				}
			} else {
				statusMsg = fmt.Sprintf(i18n.Get("status_custom_cookies_set"), path) // Config exists but wrong type
				a.UpdateStatus("Warning: Config manager has unexpected type, cannot save cookie path.")
			}
		} else {
			statusMsg = fmt.Sprintf(i18n.Get("status_custom_cookies_set"), path) // No config manager
			a.UpdateStatus("Warning: Config manager not initialized, cannot save cookie path.")
		}

		a.UpdateStatus(statusMsg)
		dialog.ShowInformation(i18n.Get("dialog_cookies_title"), statusMsg, a.GetWindow())

	}, a.GetWindow())

	fd.SetFilter(storage.NewExtensionFileFilter([]string{".txt"}))
	fd.Show()
}

// ShowAboutDialogImpl shows the about dialog
func ShowAboutDialogImpl(a common.AppInterface) {
	title := widget.NewLabelWithStyle(
		"GoTube Video Downloader",
		fyne.TextAlignCenter,
		fyne.TextStyle{Bold: true},
	)

	// Use correct Get() call for translation
	content := widget.NewLabel(fmt.Sprintf(i18n.Get("about_text"), runtime.Version()))
	content.Wrapping = fyne.TextWrapWord

	aboutContainer := container.NewVBox(
		title,
		widget.NewSeparator(),
		content,
	)

	dlg := dialog.NewCustom(i18n.Get("about_title"), i18n.Get("dialog_close"), aboutContainer, a.GetWindow())
	dlg.Resize(fyne.NewSize(400, 300))
	dlg.Show()
}

// OpenLogDirectoryImpl opens the log directory in the file explorer
func OpenLogDirectoryImpl(a common.AppInterface) {
	loggerInterface := a.GetLogger()
	if loggerInterface == nil {
		dialog.ShowInformation(i18n.Get("dialog_logs_title"), i18n.Get("dialog_logs_not_available"), a.GetWindow())
		return
	}

	// Assuming loggerInterface has GetConfigDir or similar, or hardcode path
	logDir := ""
	// Example: Get CWD or config dir to find logs subdir
	cfgInterface := a.GetConfig()
	if cfgInterface != nil {
		if cfg, ok := cfgInterface.(*config.Manager); ok {
			logDir = filepath.Join(cfg.GetConfigDir(), "logs")
		}
	}
	if logDir == "" { // Fallback if config dir not found
		cwd, _ := os.Getwd()
		logDir = filepath.Join(cwd, "logs")
	}

	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		dialog.ShowError(fmt.Errorf(i18n.Get("error_log_dir_not_found"), logDir), a.GetWindow())
		return
	}

	var cmd string
	var args []string

	switch runtime.GOOS {
	case "windows":
		cmd = "explorer"
		args = []string{logDir}
	case "darwin":
		cmd = "open"
		args = []string{logDir}
	default:
		cmd = "xdg-open"
		args = []string{logDir}
	}

	// Use CombinedOutput to potentially capture errors opening the directory
	execCmd := exec.Command(cmd, args...)
	output, err := execCmd.CombinedOutput()
	if err != nil {
		errMsg := fmt.Sprintf("%s\nOutput: %s", fmt.Errorf(i18n.Get("error_open_log_dir"), err), string(output))
		dialog.ShowError(fmt.Errorf(errMsg), a.GetWindow())
	}
}

// ShowCookieGuideImpl displays help on getting cookies.txt
func ShowCookieGuideImpl(a common.AppInterface) {
	dialog.ShowInformation(i18n.Get("menu_cookie_guide"), // Use specific title
		i18n.Get("cookie_import_help")+"\n\n"+ // Reuse import help
			"Alternatively, use File -> Use Browser Cookies to attempt using cookies directly from Chrome, Firefox, Edge, etc. (requires Python 'secretstorage' module on Linux/macOS).",
		a.GetWindow())
}

// CreateEncryptionMenu creates a dynamic encryption menu based on encryption status
func CreateEncryptionMenu(a common.AppInterface) *fyne.Menu {
	// Get database safely
	dbInterface := a.GetDatabase()
	if dbInterface == nil {
		// If database is not available, return a menu with a disabled item
		disabledItem := fyne.NewMenuItem(i18n.Get("menu_encryption")+" ("+i18n.Get("disabled")+")", nil)
		disabledItem.Disabled = true
		return fyne.NewMenu(i18n.Get("menu_encryption"), disabledItem)
	}

	db, ok := dbInterface.(*database.DB)
	if !ok || db == nil {
		// Handle case where DB exists but is wrong type (shouldn't happen ideally)
		disabledItem := fyne.NewMenuItem(i18n.Get("menu_encryption")+" (Error)", nil)
		disabledItem.Disabled = true
		return fyne.NewMenu(i18n.Get("menu_encryption"), disabledItem)
	}

	// Check if encryption is enabled
	if db.IsEncrypted() {
		// If encryption is enabled, show change password and disable encryption options
		return fyne.NewMenu(i18n.Get("menu_encryption"),
			fyne.NewMenuItem(i18n.Get("menu_change_password"), func() {
				a.ShowChangePasswordDialog()
			}),
			fyne.NewMenuItem(i18n.Get("menu_disable_encryption"), func() {
				a.ShowDisableEncryptionDialog()
			}),
		)
	} else {
		// If encryption is disabled, show only enable encryption option
		return fyne.NewMenu(i18n.Get("menu_encryption"),
			fyne.NewMenuItem(i18n.Get("menu_enable_encryption"), func() {
				// Provide a no-op callback as UI is already running
				a.ShowEnableEncryptionDialog(func() {})
			}),
		)
	}
}

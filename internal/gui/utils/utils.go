// File: internal/gui/utils/utils.go

// Package utils provides utility functions for the GUI components.
package utils

import (
	"fmt"
	"math"
)

// Note: Time-related utility functions have been moved to the timeutil package.
// See internal/downloader/timeutil/timeutil.go for ValidateTimeFormat and ConvertToSeconds.

// FormatDurationSeconds converts total seconds (float64) into a "M:SS" string.
func FormatDurationSeconds(totalSeconds float64) string {
	if totalSeconds < 0 {
		return "N/A" // Indicate invalid or unavailable duration
	}
	// Round to nearest second before calculation
	totalSeconds = math.Round(totalSeconds)
	minutes := int(totalSeconds) / 60
	seconds := int(totalSeconds) % 60
	return fmt.Sprintf("%d:%02d", minutes, seconds)
}

// Note: Duration and file size formatting functions are available in preview/utils.go.
// See internal/gui/preview/utils.go for formatDurationFromInt and formatFileSize.

// Note: Image and thumbnail handling functions are available in preview/utils.go.
// See internal/gui/preview/utils.go for DownloadThumbnail and LoadImage.

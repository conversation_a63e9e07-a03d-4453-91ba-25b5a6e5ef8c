// File: internal/gui/utils/drag_drop.go

package utils

import (
	"fmt"
	"net/url"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
)

// DragDropHandler handles drag and drop functionality for the application
type DragDropHandler struct {
	app      common.AppInterface
	urlEntry *widget.Entry
}

// NewDragDropHandler creates a new drag and drop handler
func NewDragDropHandler(app common.AppInterface, urlEntry *widget.Entry) *DragDropHandler {
	return &DragDropHandler{
		app:      app,
		urlEntry: urlEntry,
	}
}

// SetupDragAndDrop configures drag and drop support for the application
func (h *DragDropHandler) SetupDragAndDrop() {
	// Set up drag and drop for the main window
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})

	// Set up drag and drop for the URL entry
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})

	// Set up drag and drop for the main window
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})

	// Set up drag and drop for the URL entry
	h.urlEntry.OnChanged = func(text string) {
		// Check if the text is a URL that was pasted
		if isValidURL(text) && !strings.Contains(text, "\n") {
			// Automatically fetch info for the URL
			// Note: showVideoPreview functionality would need to be implemented elsewhere
			// h.showVideoPreview(text)
		}
	}

	// Set up drag and drop for the window
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})

	// Set up drag and drop for the window content
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})

	// Set up drag and drop for the window content
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})

	// Set up drag and drop for the window content
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})

	// Set up drag and drop for the window content
	h.app.GetWindow().Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		// Handle keyboard shortcuts
	})
}

// isValidURL checks if a string is a valid URL
func isValidURL(str string) bool {
	u, err := url.Parse(str)
	return err == nil && u.Scheme != "" && u.Host != "" &&
		(strings.Contains(u.Host, "youtube.com") || strings.Contains(u.Host, "youtu.be"))
}

// HandleDroppedURL processes a URL that was dropped onto the application
func (h *DragDropHandler) HandleDroppedURL(urlStr string) {
	if !isValidURL(urlStr) {
		dialog.ShowError(fmt.Errorf("invalid URL: %s", urlStr), h.app.GetWindow())
		return
	}

	// Add the URL to the entry
	currentText := h.urlEntry.Text
	if currentText != "" && !strings.HasSuffix(currentText, "\n") {
		currentText += "\n"
	}
	h.urlEntry.SetText(currentText + urlStr)

	// The preview will be automatically updated by the urlEntry.OnChanged handler
}

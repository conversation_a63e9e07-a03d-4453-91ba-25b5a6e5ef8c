package utils

import (
	"testing"
)

// TestIsValidURL tests the isValidURL function
func TestIsValidURL(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		expected bool
	}{
		{"Valid YouTube URL", "https://www.youtube.com/watch?v=dQw4w9WgXcQ", true},
		{"Valid YouTube short URL", "https://youtu.be/dQw4w9WgXcQ", true},
		{"Invalid URL (no scheme)", "youtube.com/watch?v=dQw4w9WgXcQ", false},
		{"Invalid URL (no host)", "https:///watch?v=dQw4w9WgXcQ", false},
		{"Invalid URL (not YouTube)", "https://example.com/video", false},
		{"Empty string", "", false},
		{"Malformed URL", "http://", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidURL(tt.url)
			if result != tt.expected {
				t.Errorf("isValidURL(%s) = %v, expected %v", tt.url, result, tt.expected)
			}
		})
	}
}

// TestDragDropHandler tests the DragDropHandler
func TestDragDropHandler(t *testing.T) {
	// This test would require mocking the Fyne UI components
	// which is complex in a unit test. We'll skip it for now.
	t.Skip("Skipping DragDropHandler test as it requires UI mocking")
}

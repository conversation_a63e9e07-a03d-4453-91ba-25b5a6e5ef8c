// File: internal/gui/common/common.go

package common

import (
	"fyne.io/fyne/v2"
)

// AppInterface defines the interface for accessing app components
type AppInterface interface {
	GetWindow() fyne.Window
	GetDownloader() interface{}
	GetConfig() interface{}
	GetHistory() interface{}
	GetLogger() interface{}
	GetDatabase() interface{}
	UpdateStatus(string)

	// Menu-related methods
	SelectCustomCookies()
	SetCookiesFromText(string) error
	SetUseBrowserCookies(bool)
	SetBrowserName(string)
	ShowFilenameTemplateDialog()
	ShowLanguageDialog(onFinish func()) // Correct signature
	ShowAboutDialog()
	OpenLogDirectory()
	ShowCookieGuide()

	// Encryption-related methods
	ShowPasswordDialog(onFinish func()) // Correct signature
	ShowExistingDatabasePasswordDialog(func(string))
	ShowChangePasswordDialog()
	ShowEnableEncryptionDialog(onFinish func()) // Correct signature
	ShowDisableEncryptionDialog()
	UpdateEncryptionMenu()
}

// TabInterface defines the interface for tab components
type TabInterface interface {
	Content() fyne.CanvasObject
	UpdateLanguage()
}

// File: internal/gui/app/app.go

package app

import (
	"fmt"
	"os"
	"sync"
	"time" // Re-introduced for the startup timer

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/menu"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/preview"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/language"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/resources"
)

// Ensure App implements common.AppInterface
var _ common.AppInterface = (*App)(nil)

// App represents the main application structure.
type App struct {
	fyneApp     fyne.App
	window      fyne.Window
	history     *history.HistoryManager
	logger      *logger.Logger
	downloader  *downloader.Downloader
	config      *config.Manager
	currentLang string
	previewArea *preview.PreviewArea // Keep preview area reference
	tabs        *container.AppTabs
	downloadTab interface{}   // Generic interface for download tab
	historyTab  interface{}   // Generic interface for history tab
	db          *database.DB  // New database field
	dbExists    bool          // Track if DB file existed at startup
	dbPath      string        // Store db path
	statusBar   *widget.Label // New status bar widget

	// For one-time startup checks
	initialCheckDone bool
	checkMutex       sync.Mutex
}

// NewApp creates a new instance of the application
func NewApp() *App {
	fyneApp := app.New()
	fyneApp.Settings().SetTheme(resources.CustomTheme{})
	w := fyneApp.NewWindow("GoTube Video Downloader") // Initial title, updated later by language
	w.Resize(fyne.NewSize(800, 600))

	// Initialize Logger
	appLogger := initLogger(w)
	appLogger.Info("--- NewApp Start ---")

	// Initialize Database
	db, dbPath, dbExists := initDatabase(w, appLogger)

	// Initialize Core Components (History, Config, Downloader)
	// NOTE: Config/History loading is DEFERRED until after potential dialogs
	histManager, configManager, dl := initComponents(w, db, appLogger)
	if dl == nil {
		// Show critical error and exit
		msg := "Critical error: Could not initialize core components (Downloader missing). Application will exit."
		fmt.Println(msg)
		if w != nil {
			dialog.ShowError(fmt.Errorf(msg), w)
			w.SetCloseIntercept(func() { w.Close() })
			w.Show()
		}
		os.Exit(1)
		return nil
	}

	a := &App{
		fyneApp:    fyneApp,
		window:     w,
		history:    histManager,
		logger:     appLogger,
		downloader: dl,
		config:     configManager,
		db:         db,
		dbExists:   dbExists,
		dbPath:     dbPath,
	}

	// --- Setup happens immediately, but data load/checks are deferred ---
	a.logger.Info("NewApp: Setting up initial UI structure and language...")
	a.initializeLanguage() // Initialize language based on potentially unloaded config (will use default)
	a.setupUI()            // Build UI structure
	menu.SetupMenu(a)      // Build menu structure
	setAppIcon(a.fyneApp)
	a.setupCloseIntercept()

	// The SetOnShown call is removed as it was incorrect.

	a.logger.Info("--- NewApp End (UI structure built, checks deferred) ---")
	return a
}

// startInitialChecks performs the first-time/startup checks, ensuring it only runs once.
func (a *App) startInitialChecks() {
	a.checkMutex.Lock()
	if a.initialCheckDone {
		a.checkMutex.Unlock()
		a.logger.Debug("startInitialChecks: Already performed.")
		return // Already done
	}
	// Mark as done immediately to prevent race conditions
	a.initialCheckDone = true
	a.checkMutex.Unlock()

	a.logger.Info("startInitialChecks: Performing first-time/startup sequence...")

	// --- Start the sequence ---
	// For a new database, the sequence is: Language Dialog -> Encryption Dialog -> Final Setup
	// For an existing database, it's: Encryption Dialog -> Final Setup
	if !a.dbExists {
		a.logger.Info("startInitialChecks: New DB detected. Starting language check first.")
		// Show language dialog; its onFinish callback will trigger the next step.
		language.ShowFirstRunLanguageDialog(a, a.runEncryptionCheck)
	} else {
		a.logger.Info("startInitialChecks: Existing DB detected. Proceeding to encryption check.")
		// Skip language dialog and go straight to the encryption check.
		a.runEncryptionCheck()
	}
}

// runEncryptionCheck is the second step in the startup sequence.
// It applies any language changes and then handles the database encryption state.
func (a *App) runEncryptionCheck() {
	a.logger.Info("runEncryptionCheck: Starting encryption check step...")

	// Apply the chosen/default language so the encryption dialog is translated.
	a.initializeLanguage()

	// handleDatabaseEncryptionStartup shows password dialogs if needed.
	// Its callback will trigger the final step of the setup process.
	handleDatabaseEncryptionStartup(a, a.dbPath, a.dbExists, a.finishInitialSetup)
}

// finishInitialSetup is the final step in the startup sequence.
// It loads all application data and refreshes the UI.
func (a *App) finishInitialSetup() {
	a.logger.Info("finishInitialSetup: Loading data and applying config...")
	loadDataAndApplyConfig(a) // Load data now that DB is guaranteed to be unlocked

	a.logger.Info("finishInitialSetup: Refreshing all UI elements...")
	// Refresh UI elements that depend on loaded data/config
	a.UpdateUILanguage()     // Ensure UI reflects final language setting
	a.UpdateEncryptionMenu() // Ensure menu reflects final DB encryption state
	a.UpdateSponsorBlockUI() // Ensure SponsorBlock UI reflects loaded config
	if a.historyTab != nil {
		// Refresh the history view to show loaded records
		a.historyTab.(interface{ RefreshHistory() }).RefreshHistory()
	}
	a.logger.Info("finishInitialSetup: Startup sequence complete.")
}

// Run starts the application's event loop.
func (a *App) Run() {
	if a == nil {
		fmt.Println("Error: App is nil, cannot run.")
		return
	}
	a.logger.Info("Starting Fyne App Run (ShowAndRun)...")

	// Schedule the initial checks to run shortly after the event loop starts.
	// This goroutine allows the main window to appear before we show dialogs.
	go func() {
		// Wait a short moment for the event loop and window to stabilize
		time.Sleep(200 * time.Millisecond)
		a.startInitialChecks()
	}()

	// Show the window and run the event loop.
	// The initial checks will be triggered by the timer above.
	a.window.ShowAndRun()
	a.logger.Info("Fyne event loop finished.")
}

// setupCloseIntercept configures actions to take when the window is closing.
func (a *App) setupCloseIntercept() {
	a.window.SetCloseIntercept(func() {
		if a.logger != nil {
			a.logger.Info("Application closing.")
			// Close logger *before* database
			a.logger.Close()
		}
		// Close database connection
		if a.db != nil {
			fmt.Println("Closing database connection...") // Use fmt as logger might be closed
			err := a.db.Close()
			if err != nil {
				fmt.Printf("Error closing database: %v\n", err)
			}
		}
		a.window.Close() // Close the Fyne window
	})
}

// --- AppInterface Implementation ---

func (a *App) GetWindow() fyne.Window     { return a.window }
func (a *App) GetDownloader() interface{} { return a.downloader }
func (a *App) GetConfig() interface{}     { return a.config }
func (a *App) GetHistory() interface{}    { return a.history }
func (a *App) GetLogger() interface{}     { return a.logger }
func (a *App) GetDatabase() interface{}   { return a.db }

func (a *App) UpdateStatus(status string) {
	if a.logger != nil {
		a.logger.Info("Status: %s", status)
	}
	if a.statusBar != nil {
		a.statusBar.SetText(status)
	}
}

// --- Menu Action Implementations (delegated or simple) ---

func (a *App) SelectCustomCookies() { selectCustomCookiesImpl(a) }
func (a *App) SetCookiesFromText(cookieText string) error {
	return setCookiesFromTextImpl(a, cookieText)
}
func (a *App) SetUseBrowserCookies(enabled bool) { setUseBrowserCookiesImpl(a, enabled) }
func (a *App) SetBrowserName(browser string)     { setBrowserNameImpl(a, browser) }
func (a *App) ShowFilenameTemplateDialog()       { showFilenameTemplateDialogImpl(a) }

// ShowLanguageDialog implements the AppInterface method, accepting the callback.
func (a *App) ShowLanguageDialog(onFinish func()) {
	// Pass a callback to update UI *after* the language dialog finishes
	language.ShowLanguageDialog(a, func() {
		// This inner callback runs after the language dialog is dismissed
		a.UpdateUILanguage() // Update UI reflecting the potentially new language
		if onFinish != nil {
			onFinish() // Then call the original callback if provided
		}
	})
}
func (a *App) ShowAboutDialog()  { menu.ShowAboutDialogImpl(a) }
func (a *App) OpenLogDirectory() { menu.OpenLogDirectoryImpl(a) }
func (a *App) ShowCookieGuide()  { menu.ShowCookieGuideImpl(a) }

// --- Encryption-related methods (delegated) ---
// NOTE: These will be called *after* the UI is already running in this flow.

// ShowPasswordDialog implements the AppInterface method, accepting the callback.
func (a *App) ShowPasswordDialog(onFinish func()) { menu.ShowPasswordDialogImpl(a, onFinish) }

// ShowExistingDatabasePasswordDialog implements the AppInterface method.
func (a *App) ShowExistingDatabasePasswordDialog(callback func(string)) {
	menu.ShowExistingDatabasePasswordDialogImpl(a, callback)
}

// ShowChangePasswordDialog implements the AppInterface method.
func (a *App) ShowChangePasswordDialog() { menu.ShowChangePasswordDialogImpl(a) }

// ShowEnableEncryptionDialog implements the AppInterface method, accepting the callback.
func (a *App) ShowEnableEncryptionDialog(onFinish func()) {
	menu.ShowEnableEncryptionDialogImpl(a, onFinish)
}

// ShowDisableEncryptionDialog implements the AppInterface method.
func (a *App) ShowDisableEncryptionDialog() { menu.ShowDisableEncryptionDialogImpl(a) }

// UpdateEncryptionMenu updates the encryption menu based on the current encryption status
func (a *App) UpdateEncryptionMenu() {
	// Update the main menu to reflect the current encryption status
	// Ensure menu is rebuilt only if window exists and has a menu
	if a.window != nil && a.window.MainMenu() != nil {
		menu.SetupMenu(a)
	}
}

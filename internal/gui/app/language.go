// File: internal/gui/app/language.go

package app

import (
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/menu"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// initializeLanguage sets up the application language based on configuration.
func (a *App) initializeLanguage() {
	lang := a.config.GetLanguage()
	if lang == "" {
		lang = "en" // Default to English
	}
	a.currentLang = lang
	i18n.SetLanguage(lang)
	a.window.SetTitle(i18n.Get("app_title"))
}

// UpdateUILanguage updates the UI elements across the application to reflect the current language setting.
func (a *App) UpdateUILanguage() {
	lang := a.config.GetLanguage()
	if lang == "" {
		lang = "en"
	}
	// Only update if language actually changed
	if a.currentLang == lang {
		return
	}

	a.currentLang = lang
	i18n.SetLanguage(lang)

	// Update Window Title
	a.window.SetTitle(i18n.Get("app_title"))

	// Update Tab Titles (check for nil first)
	if a.tabs != nil && len(a.tabs.Items) >= 2 {
		a.tabs.Items[0].Text = i18n.Get("tab_download")
		a.tabs.Items[1].Text = i18n.Get("tab_history")
		a.tabs.Refresh()
	}

	// Update Download Tab Content (check for nil)
	if a.downloadTab != nil {
		a.downloadTab.(interface{ UpdateLanguage() }).UpdateLanguage()
	}

	// Update History Tab Content (check for nil)
	if a.historyTab != nil {
		a.historyTab.(interface{ UpdateLanguage() }).UpdateLanguage()
	}

	// Update Main Menu
	menu.SetupMenu(a) // Rebuild the menu with new translations
}

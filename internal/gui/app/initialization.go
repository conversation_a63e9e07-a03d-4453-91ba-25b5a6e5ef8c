// File: internal/gui/app/initialization.go

package app

import (
	"fmt"
	"os"
	"path/filepath"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/dialog"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/database"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/downloader"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
	// menu package no longer needed here
)

// initLogger initializes the application logger.
func initLogger(w fyne.Window) *logger.Logger {
	// ... (implementation remains the same) ...
	cwd, err := os.Getwd()
	if err != nil {
		dialog.ShowError(fmt.Errorf("Failed to get current directory: %v", err), w)
		cwd = "."
	}
	logDir := filepath.Join(cwd, "logs")
	os.Mkdir<PERSON>ll(logDir, 0777) // Ensure log directory exists

	appLogger, err := logger.NewLogger(logDir)
	if err != nil {
		dialog.ShowError(fmt.Errorf("Failed to initialize logger: %v", err), w)
	} else {
		appLogger.Info("Logger initialized") // Simplified message
	}
	return appLogger
}

// initDatabase initializes the database connection.
func initDatabase(w fyne.Window, appLogger *logger.Logger) (db *database.DB, dbPath string, dbExists bool) {
	// ... (implementation remains the same) ...
	cwd, _ := os.Getwd() // Ignore error as it was handled in initLogger
	dbPath = filepath.Join(cwd, "gotube.db")

	// Check if database file exists
	if _, err := os.Stat(dbPath); err == nil {
		dbExists = true
	}

	// Attempt to open/create the database connection.
	// Start with empty password, NewDB handles checking encryption status.
	db, err := database.NewDB(dbPath, "", appLogger)
	if err != nil {
		dialog.ShowError(fmt.Errorf("failed to initialize database: %v", err), w)
		if appLogger != nil {
			appLogger.Error("Failed to initialize database: %v", err)
		}
		db = nil // Ensure db is nil on failure
	} else if appLogger != nil {
		status := "created"
		if dbExists {
			status = "opened"
		}
		appLogger.Info("Database %s successfully at: %s", status, dbPath)
	}
	return db, dbPath, dbExists
}

// initComponents initializes core application components like history, config, and downloader.
// NOTE: Data loading is now deferred.
func initComponents(w fyne.Window, db *database.DB, appLogger *logger.Logger) (*history.HistoryManager, *config.Manager, *downloader.Downloader) {
	// ... (implementation remains the same, NewHistoryManager/NewConfigManager defer loading) ...
	var histManager *history.HistoryManager
	var configManager *config.Manager
	var dl *downloader.Downloader
	var err error

	// Initialize history manager
	if db != nil {
		histManager, err = history.NewHistoryManager(db) // Defers loading
		if err != nil {
			dialog.ShowError(fmt.Errorf("Failed to initialize history manager: %v", err), w)
		} else if appLogger != nil {
			appLogger.Info("History manager initialized (loading deferred)")
		}
	} else {
		appLogger.Error("initComponents: Database is nil, cannot initialize history.")
	}

	// Initialize config manager
	if db != nil {
		configManager, err = config.NewManager(db) // Defers loading
		if err != nil {
			dialog.ShowError(fmt.Errorf("Failed to initialize config manager: %v", err), w)
		} else if appLogger != nil {
			appLogger.Info("Config manager initialized (loading deferred)")
		}
	} else {
		appLogger.Error("initComponents: Database is nil, cannot initialize config.")
	}

	// Initialize downloader
	if db == nil {
		errMsg := "CRITICAL: Database is required for downloader initialization"
		fmt.Println(errMsg)
		if appLogger != nil {
			appLogger.Error(errMsg)
		}
		dialog.ShowError(fmt.Errorf(errMsg), w)
		return nil, nil, nil // Critical failure
	}

	dl, err = downloader.NewDownloader(db) // Downloader needs DB immediately
	if err != nil {
		errMsg := fmt.Sprintf("CRITICAL: Failed to initialize downloader (yt-dlp or ffmpeg likely missing/broken): %v", err)
		fmt.Println(errMsg)
		if appLogger != nil {
			appLogger.Error(errMsg)
		}
		dialog.ShowError(fmt.Errorf(errMsg), w)
		return nil, nil, nil // Critical failure
	}
	fmt.Println("Downloader initialized successfully")

	return histManager, configManager, dl
}

// applyInitialConfigToDownloader applies settings from the config manager to the downloader instance.
// Should be called *after* config is loaded.
func applyInitialConfigToDownloader(dl *downloader.Downloader, cfg *config.Manager, w fyne.Window, appLogger *logger.Logger) {
	// ... (implementation remains the same) ...
	if dl == nil || cfg == nil {
		if appLogger != nil {
			appLogger.Warn("Skipping applyInitialConfigToDownloader: downloader or config is nil")
		}
		return
	}
	if !cfg.IsLoaded() { // Check if config is actually loaded
		if appLogger != nil {
			appLogger.Warn("Skipping applyInitialConfigToDownloader: config not loaded yet.")
		}
		return
	}
	appLogger.Info("Applying initial config to downloader...")

	template := cfg.GetFilenameTemplate()
	if template != "" {
		dl.SetFilenameTemplate(template)
		appLogger.Info("Applied filename template: %s", template)
	} else {
		appLogger.Warn("Filename template from config was empty.")
	}

	customCookiePath := cfg.GetCustomCookiePath()
	if customCookiePath != "" {
		appLogger.Info("Applying custom cookie path from config: %s", customCookiePath)
		err := dl.SetCustomCookies(customCookiePath)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to load previously saved custom cookie path '%s': %v. Please re-select the file if needed.", customCookiePath, err)
			appLogger.Error(errMsg)
			dialog.ShowInformation("Cookie Load Warning", errMsg, w)
		} else {
			appLogger.Info("Successfully applied custom cookie path from config: %s", customCookiePath)
		}
	}

	sponsorBlock := cfg.GetSponsorBlock()
	dl.SetSponsorBlock(sponsorBlock)
	appLogger.Info("Applied SponsorBlock setting: %v", sponsorBlock)

	useBrowserCookies := cfg.GetBool("use_browser_cookies")
	browserName := cfg.GetString("browser_name")
	if browserName == "" {
		browserName = "chrome"
	}
	dl.SetBrowserName(browserName)
	dl.SetUseBrowserCookies(useBrowserCookies)
	appLogger.Info("Applied browser cookie settings: Use=%v, Browser=%s", useBrowserCookies, browserName)

	appLogger.Info("Finished applying initial configuration to downloader.")
}

// handleDatabaseEncryptionStartup manages the password prompt logic.
// It now takes a postCheckSetup function to call when DB is ready.
func handleDatabaseEncryptionStartup(a *App, dbPath string, dbExists bool, postCheckSetup func()) {
	if a.db == nil {
		a.logger.Error("handleDatabaseEncryptionStartup: Database is nil, cannot proceed.")
		postCheckSetup() // Proceed, but data loading will fail
		return
	}

	if !dbExists {
		a.logger.Info("handleDatabaseEncryptionStartup: New DB. Calling ShowPasswordDialog...")
		// New database: show prompt for initial password *now*
		// Pass the postCheckSetup callback to be called after the dialog finishes.
		a.ShowPasswordDialog(postCheckSetup)

	} else if a.db.IsEncrypted() {
		a.logger.Info("handleDatabaseEncryptionStartup: Encrypted DB. Calling ShowExistingDatabasePasswordDialog...")
		// Existing encrypted database: show prompt to unlock *now*
		// The callback (reopenEncryptedDatabase) will handle calling postCheckSetup.
		a.ShowExistingDatabasePasswordDialog(func(password string) {
			reopenEncryptedDatabase(a, dbPath, password, postCheckSetup)
		})
	} else {
		a.logger.Info("handleDatabaseEncryptionStartup: Existing unencrypted DB. Proceeding directly.")
		// DB exists and is *not* encrypted. No dialog needed.
		// Data loading and config application will happen within postCheckSetup.
		postCheckSetup() // Proceed directly
	}
}

// reopenEncryptedDatabase handles the process of unlocking and reinitializing after password entry.
// It now takes a postCheckSetup function to call on success/failure.
func reopenEncryptedDatabase(a *App, dbPath string, password string, postCheckSetup func()) {
	a.logger.Info("reopenEncryptedDatabase: Attempting to unlock...")
	if a.db != nil {
		err := a.db.Close()
		if err != nil {
			a.logger.Warn("Error closing locked database: %v", err)
		}
	}

	// Reopen the database with the provided password
	newDB, err := database.NewDB(dbPath, password, a.logger)
	if err != nil {
		a.logger.Error("Database unlock failed: %v", err)
		dialog.ShowError(fmt.Errorf("Failed to unlock database: %v", err), a.GetWindow())
		a.db = nil       // Set db to nil on failure
		postCheckSetup() // Proceed, but data loading will fail.
		return
	}

	// Update the database reference
	a.db = newDB
	a.logger.Info("Database unlocked successfully")

	// Re-initialize managers to use the new DB connection
	// NOTE: This assumes initComponents doesn't try to load data again immediately
	a.history, a.config, a.downloader = initComponents(a.window, a.db, a.logger)
	if a.downloader == nil {
		a.logger.Error("Failed to reinitialize downloader after unlocking database")
		dialog.ShowError(fmt.Errorf("Failed to reinitialize critical components after unlocking database"), a.window)
	}

	a.logger.Info("reopenEncryptedDatabase: Proceeding to post-check setup...")
	// Proceed to the final data loading and UI refresh step
	postCheckSetup()
}

// loadDataAndApplyConfig centralizes loading history/config and applying config to downloader.
func loadDataAndApplyConfig(a *App) {
	a.logger.Info("loadDataAndApplyConfig: Starting...")
	loadedSuccessfully := true
	if a.history != nil {
		a.logger.Info("loadDataAndApplyConfig: Loading history...")
		if err := a.history.Load(); err != nil { // Load is now safe to call
			errMsg := fmt.Sprintf("Error loading history: %v", err)
			a.logger.Error(errMsg)
			// dialog.ShowError(fmt.Errorf(errMsg), a.window) // Maybe don't block UI for this
			loadedSuccessfully = false
		} else {
			a.logger.Info("loadDataAndApplyConfig: History loaded.")
		}
	} else {
		a.logger.Warn("loadDataAndApplyConfig: History manager is nil.")
		loadedSuccessfully = false
	}

	if a.config != nil {
		a.logger.Info("loadDataAndApplyConfig: Loading config...")
		if err := a.config.Load(); err != nil { // Load is now safe to call
			errMsg := fmt.Sprintf("Error loading config: %v", err)
			a.logger.Error(errMsg)
			// dialog.ShowError(fmt.Errorf(errMsg), a.window) // Maybe don't block UI for this
			loadedSuccessfully = false
		} else {
			a.logger.Info("loadDataAndApplyConfig: Config loaded.")
		}
	} else {
		a.logger.Warn("loadDataAndApplyConfig: Config manager is nil.")
		loadedSuccessfully = false
	}

	// Apply config to downloader only if config loaded successfully and components exist
	if loadedSuccessfully && a.downloader != nil && a.config != nil {
		a.logger.Info("loadDataAndApplyConfig: Applying config to downloader...")
		applyInitialConfigToDownloader(a.downloader, a.config, a.window, a.logger)
		a.logger.Info("loadDataAndApplyConfig: Finished applying config.")
	} else {
		a.logger.Warn("loadDataAndApplyConfig: Skipping applying config to downloader (load failed or components nil).")
	}
	a.logger.Info("loadDataAndApplyConfig: Finished.")
}

package app

import (
	"fmt"
	"os"
	"path/filepath"

	"fyne.io/fyne/v2"
)

// isSecretStorageInstalled checks if the secretstorage Python module is installed.
// Note: This function might be better placed in a utility or resource package
// if used elsewhere, but keeping it here for consolidation during refactoring.
// func isSecretStorageInstalled() bool {
// 	cmd := exec.Command("python3", "-m", "pip", "show", "secretstorage")
// 	err := cmd.Run()
// 	return err == nil // Exit code 0 means it's installed
// }

// loadAppIcon loads the application icon resource.
func loadAppIcon() fyne.Resource {
	// Try loading from relative path first (dev environment)
	iconPath := "assets/icon.png"
	icon, err := fyne.LoadResourceFromPath(iconPath)
	if err != nil {
		fmt.Printf("Warning: Failed to load icon from '%s': %v. Trying executable directory.\n", iconPath, err)
		// Try loading from path relative to executable (installed environment)
		exePath, err := os.Executable()
		if err == nil {
			iconPath = filepath.Join(filepath.Dir(exePath), "assets", "icon.png")
		}
	}

	if err != nil {
		fmt.Printf("Warning: Failed to load application icon from final path '%s': %v. Using fallback.\n", iconPath, err)
		// Return a minimal fallback icon (1x1 transparent pixel)
		return &fyne.StaticResource{
			StaticName:    "fallback.png",
			StaticContent: []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82},
		}
	}

	fmt.Printf("Successfully loaded icon from: %s\n", iconPath)
	return icon
}

// setAppIcon sets the application icon.
func setAppIcon(fyneApp fyne.App) {
	icon := loadAppIcon()
	if icon != nil {
		fyneApp.SetIcon(icon)
	}
}

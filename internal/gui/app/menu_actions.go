// File: internal/gui/app/menu_actions.go

package app

import (
	"fmt"
	"runtime" // Import runtime
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/storage"
	"fyne.io/fyne/v2/widget"

	dialogs "github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/dialogs" // Import the dialogs package from components
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/resources"
)

// selectCustomCookiesImpl shows a file dialog to select a custom cookie file.
func selectCustomCookiesImpl(a *App) {
	fd := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			if err.Error() != "operation cancelled" { // Don't show error on user cancel
				dialog.ShowError(err, a.GetWindow())
			}
			return
		}
		if reader == nil {
			return // User cancelled
		}
		defer reader.Close()

		path := reader.URI().Path()

		// Use direct access: a.downloader
		if a.downloader == nil {
			dialog.ShowError(fmt.Errorf("Downloader not available"), a.GetWindow())
			return
		}
		err = a.downloader.SetCustomCookies(path) // Use direct field access
		if err != nil {
			// Provide clearer error message
			errMsg := fmt.Sprintf(i18n.Get("error_custom_cookies_dialog")+"\n\nEnsure the file exists, is readable, and is in Netscape format.", err)
			dialog.ShowError(fmt.Errorf(errMsg), a.GetWindow())
			a.UpdateStatus(fmt.Sprintf(i18n.Get("error_custom_cookies"), err))
			return
		}

		// Use direct access: a.config
		if a.config == nil { // Check if config is initialized
			// Handle case where config is nil, maybe log or show a warning
			a.UpdateStatus("Warning: Config manager not initialized, cannot save cookie path.")
			// We can still proceed using the cookie file for the session
			statusMsg := fmt.Sprintf(i18n.Get("status_custom_cookies_set"), path)
			a.UpdateStatus(statusMsg)
			dialog.ShowInformation(i18n.Get("dialog_cookies_title"), statusMsg, a.GetWindow())
			return
		}

		saveErr := a.config.SetCustomCookiePath(path) // Use direct field access
		statusMsg := ""
		if saveErr != nil {
			errMsg := fmt.Sprintf("Cookie file set, but failed to save path to config: %v", saveErr)
			a.UpdateStatus(errMsg)
			dialog.ShowInformation("Config Save Warning", errMsg, a.GetWindow())
			statusMsg = fmt.Sprintf(i18n.Get("status_custom_cookies_set"), path) // Use 'set' status if save failed
		} else {
			statusMsg = fmt.Sprintf(i18n.Get("status_custom_cookies_saved"), path) // Use 'saved' status on success
		}
		a.UpdateStatus(statusMsg)
		dialog.ShowInformation(i18n.Get("dialog_cookies_title"), statusMsg, a.GetWindow())

	}, a.GetWindow())

	fd.SetFilter(storage.NewExtensionFileFilter([]string{".txt"}))
	fd.Show()
}

// setCookiesFromTextImpl sets cookies from a text string in Netscape format.
func setCookiesFromTextImpl(a *App, cookieText string) error {
	if a.downloader == nil { // Use direct access
		return fmt.Errorf("downloader not initialized")
	}

	cookieManager := a.downloader.GetCookieManager() // Use direct access
	if cookieManager == nil {
		return fmt.Errorf("cookie manager not available")
	}

	err := cookieManager.SetCookiesFromText(cookieText)
	if err == nil {
		a.UpdateStatus(i18n.Get("status_cookies_imported"))
	}
	return err
}

// setUseBrowserCookiesImpl enables or disables using browser cookies directly.
// On non-Windows, prompts for installation if 'secretstorage' is missing when enabling.
func setUseBrowserCookiesImpl(a *App, enabled bool) {
	if a.downloader == nil {
		fmt.Println("Error: Downloader not initialized")
		a.UpdateStatus(i18n.Get("error_downloader_not_ready")) // Use translation
		return
	}

	dependencyName := "secretstorage"
	needsCheck := runtime.GOOS != "windows"

	if enabled && needsCheck {
		// --- Check dependency only when enabling on non-Windows ---
		a.UpdateStatus(i18n.Format("status_checking_dependency", dependencyName))
		isInstalled := resources.IsSecretStorageInstalled() // This uses the platform-specific check

		if !isInstalled {
			// Dependency missing, show install prompt
			dialogs.ShowInstallDependencyDialog(
				a.GetWindow(),
				dependencyName,
				resources.InstallSystemPythonModule, // Pass the installation function
				a.UpdateStatus,                      // Pass the status update function
			)
			// DO NOT enable the feature yet. User needs to install and re-try.
			return // Exit the function here
		} else {
			a.UpdateStatus(i18n.Format("status_dependency_found", dependencyName))
			// Proceed to enable below
		}
	}

	// --- Enable/Disable in Downloader and Config ---
	// This part runs if:
	// 1. We are disabling the feature.
	// 2. We are on Windows (no check needed).
	// 3. We are on non-Windows and the check passed.
	a.downloader.SetUseBrowserCookies(enabled)
	if a.config != nil {
		a.config.SetBool("use_browser_cookies", enabled)
	}

	// Update status message
	statusState := i18n.Get("disabled")
	if enabled {
		statusState = i18n.Get("enabled")
	}
	a.UpdateStatus(i18n.Format("status_browser_cookies_toggled", statusState))
}

// setBrowserNameImpl sets the browser to extract cookies from.
func setBrowserNameImpl(a *App, browser string) {
	if a.downloader == nil {
		fmt.Println("Error: Downloader not initialized")
		a.UpdateStatus(i18n.Get("error_downloader_not_ready"))
		return
	}
	a.downloader.SetBrowserName(browser)
	// Also save to config only if config manager is available
	if a.config != nil {
		a.config.SetString("browser_name", a.downloader.GetBrowserName()) // Use direct access + getter
	}
	a.UpdateStatus(i18n.Format("status_browser_set", a.downloader.GetBrowserName()))
}

// showFilenameTemplateDialogImpl displays the dialog for setting the filename template.
func showFilenameTemplateDialogImpl(a *App) {
	if a.config == nil {
		dialog.ShowError(fmt.Errorf("Config manager not available"), a.GetWindow())
		return
	}
	templateEntry := widget.NewEntry()
	templateEntry.SetText(a.config.GetFilenameTemplate())
	templateEntry.SetPlaceHolder("%(title)s-%(id)s.%(ext)s")
	helpText := widget.NewLabel("Available variables:\n" + "%(title)s, %(id)s, %(uploader)s, %(upload_date)s, %(ext)s, " + "%(resolution)s, %(format)s, %(duration)s\n" + "Example: %(upload_date)s/%(uploader)s/%(title)s [%(id)s].%(ext)s")
	helpText.Wrapping = fyne.TextWrapWord
	exampleLabel := widget.NewLabel("")
	exampleLabel.Wrapping = fyne.TextWrapWord
	updateExample := func(text string) {
		effectiveTemplate := text
		if effectiveTemplate == "" {
			effectiveTemplate = "%(title)s-%(id)s.%(ext)s"
		}
		example := effectiveTemplate
		example = strings.ReplaceAll(example, "%(title)s", "video-title")
		example = strings.ReplaceAll(example, "%(id)s", "abc123")
		example = strings.ReplaceAll(example, "%(uploader)s", "channel-name")
		example = strings.ReplaceAll(example, "%(upload_date)s", "20230101")
		example = strings.ReplaceAll(example, "%(ext)s", "mp4")
		example = strings.ReplaceAll(example, "%(resolution)s", "1080p")
		example = strings.ReplaceAll(example, "%(format)s", "mp4")
		example = strings.ReplaceAll(example, "%(duration)s", "300")
		exampleLabel.SetText(fmt.Sprintf("Example output: %s", example))
	}
	templateEntry.OnChanged = updateExample
	updateExample(templateEntry.Text)
	content := container.NewVBox(widget.NewLabel(i18n.Get("filename_template")), templateEntry, widget.NewSeparator(), helpText, widget.NewSeparator(), exampleLabel)
	dlg := dialog.NewCustomConfirm(i18n.Get("custom_filename"), i18n.Get("save"), i18n.Get("cancel"), content, func(confirmed bool) {
		if confirmed {
			template := templateEntry.Text
			// Save to config
			err := a.config.SetFilenameTemplate(template)
			if err != nil {
				dialog.ShowError(fmt.Errorf("Failed to save filename template: %v", err), a.GetWindow())
				return
			}
			// Update downloader only if available
			if a.downloader != nil {
				a.downloader.SetFilenameTemplate(template)
			}
			// Get final template (might have been corrected)
			finalTemplate := a.config.GetFilenameTemplate()
			a.UpdateStatus(fmt.Sprintf("Filename template updated: %s", finalTemplate))
		}
	}, a.GetWindow())
	dlg.Resize(fyne.NewSize(500, 400))
	dlg.Show()
}

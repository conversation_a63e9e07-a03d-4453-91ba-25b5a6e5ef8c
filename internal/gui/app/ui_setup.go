// File: internal/gui/app/ui_setup.go

package app

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/components/preview"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/screens/download"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/screens/history"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/sponsorblock"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// setupUI initializes the main user interface components and layout.
func (a *App) setupUI() {
	a.logger.Info("setupUI: Initializing UI components...")

	// Initialize Preview Area
	a.logger.Info("setupUI: Creating Preview Area...")
	a.previewArea = preview.NewPreviewArea(a.downloader)
	a.previewArea.Hide() // Start hidden
	a.logger.Info("setupUI: Preview Area created.")

	// Initialize Tabs
	a.logger.Info("setupUI: Creating Download Tab...")
	a.downloadTab = download.NewDownloadTab(a)
	a.logger.Info("setupUI: Download Tab created.")
	a.logger.Info("setupUI: Creating History Tab...")
	a.historyTab = history.NewHistoryTab(a)
	a.logger.Info("setupUI: History Tab created.")

	// --- Setup Dependencies Between UI Components ---
	a.logger.Info("setupUI: Linking UI components...")
	// Link history tab "Load URL" action to download tab
	a.historyTab.(interface{ SetLoadURLFunc(func([]string)) }).SetLoadURLFunc(func(urls []string) {
		if a.downloadTab != nil {
			a.downloadTab.(interface{ LoadURLs([]string) }).LoadURLs(urls) // Update URL entry
			if a.tabs != nil {
				a.tabs.SelectIndex(0) // Switch to download tab
			}
		}
	})

	// Connect download tab to preview area
	if a.downloadTab != nil {
		a.downloadTab.(interface{ SetPreviewArea(interface{}) }).SetPreviewArea(a.previewArea)
	}

	// Connect download tab completion to history refresh
	if a.downloadTab != nil && a.historyTab != nil {
		a.downloadTab.(interface{ SetRefreshHistoryFunc(func()) }).SetRefreshHistoryFunc(
			a.historyTab.(interface{ RefreshHistory() }).RefreshHistory)
	}
	a.logger.Info("setupUI: UI components linked.")

	// --- Assemble Layout ---
	a.logger.Info("setupUI: Assembling layout...")
	// Create content for the download tab, incorporating the preview area
	downloadTabContent := container.NewBorder(
		nil, nil, nil, a.previewArea.Container(), // Preview on the right
		a.downloadTab.(interface{ Content() fyne.CanvasObject }).Content(), // Main download controls on the left/center
	)

	// Create the main AppTabs container
	a.tabs = container.NewAppTabs(
		container.NewTabItem(i18n.Get("tab_download"), downloadTabContent),
		container.NewTabItem(i18n.Get("tab_history"), a.historyTab.(interface{ Content() fyne.CanvasObject }).Content()),
	)
	a.logger.Info("setupUI: Layout assembled.")

	// Initialize and set up the status bar
	a.statusBar = widget.NewLabel(i18n.Get("status_idle"))
	a.statusBar.Wrapping = fyne.TextTruncate

	// Create the main content container with the status bar at the bottom
	mainContent := container.NewBorder(
		nil,         // top
		a.statusBar, // bottom
		nil,         // left
		nil,         // right
		a.tabs,      // center
	)

	// --- Final Setup ---
	a.logger.Info("setupUI: Setting window content...")
	a.window.SetContent(mainContent) // Set the container with the status bar
	a.logger.Info("setupUI: Window content set.")
}

// UpdateSponsorBlockUI updates all UI elements related to SponsorBlock.
func (a *App) UpdateSponsorBlockUI() {
	if a.downloadTab != nil {
		a.logger.Info("UpdateSponsorBlockUI: Updating...")
		// Delegate to the sponsorblock package, passing necessary components
		// Ensure config is loaded before calling this
		if a.config != nil && a.config.IsLoaded() { // Add check if config is loaded
			sponsorblock.UpdateSponsorBlockUI(a, a.downloadTab.(interface{ UpdateSponsorBlockCheckbox(bool) })) // Use type assertion
			a.logger.Info("UpdateSponsorBlockUI: Update complete.")
		} else {
			a.logger.Warn("UpdateSponsorBlockUI: Skipping - Config not loaded yet.")
		}
	} else {
		if a.logger != nil {
			a.logger.Warn("UpdateSponsorBlockUI called before downloadTab was initialized.")
		}
	}
}

// Add IsLoaded method to ConfigManager interface/struct if it doesn't exist
// Gofile: internal/config/config.go (add this method)
/*
func (m *Manager) IsLoaded() bool {
	return m.loaded
}
*/

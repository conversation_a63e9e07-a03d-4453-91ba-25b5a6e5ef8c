// File: internal/gui/language/language_dialog.go

package language

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/config"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/gui/common"
	"github.com/hedgehog/GoTube-Video-Downloader/internal/i18n"
)

// ShowLanguageDialog shows a dialog for selecting the application language.
// Takes an onFinish callback which is executed after the dialog is closed (saved or cancelled).
func ShowLanguageDialog(a common.AppInterface, onFinish func()) {
	// Get available languages
	availableLanguages := i18n.GetAvailableLanguages()

	// Get config manager safely
	cfgMgrInterface := a.GetConfig()
	var configManager *config.Manager
	if cfgMgrInterface != nil {
		if mgr, ok := cfgMgrInterface.(*config.Manager); ok {
			configManager = mgr
		}
	}

	currentLang := "en" // Default
	if configManager != nil {
		currentLang = configManager.GetLanguage()
	}

	// Create radio group for language selection
	radioGroup := widget.NewRadioGroup([]string{}, nil)

	// Create a map to store language codes by display name
	langCodeByName := make(map[string]string)

	// Populate radio options
	var options []string
	foundCurrent := false
	for code, name := range availableLanguages {
		options = append(options, name)
		langCodeByName[name] = code

		// Set the current language as selected
		if code == currentLang {
			radioGroup.SetSelected(name)
			foundCurrent = true
		}
	}
	// If currentLang from config wasn't in the list, select English default
	if !foundCurrent && len(i18n.SupportedLanguages) > 0 { // Ensure SupportedLanguages is not empty
		radioGroup.SetSelected(i18n.SupportedLanguages[0].Name) // Select English Name
	}

	// Set radio options
	radioGroup.Options = options
	radioGroup.Refresh() // Ensure options are displayed

	// Create content layout
	content := container.NewVBox(
		widget.NewLabel(i18n.Get("select_language")),
		widget.NewSeparator(),
		radioGroup,
	)

	// Create dialog
	dlg := dialog.NewCustomConfirm(
		i18n.Get("language_settings"),
		i18n.Get("save"),
		i18n.Get("cancel"),
		content,
		func(confirmed bool) {
			languageChanged := false // Flag to track if language was actually changed
			if confirmed && radioGroup.Selected != "" {
				// Get the selected language code
				selectedLangCode := langCodeByName[radioGroup.Selected]

				// Check if language is actually different from current
				currentCode := i18n.GetCurrentLanguage()
				if selectedLangCode != currentCode {
					// Save the language setting only if config manager is available
					if configManager != nil {
						err := configManager.SetLanguage(selectedLangCode)
						if err != nil {
							dialog.ShowError(fmt.Errorf("Failed to save language setting: %v", err), a.GetWindow())
							// Don't proceed if save failed
						} else {
							// Update the UI language immediately
							i18n.SetLanguage(selectedLangCode)
							languageChanged = true // Mark that language was changed

							// Notify the app about the language change
							a.UpdateStatus(fmt.Sprintf("Language changed to %s", radioGroup.Selected))

							// Show confirmation dialog only if save was successful
							dialog.ShowInformation(
								i18n.Get("language_updated_title"),
								i18n.Get("language_updated_message"),
								a.GetWindow(),
							)
						}
					} else {
						// Config manager not available, maybe first run?
						// Just update i18n for this session
						i18n.SetLanguage(selectedLangCode)
						languageChanged = true
						a.UpdateStatus(fmt.Sprintf("Language set to %s (config unavailable)", radioGroup.Selected))
					}
				} else {
					// Language selected is the same as current, no action needed
					a.UpdateStatus("Language setting unchanged.")
				}
			}

			// Call the onFinish callback regardless of save success/failure/cancel
			if onFinish != nil {
				// If language was changed, check if the app implements UpdateUILanguage and call it *before* onFinish.
				if languageChanged {
					// Check if the app implements the UpdateUILanguage method
					if updater, ok := a.(interface{ UpdateUILanguage() }); ok {
						// Call the UpdateUILanguage method to update all UI elements including the menu
						updater.UpdateUILanguage()
					}
				}
				onFinish() // Signal that the language dialog process is complete
			}
		},
		a.GetWindow(),
	)

	dlg.Resize(fyne.NewSize(300, 250))
	dlg.Show()
}

// ShowFirstRunLanguageDialog is a wrapper specifically for the first run scenario.
// It ensures the onFinish callback is always called.
func ShowFirstRunLanguageDialog(a common.AppInterface, onFinish func()) {
	// Use the main ShowLanguageDialog function, passing the onFinish callback
	ShowLanguageDialog(a, onFinish)
}

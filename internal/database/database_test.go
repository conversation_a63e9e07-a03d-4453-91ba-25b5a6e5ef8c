// File: internal/database/database_test.go

package database

import (
	"database/sql"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
)

// TestMain sets up and tears down the test environment
func TestMain(m *testing.M) {
	// Setup test environment
	tempDir, err := os.MkdirTemp("", "gotube-test-*")
	if err != nil {
		panic("Failed to create temp directory for tests: " + err.Error())
	}

	// Run tests
	exitCode := m.Run()

	// Cleanup
	os.RemoveAll(tempDir)

	os.Exit(exitCode)
}

// createTestDB creates a temporary database for testing
func createTestDB(t *testing.T) (*DB, string, func()) {
	t.Helper()

	// Create a temporary directory for the test database
	tempDir, err := os.MkdirTemp("", "gotube-db-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	// Create a test database path
	dbPath := filepath.Join(tempDir, "test.db")

	// Create a test logger
	testLogger, _ := logger.NewLogger(filepath.Join(tempDir, "test.log"))

	// Create a test database
	db, err := NewDB(dbPath, "test-password", testLogger)
	if err != nil {
		os.RemoveAll(tempDir)
		t.Fatalf("Failed to create test database: %v", err)
	}

	// Return cleanup function
	cleanup := func() {
		db.Close()
		os.RemoveAll(tempDir)
	}

	return db, dbPath, cleanup
}

// TestDatabaseInitialization tests database initialization
func TestDatabaseInitialization(t *testing.T) {
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Check if database was initialized correctly
	if db == nil {
		t.Fatal("Database should not be nil")
	}

	// Check if encryption is enabled (should be true with password)
	if !db.IsEncrypted() {
		t.Error("Database should be encrypted with password")
	}

	// Check if encryptor was created
	if db.GetEncryptor() == nil {
		t.Error("Encryptor should not be nil")
	}
}

// TestDatabaseEncryption tests database encryption
func TestDatabaseEncryption(t *testing.T) {
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Test encryption and decryption
	testData := []byte("test data")
	encrypted, err := db.GetEncryptor().Encrypt(testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}

	decrypted, err := db.GetEncryptor().Decrypt(encrypted)
	if err != nil {
		t.Fatalf("Failed to decrypt data: %v", err)
	}

	// Check if decrypted data matches original
	if string(decrypted) != string(testData) {
		t.Errorf("Decrypted data does not match original: got %s, want %s", decrypted, testData)
	}
}

// TestConfigOperations tests config operations
func TestConfigOperations(t *testing.T) {
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Test setting and getting config values
	testKey := "test_key"
	testValue := "test_value"

	// Set config value
	err := db.SetConfig(testKey, testValue, false)
	if err != nil {
		t.Fatalf("Failed to set config value: %v", err)
	}

	// Get config value
	value, isEncrypted, err := db.GetConfig(testKey)
	if err != nil {
		t.Fatalf("Failed to get config value: %v", err)
	}

	// Check if value is not marked as encrypted
	if isEncrypted {
		t.Error("Config value should not be marked as encrypted")
	}

	// Check if value matches
	if value != testValue {
		t.Errorf("Config value does not match: got %s, want %s", value, testValue)
	}

	// Test encrypted config values
	encryptedKey := "encrypted_key"
	encryptedValue := "sensitive_value"

	// Set encrypted config value
	err = db.SetConfig(encryptedKey, encryptedValue, true)
	if err != nil {
		t.Fatalf("Failed to set encrypted config value: %v", err)
	}

	// Get encrypted config value
	value, isEncrypted, err = db.GetConfig(encryptedKey)
	if err != nil {
		t.Fatalf("Failed to get encrypted config value: %v", err)
	}

	// Check if value is marked as encrypted
	if !isEncrypted {
		t.Error("Config value should be marked as encrypted")
	}

	// Check if value matches
	if value != encryptedValue {
		t.Errorf("Encrypted config value does not match: got %s, want %s", value, encryptedValue)
	}
}

// TestHistoryOperations tests history operations
func TestHistoryOperations(t *testing.T) {
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Create a test history entry
	entry := HistoryEntry{
		URL:       "https://www.youtube.com/watch?v=test",
		Title:     "Test Video",
		Format:    "mp4",
		Quality:   "720p",
		Path:      "/path/to/video.mp4",
		Timestamp: time.Now(),
	}

	// Add history entry
	id, err := db.AddHistory(entry)
	if err != nil {
		t.Fatalf("Failed to add history entry: %v", err)
	}

	// Check if ID is valid
	if id <= 0 {
		t.Errorf("Invalid history entry ID: %d", id)
	}

	// Get history entries
	entries, err := db.GetHistory()
	if err != nil {
		t.Fatalf("Failed to get history entries: %v", err)
	}

	// Check if entry was added
	if len(entries) != 1 {
		t.Errorf("Expected 1 history entry, got %d", len(entries))
	}

	// Check if entry matches
	if entries[0].URL != entry.URL || entries[0].Title != entry.Title {
		t.Errorf("History entry does not match: got %+v, want %+v", entries[0], entry)
	}

	// Test clearing history
	err = db.ClearHistory()
	if err != nil {
		t.Fatalf("Failed to clear history: %v", err)
	}

	// Check if history was cleared
	entries, err = db.GetHistory()
	if err != nil {
		t.Fatalf("Failed to get history entries after clearing: %v", err)
	}

	if len(entries) != 0 {
		t.Errorf("Expected 0 history entries after clearing, got %d", len(entries))
	}
}

// TestCookieOperations tests cookie operations
func TestCookieOperations(t *testing.T) {
	db, _, cleanup := createTestDB(t)
	defer cleanup()

	// Create a test cookie entry
	cookie := CookieEntry{
		Domain:   "youtube.com",
		Path:     "/",
		Name:     "test_cookie",
		Value:    "test_value",
		Secure:   true,
		HttpOnly: true,
		Expires:  sql.NullTime{Time: time.Now().Add(24 * time.Hour), Valid: true},
	}

	// Add cookie
	id, err := db.AddCookie(cookie)
	if err != nil {
		t.Fatalf("Failed to add cookie: %v", err)
	}

	// Check if ID is valid
	if id <= 0 {
		t.Errorf("Invalid cookie ID: %d", id)
	}

	// Get all cookies
	cookies, err := db.GetAllCookies()
	if err != nil {
		t.Fatalf("Failed to get cookies: %v", err)
	}

	// Check if cookie was added
	if len(cookies) != 1 {
		t.Errorf("Expected 1 cookie, got %d", len(cookies))
	}

	// Check if cookie matches
	if cookies[0].Domain != cookie.Domain || cookies[0].Name != cookie.Name {
		t.Errorf("Cookie does not match: got %+v, want %+v", cookies[0], cookie)
	}

	// Test deleting cookie
	err = db.DeleteCookie(cookie.Domain, cookie.Path, cookie.Name)
	if err != nil {
		t.Fatalf("Failed to delete cookie: %v", err)
	}

	// Check if cookie was deleted
	cookies, err = db.GetAllCookies()
	if err != nil {
		t.Fatalf("Failed to get cookies after deletion: %v", err)
	}

	if len(cookies) != 0 {
		t.Errorf("Expected 0 cookies after deletion, got %d", len(cookies))
	}
}

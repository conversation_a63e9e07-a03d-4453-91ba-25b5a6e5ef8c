package database

import (
	"fmt"
	"database/sql"
)

// SetConfig sets a configuration value
func (d *DB) SetConfig(key, value string, encrypt bool) error {
	if d.logger != nil {
		if encrypt {
			d.logger.Info("Setting encrypted config value for key: %s", key)
		} else {
			d.logger.Info("Setting config value for key: %s", key)
		}
	}

	var storedValue string
	var err error

	if encrypt {
		// Encrypt the value
		storedValue, err = d.encryptor.Encrypt([]byte(value))
		if err != nil {
			if d.logger != nil {
				d.logger.Error("Failed to encrypt config value for key %s: %v", key, err)
			}
			return fmt.Errorf("failed to encrypt config value: %w", err)
		}
	} else {
		storedValue = value
	}

	// Insert or update the config entry
	_, err = d.db.Exec(
		"INSERT OR REPLACE INTO config (key, value, is_encrypted) VALUES (?, ?, ?)",
		key, storedValue, encrypt,
	)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to set config for key %s: %v", key, err)
		}
		return fmt.Errorf("failed to set config: %w", err)
	}

	if d.logger != nil {
		d.logger.Info("Successfully set config value for key: %s", key)
	}

	return nil
}

// GetConfig gets a configuration value
func (d *DB) GetConfig(key string) (string, bool, error) {
	var value string
	var isEncrypted bool

	err := d.db.QueryRow(
		"SELECT value, is_encrypted FROM config WHERE key = ?",
		key,
	).Scan(&value, &isEncrypted)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", false, nil
		}
		return "", false, fmt.Errorf("failed to get config: %w", err)
	}

	if isEncrypted {
		// Decrypt the value
		decrypted, err := d.encryptor.Decrypt(value)
		if err != nil {
			return "", true, fmt.Errorf("failed to decrypt config value: %w", err)
		}
		return string(decrypted), true, nil
	}

	return value, false, nil
}

// getAllConfig gets all configuration entries
func (d *DB) getAllConfig() ([]ConfigEntry, error) {
	rows, err := d.db.Query("SELECT key, value, is_encrypted FROM config")
	if err != nil {
		return nil, fmt.Errorf("failed to query config entries: %w", err)
	}
	defer rows.Close()

	var configs []ConfigEntry
	for rows.Next() {
		var key, value string
		var isEncrypted bool
		if err := rows.Scan(&key, &value, &isEncrypted); err != nil {
			return nil, fmt.Errorf("failed to scan config entry: %w", err)
		}

		if isEncrypted {
			// Decrypt the value
			decrypted, err := d.encryptor.Decrypt(value)
			if err != nil {
				return nil, fmt.Errorf("failed to decrypt config value: %w", err)
			}
			value = string(decrypted)
		}

		configs = append(configs, ConfigEntry{
			Key:         key,
			Value:       value,
			IsEncrypted: isEncrypted,
		})
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating config rows: %w", err)
	}

	return configs, nil
}

// DeleteConfig deletes a configuration value
func (d *DB) DeleteConfig(key string) error {
	_, err := d.db.Exec("DELETE FROM config WHERE key = ?", key)
	if err != nil {
		return fmt.Errorf("failed to delete config: %w", err)
	}
	return nil
}
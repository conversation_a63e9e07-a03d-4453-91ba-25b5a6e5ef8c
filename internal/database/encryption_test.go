package database

import (
	"testing"
)

// TestEncryptorCreation tests creating an encryptor
func TestEncryptorCreation(t *testing.T) {
	// Generate a salt
	salt, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt: %v", err)
	}

	// Create an encryptor
	encryptor, err := NewEncryptor("test-password", salt)
	if err != nil {
		t.Fatalf("Failed to create encryptor: %v", err)
	}

	// Check if encryptor was created
	if encryptor == nil {
		t.Fatal("Encryptor should not be nil")
	}
}

// TestEncryptionDecryption tests encryption and decryption
func TestEncryptionDecryption(t *testing.T) {
	// Generate a salt
	salt, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt: %v", err)
	}

	// Create an encryptor
	encryptor, err := NewEncryptor("test-password", salt)
	if err != nil {
		t.Fatalf("Failed to create encryptor: %v", err)
	}

	// Test data
	testData := []byte("This is a test string to encrypt and decrypt")

	// Encrypt data
	encrypted, err := encryptor.Encrypt(testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}

	// Check if encrypted data is not empty
	if encrypted == "" {
		t.Fatal("Encrypted data should not be empty")
	}

	// Decrypt data
	decrypted, err := encryptor.Decrypt(encrypted)
	if err != nil {
		t.Fatalf("Failed to decrypt data: %v", err)
	}

	// Check if decrypted data matches original
	if string(decrypted) != string(testData) {
		t.Errorf("Decrypted data does not match original: got %s, want %s", decrypted, testData)
	}
}

// TestEncryptionWithDifferentPasswords tests encryption with different passwords
func TestEncryptionWithDifferentPasswords(t *testing.T) {
	// Generate a salt
	salt, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt: %v", err)
	}

	// Create an encryptor with password1
	encryptor1, err := NewEncryptor("password1", salt)
	if err != nil {
		t.Fatalf("Failed to create encryptor1: %v", err)
	}

	// Create an encryptor with password2
	encryptor2, err := NewEncryptor("password2", salt)
	if err != nil {
		t.Fatalf("Failed to create encryptor2: %v", err)
	}

	// Test data
	testData := []byte("This is a test string to encrypt and decrypt")

	// Encrypt data with encryptor1
	encrypted, err := encryptor1.Encrypt(testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data with encryptor1: %v", err)
	}

	// Try to decrypt with encryptor2 (should fail)
	_, err = encryptor2.Decrypt(encrypted)
	if err == nil {
		t.Fatal("Decryption with wrong password should fail")
	}
}

// TestEncryptionWithEmptyPassword tests encryption with empty password
func TestEncryptionWithEmptyPassword(t *testing.T) {
	// Generate a salt
	salt, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt: %v", err)
	}

	// Create an encryptor with empty password
	encryptor, err := NewEncryptor("", salt)
	if err != nil {
		t.Fatalf("Failed to create encryptor with empty password: %v", err)
	}

	// Test data
	testData := []byte("This is a test string to encrypt and decrypt")

	// Encrypt data
	encrypted, err := encryptor.Encrypt(testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data with empty password: %v", err)
	}

	// Decrypt data
	decrypted, err := encryptor.Decrypt(encrypted)
	if err != nil {
		t.Fatalf("Failed to decrypt data with empty password: %v", err)
	}

	// Check if decrypted data matches original
	if string(decrypted) != string(testData) {
		t.Errorf("Decrypted data does not match original: got %s, want %s", decrypted, testData)
	}
}

// TestSaltGeneration tests salt generation
func TestSaltGeneration(t *testing.T) {
	// Generate a salt
	salt1, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt1: %v", err)
	}

	// Check if salt1 has the correct size
	if len(salt1) != saltSize {
		t.Errorf("Salt1 has incorrect size: got %d, want %d", len(salt1), saltSize)
	}

	// Generate another salt
	salt2, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt2: %v", err)
	}

	// Check if salt2 has the correct size
	if len(salt2) != saltSize {
		t.Errorf("Salt2 has incorrect size: got %d, want %d", len(salt2), saltSize)
	}

	// Check if salts are different (they should be random)
	if string(salt1) == string(salt2) {
		t.Error("Generated salts should be different")
	}
}

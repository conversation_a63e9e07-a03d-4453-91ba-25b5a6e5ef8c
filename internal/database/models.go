package database

import (
	"database/sql"
	"encoding/json"
	"time"
)

// ConfigEntry represents a configuration entry in the database
type ConfigEntry struct {
	Key         string
	Value       string
	IsEncrypted bool
}

// HistoryEntry represents a download history entry in the database
type HistoryEntry struct {
	ID          int64
	URL         string
	Title       string
	Format      string
	Quality     string
	Path        string
	Timestamp   time.Time
	Description sql.NullString
	Thumbnail   sql.NullString
	Duration    sql.NullInt64
	Author      sql.NullString
}

// CookieEntry represents a cookie entry in the database
type CookieEntry struct {
	ID       int64
	Domain   string
	Path     string
	Name     string
	Value    string // Encrypted
	Secure   bool
	HttpOnly bool
	Expires  sql.NullTime
}

// StringValue is a helper for storing string values in the config
type StringValue struct {
	Value string
}

// BoolValue is a helper for storing boolean values in the config
type BoolValue struct {
	Value bool
}

// MarshalStringMap marshals a map of strings to JSON
func MarshalStringMap(m map[string]string) (string, error) {
	if m == nil {
		m = make(map[string]string)
	}
	data, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// UnmarshalStringMap unmarshals a JSON string to a map of strings
func UnmarshalStringMap(s string) (map[string]string, error) {
	m := make(map[string]string)
	if s == "" {
		return m, nil
	}
	err := json.Unmarshal([]byte(s), &m)
	return m, err
}

// MarshalBoolMap marshals a map of booleans to JSON
func MarshalBoolMap(m map[string]bool) (string, error) {
	if m == nil {
		m = make(map[string]bool)
	}
	data, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// UnmarshalBoolMap unmarshals a JSON string to a map of booleans
func UnmarshalBoolMap(s string) (map[string]bool, error) {
	m := make(map[string]bool)
	if s == "" {
		return m, nil
	}
	err := json.Unmarshal([]byte(s), &m)
	return m, err
}
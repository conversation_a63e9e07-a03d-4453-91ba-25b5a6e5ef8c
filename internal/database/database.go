// File: internal/database/database.go

package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"

	"github.com/hedgehog/GoTube-Video-Downloader/internal/logger"
	_ "github.com/mattn/go-sqlite3"
)

// DB represents the application database
type DB struct {
	db          *sql.DB
	encryptor   *Encryptor
	path        string
	isEncrypted bool
	password    string
	logger      *logger.Logger
}

// NewDB creates a new database connection
func NewDB(dbPath string, password string, logger *logger.Logger) (*DB, error) {
	// Create directory if it doesn't exist
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// Open database connection
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Check if database exists and is initialized
	var saltStr string
	isNewDB := false
	var isEncrypted bool

	// Check if metadata table exists
	err = db.QueryRow("SELECT name FROM sqlite_master WHERE type='table' AND name='metadata'").Scan(&saltStr)
	if err != nil {
		// Table doesn't exist, new database
		isNewDB = true
	} else {
		// Check if salt exists
		err = db.QueryRow("SELECT value FROM metadata WHERE key = 'salt'").Scan(&saltStr)
		if err != nil {
			if err == sql.ErrNoRows {
				// Database exists but no salt found
				isNewDB = true
			} else {
				// Check if tables exist
				var tableCount int
				err = db.QueryRow("SELECT count(*) FROM sqlite_master WHERE type='table'").Scan(&tableCount)
				if err != nil || tableCount == 0 {
					isNewDB = true
				}
			}
		}

		// Check if encryption is enabled
		var encryptionStatus string
		err = db.QueryRow("SELECT value FROM metadata WHERE key = 'encryption_enabled'").Scan(&encryptionStatus)
		if err == nil {
			isEncrypted = encryptionStatus == "true"
		}
	}

	var salt []byte
	if isNewDB {
		// Generate a new salt
		salt, err = GenerateSalt()
		if err != nil {
			db.Close()
			return nil, fmt.Errorf("failed to generate salt: %w", err)
		}
		// New databases are encrypted by default if a password is provided
		isEncrypted = password != ""
	} else {
		// Use existing salt
		salt = []byte(saltStr)
	}

	// Create encryptor
	encryptor, err := NewEncryptor(password, salt)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to create encryptor: %w", err)
	}

	// Create database instance
	database := &DB{
		db:          db,
		encryptor:   encryptor,
		path:        dbPath,
		isEncrypted: isEncrypted,
		password:    password,
		logger:      logger,
	}

	// Log database creation
	if logger != nil {
		logger.Info("Database instance created at: %s", dbPath)
		if isEncrypted {
			logger.Info("Database encryption is enabled")
		} else {
			logger.Info("Database encryption is disabled")
		}
	}

	// Initialize database if new
	if isNewDB {
		if err := database.initialize(salt, isEncrypted); err != nil {
			db.Close()
			return nil, fmt.Errorf("failed to initialize database: %w", err)
		}
	}

	return database, nil
}

// initialize creates the database tables
func (d *DB) initialize(salt []byte, isEncrypted bool) error {
	if d.logger != nil {
		d.logger.Info("Initializing new database with tables")
	}
	// Create metadata table
	_, err := d.db.Exec(`
		CREATE TABLE IF NOT EXISTS metadata (
			key TEXT PRIMARY KEY,
			value TEXT NOT NULL
		)
	`)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to create metadata table: %v", err)
		}
		return fmt.Errorf("failed to create metadata table: %w", err)
	}

	// Store salt in metadata
	_, err = d.db.Exec("INSERT INTO metadata (key, value) VALUES (?, ?)", "salt", string(salt))
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to store salt: %v", err)
		}
		return fmt.Errorf("failed to store salt: %w", err)
	}

	// Store encryption status
	encryptionStatus := "false"
	if isEncrypted {
		encryptionStatus = "true"
	}
	_, err = d.db.Exec("INSERT INTO metadata (key, value) VALUES (?, ?)", "encryption_enabled", encryptionStatus)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to store encryption status: %v", err)
		}
		return fmt.Errorf("failed to store encryption status: %w", err)
	}

	// Create config table
	_, err = d.db.Exec(`
		CREATE TABLE IF NOT EXISTS config (
			key TEXT PRIMARY KEY,
			value TEXT NOT NULL,
			is_encrypted INTEGER NOT NULL DEFAULT 0
		)
	`)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to create config table: %v", err)
		}
		return fmt.Errorf("failed to create config table: %w", err)
	}

	// Create history table
	_, err = d.db.Exec(`
		CREATE TABLE IF NOT EXISTS history (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			url TEXT NOT NULL,
			title TEXT NOT NULL,
			format TEXT NOT NULL,
			quality TEXT NOT NULL,
			path TEXT NOT NULL,
			timestamp DATETIME NOT NULL,
			description TEXT,
			thumbnail TEXT,
			duration INTEGER,
			author TEXT
		)
	`)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to create history table: %v", err)
		}
		return fmt.Errorf("failed to create history table: %w", err)
	}

	// Create cookies table
	_, err = d.db.Exec(`
		CREATE TABLE IF NOT EXISTS cookies (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			domain TEXT NOT NULL,
			path TEXT NOT NULL,
			name TEXT NOT NULL,
			value TEXT NOT NULL,
			secure INTEGER NOT NULL DEFAULT 0,
			http_only INTEGER NOT NULL DEFAULT 0,
			expires DATETIME,
			UNIQUE(domain, path, name)
		)
	`)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to create cookies table: %v", err)
		}
		return fmt.Errorf("failed to create cookies table: %w", err)
	}

	if d.logger != nil {
		d.logger.Info("Database initialization completed successfully")
	}
	return nil
}

// Close closes the database connection
func (d *DB) Close() error {
	if d.logger != nil {
		d.logger.Info("Closing database connection")
	}
	return d.db.Close()
}

// GetPath returns the database file path
func (d *DB) GetPath() string {
	return d.path
}

// GetEncryptor returns the encryptor
func (d *DB) GetEncryptor() *Encryptor {
	return d.encryptor
}

// IsEncrypted returns whether the database is encrypted
func (d *DB) IsEncrypted() bool {
	return d.isEncrypted
}

// Transaction executes a function within a transaction
func (d *DB) Transaction(fn func(*sql.Tx) error) error {
	tx, err := d.db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		}
	}()
	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit()
}
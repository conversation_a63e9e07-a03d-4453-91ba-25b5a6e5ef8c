package database

import (
	"fmt"
	"os"
	"strings"
)

// Add<PERSON><PERSON><PERSON> adds a cookie
func (d *DB) AddCookie(cookie CookieEntry) (int64, error) {
	if d.logger != nil {
		d.logger.Info("Adding cookie: %s (domain=%s, path=%s)", cookie.Name, cookie.Domain, cookie.Path)
	}
	// Encrypt the cookie value
	encryptedValue, err := d.encryptor.Encrypt([]byte(cookie.Value))
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to encrypt cookie value: %v", err)
		}
		return 0, fmt.Errorf("failed to encrypt cookie value: %w", err)
	}

	result, err := d.db.Exec(
		`INSERT OR REPLACE INTO cookies
		(domain, path, name, value, secure, http_only, expires)
		VALUES (?, ?, ?, ?, ?, ?, ?)`,
		cookie.Domain, cookie.Path, cookie.Name, encryptedValue,
		cookie.Secure, cookie.HttpOnly, cookie.Expires,
	)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to add cookie: %v", err)
		}
		return 0, fmt.Errorf("failed to add cookie: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get last insert ID for cookie: %v", err)
		}
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}

	if d.logger != nil {
		d.logger.Info("Successfully added cookie with ID: %d", id)
	}

	return id, nil
}

// GetCookies gets all cookies for a domain
func (d *DB) GetCookies(domain string) ([]CookieEntry, error) {
	rows, err := d.db.Query(
		`SELECT id, domain, path, name, value, secure, http_only, expires
		FROM cookies WHERE domain = ? OR domain LIKE ?`,
		domain, "%."+domain,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get cookies: %w", err)
	}
	defer rows.Close()

	var cookies []CookieEntry
	for rows.Next() {
		var cookie CookieEntry
		var encryptedValue string
		err := rows.Scan(
			&cookie.ID, &cookie.Domain, &cookie.Path, &cookie.Name,
			&encryptedValue, &cookie.Secure, &cookie.HttpOnly, &cookie.Expires,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan cookie: %w", err)
		}

		// Decrypt the cookie value
		decrypted, err := d.encryptor.Decrypt(encryptedValue)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt cookie value: %w", err)
		}
		cookie.Value = string(decrypted)

		cookies = append(cookies, cookie)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating cookie rows: %w", err)
	}

	return cookies, nil
}

// GetAllCookies gets all cookies
func (d *DB) GetAllCookies() ([]CookieEntry, error) {
	rows, err := d.db.Query(
		`SELECT id, domain, path, name, value, secure, http_only, expires FROM cookies`,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get all cookies: %w", err)
	}
	defer rows.Close()

	var cookies []CookieEntry
	for rows.Next() {
		var cookie CookieEntry
		var encryptedValue string
		err := rows.Scan(
			&cookie.ID, &cookie.Domain, &cookie.Path, &cookie.Name,
			&encryptedValue, &cookie.Secure, &cookie.HttpOnly, &cookie.Expires,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan cookie: %w", err)
		}

		// Decrypt the cookie value
		decrypted, err := d.encryptor.Decrypt(encryptedValue)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt cookie value: %w", err)
		}
		cookie.Value = string(decrypted)

		cookies = append(cookies, cookie)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating cookie rows: %w", err)
	}

	return cookies, nil
}

// DeleteCookie deletes a cookie
func (d *DB) DeleteCookie(domain, path, name string) error {
	_, err := d.db.Exec(
		"DELETE FROM cookies WHERE domain = ? AND path = ? AND name = ?",
		domain, path, name,
	)
	if err != nil {
		return fmt.Errorf("failed to delete cookie: %w", err)
	}
	return nil
}

// ClearCookies deletes all cookies
func (d *DB) ClearCookies() error {
	_, err := d.db.Exec("DELETE FROM cookies")
	if err != nil {
		return fmt.Errorf("failed to clear cookies: %w", err)
	}
	return nil
}

// ExportCookiesToNetscape exports cookies to a Netscape format file
func (d *DB) ExportCookiesToNetscape(filePath string) error {
	cookies, err := d.GetAllCookies()
	if err != nil {
		return fmt.Errorf("failed to get cookies: %w", err)
	}

	if d.logger != nil {
		d.logger.Info("Exporting %d cookies to %s", len(cookies), filePath)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create cookie file: %w", err)
	}
	defer file.Close()

	// Write header
	_, err = file.WriteString("# Netscape HTTP Cookie File\n")
	if err != nil {
		return fmt.Errorf("failed to write cookie file header: %w", err)
	}

	// Write cookies
	youtubeCookieCount := 0
	for _, cookie := range cookies {
		secure := "FALSE"
		if cookie.Secure {
			secure = "TRUE"
		}

		expires := "0"
		if cookie.Expires.Valid {
			expires = fmt.Sprintf("%d", cookie.Expires.Time.Unix())
		}

		line := fmt.Sprintf("%s\t%s\t%s\t%s\t%s\t%s\t%s\n",
			cookie.Domain, "TRUE", cookie.Path, secure, expires, cookie.Name, cookie.Value)

		_, err = file.WriteString(line)
		if err != nil {
			return fmt.Errorf("failed to write cookie to file: %w", err)
		}

		// Count YouTube cookies
		if strings.Contains(cookie.Domain, "youtube.com") {
			youtubeCookieCount++
			if d.logger != nil {
				d.logger.Debug("Exported YouTube cookie: %s (domain=%s, path=%s)",
					cookie.Name, cookie.Domain, cookie.Path)
			}
		}
	}

	if d.logger != nil {
		d.logger.Info("Exported %d YouTube cookies out of %d total cookies", youtubeCookieCount, len(cookies))
	}
	return nil
}

package database

import (
	"fmt"
)

// ChangePassword changes the database encryption password
func (d *DB) ChangePassword(currentPassword, newPassword string) error {
	if d.logger != nil {
		d.logger.Info("Changing database encryption password")
	}

	// Verify current password
	if d.password != currentPassword {
		if d.logger != nil {
			d.logger.Error("Incorrect current password provided for password change")
		}
		return fmt.Errorf("incorrect current password")
	}

	// Get all cookies
	if d.logger != nil {
		d.logger.Info("Retrieving all cookies for password change")
	}
	cookies, err := d.GetAllCookies()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get cookies for password change: %v", err)
		}
		return fmt.Errorf("failed to get cookies: %w", err)
	}

	// Get all config entries
	if d.logger != nil {
		d.logger.Info("Retrieving all config entries for password change")
	}
	configs, err := d.getAllConfig()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get config entries for password change: %v", err)
		}
		return fmt.Errorf("failed to get config entries: %w", err)
	}

	// Create new encryptor with new password
	if d.logger != nil {
		d.logger.Info("Creating new encryptor with new password")
	}
	var salt []byte
	err = d.db.QueryRow("SELECT value FROM metadata WHERE key = 'salt'").Scan(&salt)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get salt for password change: %v", err)
		}
		return fmt.Errorf("failed to get salt: %w", err)
	}

	newEncryptor, err := NewEncryptor(newPassword, salt)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to create new encryptor for password change: %v", err)
		}
		return fmt.Errorf("failed to create new encryptor: %w", err)
	}

	// Begin transaction
	if d.logger != nil {
		d.logger.Info("Beginning transaction for password change")
	}
	tx, err := d.db.Begin()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to begin transaction for password change: %v", err)
		}
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Clear cookies table
	_, err = tx.Exec("DELETE FROM cookies")
	if err != nil {
		return fmt.Errorf("failed to clear cookies table: %w", err)
	}

	// Re-encrypt and insert cookies
	for _, cookie := range cookies {
		// Re-encrypt the cookie value with the new encryptor
		encryptedValue, err := newEncryptor.Encrypt([]byte(cookie.Value))
		if err != nil {
			return fmt.Errorf("failed to encrypt cookie value: %w", err)
		}

		// Insert cookie
		_, err = tx.Exec(
			`INSERT INTO cookies
			(domain, path, name, value, secure, http_only, expires)
			VALUES (?, ?, ?, ?, ?, ?, ?)`,
			cookie.Domain, cookie.Path, cookie.Name, encryptedValue,
			cookie.Secure, cookie.HttpOnly, cookie.Expires,
		)
		if err != nil {
			return fmt.Errorf("failed to insert cookie: %w", err)
		}
	}

	// Clear config table
	_, err = tx.Exec("DELETE FROM config")
	if err != nil {
		return fmt.Errorf("failed to clear config table: %w", err)
	}

	// Re-encrypt and insert config entries
	for _, config := range configs {
		var storedValue string

		if config.IsEncrypted {
			// Re-encrypt the config value with the new encryptor
			storedValue, err = newEncryptor.Encrypt([]byte(config.Value))
			if err != nil {
				return fmt.Errorf("failed to encrypt config value: %w", err)
			}
		} else {
			storedValue = config.Value
		}

		// Insert config entry
		_, err = tx.Exec(
			"INSERT INTO config (key, value, is_encrypted) VALUES (?, ?, ?)",
			config.Key, storedValue, config.IsEncrypted,
		)
		if err != nil {
			return fmt.Errorf("failed to insert config entry: %w", err)
		}
	}

	// Commit transaction
	if d.logger != nil {
		d.logger.Info("Committing transaction for password change")
	}
	if err := tx.Commit(); err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to commit transaction for password change: %v", err)
		}
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Update encryptor and password
	d.encryptor = newEncryptor
	d.password = newPassword

	if d.logger != nil {
		d.logger.Info("Database password successfully changed")
	}

	return nil
}

// EnableEncryption enables encryption for the database
func (d *DB) EnableEncryption(password string) error {
	if d.logger != nil {
		d.logger.Info("Enabling database encryption")
	}

	if d.isEncrypted {
		if d.logger != nil {
			d.logger.Warn("Encryption is already enabled")
		}
		return fmt.Errorf("encryption is already enabled")
	}

	// Create new encryptor with the password
	var saltStr string
	err := d.db.QueryRow("SELECT value FROM metadata WHERE key = 'salt'").Scan(&saltStr)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get salt for encryption: %v", err)
		}
		return fmt.Errorf("failed to get salt: %w", err)
	}

	salt := []byte(saltStr)
	newEncryptor, err := NewEncryptor(password, salt)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to create encryptor: %v", err)
		}
		return fmt.Errorf("failed to create encryptor: %w", err)
	}

	// Get all cookies
	if d.logger != nil {
		d.logger.Info("Retrieving all cookies for encryption")
	}
	cookies, err := d.GetAllCookies()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get cookies for encryption: %v", err)
		}
		return fmt.Errorf("failed to get cookies: %w", err)
	}

	// Get all config entries
	if d.logger != nil {
		d.logger.Info("Retrieving all config entries for encryption")
	}
	configs, err := d.getAllConfig()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get config entries for encryption: %v", err)
		}
		return fmt.Errorf("failed to get config entries: %w", err)
	}

	// Begin transaction
	if d.logger != nil {
		d.logger.Info("Beginning transaction for encryption")
	}
	tx, err := d.db.Begin()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to begin transaction for encryption: %v", err)
		}
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Update encryption status in metadata
	if d.logger != nil {
		d.logger.Info("Updating encryption status in metadata")
	}
	_, err = tx.Exec("INSERT OR REPLACE INTO metadata (key, value) VALUES (?, ?)", "encryption_enabled", "true")
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to update encryption status: %v", err)
		}
		return fmt.Errorf("failed to update encryption status: %w", err)
	}

	// Clear cookies table
	_, err = tx.Exec("DELETE FROM cookies")
	if err != nil {
		return fmt.Errorf("failed to clear cookies table: %w", err)
	}

	// Re-encrypt and insert cookies
	for _, cookie := range cookies {
		// Encrypt the cookie value with the new encryptor
		encryptedValue, err := newEncryptor.Encrypt([]byte(cookie.Value))
		if err != nil {
			return fmt.Errorf("failed to encrypt cookie value: %w", err)
		}

		// Insert cookie
		_, err = tx.Exec(
			`INSERT INTO cookies
			(domain, path, name, value, secure, http_only, expires)
			VALUES (?, ?, ?, ?, ?, ?, ?)`,
			cookie.Domain, cookie.Path, cookie.Name, encryptedValue,
			cookie.Secure, cookie.HttpOnly, cookie.Expires,
		)
		if err != nil {
			return fmt.Errorf("failed to insert cookie: %w", err)
		}
	}

	// Clear config table
	_, err = tx.Exec("DELETE FROM config")
	if err != nil {
		return fmt.Errorf("failed to clear config table: %w", err)
	}

	// Re-encrypt and insert config entries
	for _, config := range configs {
		var storedValue string

		if config.IsEncrypted {
			// Encrypt the config value with the new encryptor
			storedValue, err = newEncryptor.Encrypt([]byte(config.Value))
			if err != nil {
				return fmt.Errorf("failed to encrypt config value: %w", err)
			}
		} else {
			storedValue = config.Value
		}

		// Insert config entry
		_, err = tx.Exec(
			"INSERT INTO config (key, value, is_encrypted) VALUES (?, ?, ?)",
			config.Key, storedValue, config.IsEncrypted,
		)
		if err != nil {
			return fmt.Errorf("failed to insert config entry: %w", err)
		}
	}

	// Commit transaction
	if d.logger != nil {
		d.logger.Info("Committing transaction for encryption")
	}
	if err := tx.Commit(); err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to commit transaction for encryption: %v", err)
		}
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Update encryptor, password, and encryption status
	d.encryptor = newEncryptor
	d.password = password
	d.isEncrypted = true

	if d.logger != nil {
		d.logger.Info("Database encryption successfully enabled")
	}

	return nil
}

// DisableEncryption disables encryption for the database
func (d *DB) DisableEncryption(password string) error {
	if d.logger != nil {
		d.logger.Info("Disabling database encryption")
	}

	if !d.isEncrypted {
		if d.logger != nil {
			d.logger.Warn("Encryption is already disabled")
		}
		return fmt.Errorf("encryption is already disabled")
	}

	// Verify password
	if d.password != password {
		if d.logger != nil {
			d.logger.Error("Incorrect password provided for disabling encryption")
		}
		return fmt.Errorf("incorrect password")
	}

	// Get all cookies
	if d.logger != nil {
		d.logger.Info("Retrieving all cookies for decryption")
	}
	cookies, err := d.GetAllCookies()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get cookies for decryption: %v", err)
		}
		return fmt.Errorf("failed to get cookies: %w", err)
	}

	// Get all config entries
	if d.logger != nil {
		d.logger.Info("Retrieving all config entries for decryption")
	}
	configs, err := d.getAllConfig()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to get config entries for decryption: %v", err)
		}
		return fmt.Errorf("failed to get config entries: %w", err)
	}

	// Begin transaction
	if d.logger != nil {
		d.logger.Info("Beginning transaction for decryption")
	}
	tx, err := d.db.Begin()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to begin transaction for decryption: %v", err)
		}
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Update encryption status in metadata
	if d.logger != nil {
		d.logger.Info("Updating encryption status to disabled in metadata")
	}
	_, err = tx.Exec("INSERT OR REPLACE INTO metadata (key, value) VALUES (?, ?)", "encryption_enabled", "false")
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to update encryption status: %v", err)
		}
		return fmt.Errorf("failed to update encryption status: %w", err)
	}

	// Clear cookies table
	_, err = tx.Exec("DELETE FROM cookies")
	if err != nil {
		return fmt.Errorf("failed to clear cookies table: %w", err)
	}

	// Insert cookies without encryption
	for _, cookie := range cookies {
		// Insert cookie with plaintext value
		_, err = tx.Exec(
			`INSERT INTO cookies
			(domain, path, name, value, secure, http_only, expires)
			VALUES (?, ?, ?, ?, ?, ?, ?)`,
			cookie.Domain, cookie.Path, cookie.Name, cookie.Value,
			cookie.Secure, cookie.HttpOnly, cookie.Expires,
		)
		if err != nil {
			return fmt.Errorf("failed to insert cookie: %w", err)
		}
	}

	// Clear config table
	_, err = tx.Exec("DELETE FROM config")
	if err != nil {
		return fmt.Errorf("failed to clear config table: %w", err)
	}

	// Insert config entries without encryption
	for _, config := range configs {
		// Insert config entry with plaintext value
		_, err = tx.Exec(
			"INSERT INTO config (key, value, is_encrypted) VALUES (?, ?, ?)",
			config.Key, config.Value, false,
		)
		if err != nil {
			return fmt.Errorf("failed to insert config entry: %w", err)
		}
	}

	// Commit transaction
	if d.logger != nil {
		d.logger.Info("Committing transaction for decryption")
	}
	if err := tx.Commit(); err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to commit transaction for decryption: %v", err)
		}
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Update encryption status
	d.isEncrypted = false
	d.password = ""

	if d.logger != nil {
		d.logger.Info("Database encryption successfully disabled")
	}

	return nil
}
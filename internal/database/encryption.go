package database

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"

	"golang.org/x/crypto/pbkdf2"
)

const (
	// Encryption parameters
	keySize    = 32 // 256 bits
	saltSize   = 16
	nonceSize  = 12
	iterations = 10000
)

// Encryptor handles encryption and decryption of sensitive data
type Encryptor struct {
	key []byte
}

// NewEncryptor creates a new encryptor with the given password
func NewEncryptor(password string, salt []byte) (*Encryptor, error) {
	if len(salt) == 0 {
		// Generate a random salt if none is provided
		salt = make([]byte, saltSize)
		if _, err := io.ReadFull(rand.Reader, salt); err != nil {
			return nil, fmt.Errorf("failed to generate salt: %w", err)
		}
	}

	// Derive key from password using PBKDF2
	key := pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)

	return &Encryptor{
		key: key,
	}, nil
}

// GenerateSalt generates a random salt for key derivation
func GenerateSalt() ([]byte, error) {
	salt := make([]byte, saltSize)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return nil, fmt.Errorf("failed to generate salt: %w", err)
	}
	return salt, nil
}

// Encrypt encrypts the given data
func (e *Encryptor) Encrypt(data []byte) (string, error) {
	// Create a new AES cipher block
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher block: %w", err)
	}

	// Generate a random nonce
	nonce := make([]byte, nonceSize)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Create the AES-GCM cipher
	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	// Encrypt the data
	ciphertext := aesgcm.Seal(nil, nonce, data, nil)

	// Combine nonce and ciphertext for storage
	result := make([]byte, nonceSize+len(ciphertext))
	copy(result[:nonceSize], nonce)
	copy(result[nonceSize:], ciphertext)

	// Encode as base64 for storage
	return base64.StdEncoding.EncodeToString(result), nil
}

// Decrypt decrypts the given data
func (e *Encryptor) Decrypt(encryptedData string) ([]byte, error) {
	// Decode from base64
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %w", err)
	}

	// Check if data is long enough to contain nonce
	if len(data) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// Extract nonce and ciphertext
	nonce := data[:nonceSize]
	ciphertext := data[nonceSize:]

	// Create a new AES cipher block
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher block: %w", err)
	}

	// Create the AES-GCM cipher
	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Decrypt the data
	plaintext, err := aesgcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}

	return plaintext, nil
}

package database

import (
	"database/sql" // Import sql package
	"fmt"
)

// AddHistory adds a download history entry using the main DB connection
func (d *DB) AddHistory(entry HistoryEntry) (int64, error) {
	if d.logger != nil {
		d.logger.Info("Adding history entry for: %s", entry.Title)
	}
	result, err := d.db.Exec(
		`INSERT INTO history
		(url, title, format, quality, path, timestamp, description, thumbnail, duration, author)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		entry.URL, entry.Title, entry.Format, entry.Quality, entry.Path, entry.Timestamp,
		entry.Description, entry.Thumbnail, entry.Duration, entry.Author,
	)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to add history entry: %v", err)
		}
		return 0, fmt.Errorf("failed to add history entry: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		// Getting LastInsertId might not be supported by all drivers/scenarios,
		// but it's generally okay for SQLite auto-increment. Log error if it occurs.
		if d.logger != nil {
			d.logger.Warn("Failed to get last insert ID for history: %v", err)
		}
		// Don't return error here, just the ID might be 0
		return 0, nil
	}

	if d.logger != nil {
		d.logger.Info("Successfully added history entry with ID: %d", id)
	}

	return id, nil
}

// AddHistoryTx adds a download history entry within a transaction
func (d *DB) AddHistoryTx(tx *sql.Tx, entry HistoryEntry) (int64, error) {
	// Log statement removed from Tx version for potentially higher frequency calls
	result, err := tx.Exec(
		`INSERT INTO history
		(url, title, format, quality, path, timestamp, description, thumbnail, duration, author)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		entry.URL, entry.Title, entry.Format, entry.Quality, entry.Path, entry.Timestamp,
		entry.Description, entry.Thumbnail, entry.Duration, entry.Author,
	)
	if err != nil {
		// Log error here if needed, but usually transaction handles rollback
		return 0, fmt.Errorf("failed to add history entry in transaction: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		// Log warning
		if d.logger != nil {
			d.logger.Warn("Failed to get last insert ID for history in transaction: %v", err)
		}
		return 0, nil
	}

	return id, nil
}

// GetHistory gets all download history entries
func (d *DB) GetHistory() ([]HistoryEntry, error) {
	rows, err := d.db.Query(
		`SELECT id, url, title, format, quality, path, timestamp,
		description, thumbnail, duration, author FROM history ORDER BY timestamp DESC`,
	)
	if err != nil {
		// Check specifically for "database is closed"
		if err.Error() == "sql: database is closed" {
			if d.logger != nil {
				d.logger.Warn("Attempted to query history while database is closed.")
			}
			return nil, err // Return the specific error
		}
		return nil, fmt.Errorf("failed to get history: %w", err)
	}
	defer rows.Close()

	var entries []HistoryEntry
	for rows.Next() {
		var entry HistoryEntry
		err := rows.Scan(
			&entry.ID, &entry.URL, &entry.Title, &entry.Format, &entry.Quality, &entry.Path,
			&entry.Timestamp, &entry.Description, &entry.Thumbnail, &entry.Duration, &entry.Author,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan history entry: %w", err)
		}
		entries = append(entries, entry)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating history rows: %w", err)
	}

	return entries, nil
}

// ClearHistory deletes all download history entries
func (d *DB) ClearHistory() error {
	_, err := d.db.Exec("DELETE FROM history")
	if err != nil {
		return fmt.Errorf("failed to clear history: %w", err)
	}
	if d.logger != nil {
		d.logger.Info("Cleared download history from database.")
	}
	return nil
}

package database

import (
	"bufio"
	"database/sql"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
	"time"
)

// ParseNetscapeCookieFile parses a Netscape cookie file and returns the cookies
func ParseNetscapeCookieFile(reader io.Reader) ([]NetscapeCookie, error) {
	scanner := bufio.NewScanner(reader)
	lineNum := 0
	var cookies []NetscapeCookie
	skippedLines := 0
	parsedLines := 0

	for scanner.Scan() {
		lineNum++
		line := scanner.Text()

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			skippedLines++
			continue
		}

		// Parse the cookie line
		fields := strings.Split(line, "\t")
		if len(fields) < 7 {
			fmt.Printf("Debug: Line %d: invalid cookie format, expected at least 7 fields, got %d: %s\n",
				lineNum, len(fields), line)
			return nil, fmt.Errorf("line %d: invalid cookie format, expected at least 7 fields", lineNum)
		}

		// Parse the expiration time
		expiresStr := fields[4]
		var expires time.Time
		if expiresStr != "0" {
			expiresInt, err := strconv.ParseInt(expiresStr, 10, 64)
			if err != nil {
				fmt.Printf("Debug: Line %d: invalid expiration time: %v\n", lineNum, err)
				return nil, fmt.Errorf("line %d: invalid expiration time: %w", lineNum, err)
			}
			expires = time.Unix(expiresInt, 0)
		}

		// Create the cookie
		cookie := NetscapeCookie{
			Domain:   fields[0],
			Path:     fields[2],
			Secure:   fields[3] == "TRUE",
			Expires:  expires,
			Name:     fields[5],
			Value:    fields[6],
			HttpOnly: false, // Not specified in Netscape format
		}

		// Debug output for YouTube cookies
		if strings.Contains(cookie.Domain, "youtube.com") {
			fmt.Printf("Debug: Parsed YouTube cookie: %s (domain=%s, path=%s)\n",
				cookie.Name, cookie.Domain, cookie.Path)
		}

		cookies = append(cookies, cookie)
		parsedLines++
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("Debug: Error reading cookie file: %v\n", err)
		return nil, fmt.Errorf("error reading cookie file: %w", err)
	}

	fmt.Printf("Debug: Parsed %d cookies from %d lines (skipped %d lines)\n",
		len(cookies), lineNum, skippedLines)
	return cookies, nil
}

// ImportCookiesFromNetscape imports cookies from a Netscape format file
func (d *DB) ImportCookiesFromNetscape(filePath string) error {
	if d.logger != nil {
		d.logger.Info("Importing cookies from Netscape format file: %s", filePath)
	}
	file, err := os.Open(filePath)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to open cookie file: %v", err)
		}
		return fmt.Errorf("failed to open cookie file: %w", err)
	}
	defer file.Close()

	fmt.Printf("Debug: Parsing Netscape cookie file: %s\n", filePath)
	cookies, err := ParseNetscapeCookieFile(file)
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to parse cookie file: %v", err)
		}
		return fmt.Errorf("failed to parse cookie file: %w", err)
	}

	if d.logger != nil {
		d.logger.Info("Successfully parsed %d cookies from file", len(cookies))
	}

	fmt.Printf("Debug: Successfully parsed %d cookies from file\n", len(cookies))

	// Count YouTube cookies
	youtubeCookies := 0
	for _, cookie := range cookies {
		if strings.Contains(cookie.Domain, "youtube.com") {
			youtubeCookies++
			fmt.Printf("Debug: Found YouTube cookie in parser: %s (domain=%s, path=%s)\n",
				cookie.Name, cookie.Domain, cookie.Path)
		}
	}
	fmt.Printf("Debug: Found %d YouTube cookies out of %d total cookies in parser\n", youtubeCookies, len(cookies))

	// Begin transaction
	if d.logger != nil {
		d.logger.Info("Beginning transaction for cookie import")
	}
	tx, err := d.db.Begin()
	if err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to begin transaction for cookie import: %v", err)
		}
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	for _, cookie := range cookies {
		// Encrypt the cookie value
		encryptedValue, err := d.encryptor.Encrypt([]byte(cookie.Value))
		if err != nil {
			if d.logger != nil {
				d.logger.Error("Failed to encrypt cookie value for import: %v", err)
			}
			return fmt.Errorf("failed to encrypt cookie value: %w", err)
		}

		// Insert cookie
		var expires sql.NullTime
		if !cookie.Expires.IsZero() {
			expires = sql.NullTime{Time: cookie.Expires, Valid: true}
		}

		_, err = tx.Exec(
			`INSERT OR REPLACE INTO cookies
			(domain, path, name, value, secure, http_only, expires)
			VALUES (?, ?, ?, ?, ?, ?, ?)`,
			cookie.Domain, cookie.Path, cookie.Name, encryptedValue,
			cookie.Secure, cookie.HttpOnly, expires,
		)
		if err != nil {
			if d.logger != nil {
				d.logger.Error("Failed to insert cookie during import: %v", err)
			}
			return fmt.Errorf("failed to insert cookie: %w", err)
		}
	}

	// Commit transaction
	if d.logger != nil {
		d.logger.Info("Committing transaction for cookie import")
	}
	if err := tx.Commit(); err != nil {
		if d.logger != nil {
			d.logger.Error("Failed to commit transaction for cookie import: %v", err)
		}
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	fmt.Printf("Debug: Successfully imported %d cookies to database\n", len(cookies))
	if d.logger != nil {
		d.logger.Info("Successfully imported %d cookies to database", len(cookies))
	}
	return nil
}

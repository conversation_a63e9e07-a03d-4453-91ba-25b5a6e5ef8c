#!/bin/bash

# Exit on any error
set -e

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'      # No Color

# Print step with color
print_step() {
    echo -e "\n${GREEN}>>> $1${NC}"
}

# Print error with color
print_error() {
    echo -e "${RED}Error: $1${NC}"
}

# Cleanup function
cleanup() {
    print_step "Cleaning up build artifacts..."
    rm -rf fyne-cross
    rm -rf bin/*
    rm -rf ffmpeg_build_linux
    rm -rf internal/resources/bin/*
    rm -rf logs/*
    echo "Cleanup complete."
}

cleanup

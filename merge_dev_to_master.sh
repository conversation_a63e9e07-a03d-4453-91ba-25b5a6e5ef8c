#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
# Treat unset variables as an error when substituting.
# Prevent errors in a pipeline from being masked.
set -euo pipefail

DEV_BRANCH="dev"
MASTER_BRANCH="master"
REMOTE="origin"

echo ">>> This script will merge '$DEV_BRANCH' into '$MASTER_BRANCH' and push '$MASTER_BRANCH'."
echo ">>> Make sure both branches are stable and ready for merge."
read -p ">>> Continue? (y/N): " confirm && [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]] || exit 1

echo ">>> Checking Git status..."

# Check if the working directory is clean
if [[ -n $(git status --porcelain) ]]; then
  echo "--------------------------------------------------" >&2
  echo "ERROR: Working directory is not clean." >&2
  echo "Please commit, stash, or clean your working directory before merging." >&2
  echo "--------------------------------------------------" >&2
  git status --short >&2
  echo "--------------------------------------------------" >&2
  exit 1
else
  echo "Working directory is clean."
fi

# Remember the branch the user was on
ORIGINAL_BRANCH=$(git rev-parse --abbrev-ref HEAD)
echo ">>> Current branch is '$ORIGINAL_BRANCH'."

# --- Update Master Branch ---
echo ">>> Switching to '$MASTER_BRANCH' branch..."
git checkout "$MASTER_BRANCH"

echo ">>> Pulling latest changes for '$MASTER_BRANCH' from $REMOTE (using merge strategy)..."
# Explicitly use --no-rebase to merge the remote changes
git pull --no-rebase "$REMOTE" "$MASTER_BRANCH"

# --- Update Dev Branch ---
# (We need dev updated locally to ensure the merge includes the latest dev changes)
echo ">>> Switching to '$DEV_BRANCH' branch..."
git checkout "$DEV_BRANCH"

echo ">>> Pulling latest changes for '$DEV_BRANCH' from $REMOTE (using merge strategy)..."
# Explicitly use --no-rebase to merge the remote changes
git pull --no-rebase "$REMOTE" "$DEV_BRANCH"

# --- Perform Merge ---
echo ">>> Switching back to '$MASTER_BRANCH' branch..."
git checkout "$MASTER_BRANCH"

echo ">>> Merging '$DEV_BRANCH' into '$MASTER_BRANCH' (using --no-ff)..."
# Using --no-ff creates a merge commit even if it could be fast-forwarded,
# which helps keep track of feature merges.
# If a merge conflict occurs, git will stop here, and the script will exit
# due to 'set -e'. The user must resolve conflicts manually.
git merge --no-ff "$DEV_BRANCH" -m "Merge branch '$DEV_BRANCH' into $MASTER_BRANCH" # Added default merge message

# --- Push Master ---
echo ">>> Pushing '$MASTER_BRANCH' branch to $REMOTE..."
git push "$REMOTE" "$MASTER_BRANCH"

# --- Post-Merge ---
echo ">>> Merge complete. '$MASTER_BRANCH' pushed to $REMOTE."

# Optionally, switch back to the original branch
if [[ "$ORIGINAL_BRANCH" != "$MASTER_BRANCH" ]]; then
  echo ">>> Switching back to original branch '$ORIGINAL_BRANCH'..."
  git checkout "$ORIGINAL_BRANCH"
else
  echo ">>> Remaining on '$MASTER_BRANCH' branch."
fi

echo "--------------------------------------------------"
echo ">>> Successfully merged '$DEV_BRANCH' into '$MASTER_BRANCH' and pushed '$MASTER_BRANCH'."
echo "--------------------------------------------------"

exit 0
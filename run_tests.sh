#!/bin/bash

# Exit on any error
set -e

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'      # No Color

# Print step with color
print_step() {
    echo -e "\n${G<PERSON><PERSON>}>>> $1${NC}"
}

# Print error with color
print_error() {
    echo -e "${RED}Error: $1${NC}"
}

# Run tests for a specific package
run_package_tests() {
    local package=$1
    print_step "Running tests for package: $package"
    go test -v ./$package/... || { print_error "Tests failed for $package"; exit 1; }
}

# Main function
main() {
    print_step "Running all tests"
    
    # Run tests for each package
    run_package_tests "internal/database"
    run_package_tests "internal/downloader"
    run_package_tests "internal/config"
    run_package_tests "internal/history"
    run_package_tests "internal/logger"
    run_package_tests "internal/utils"
    run_package_tests "internal/i18n"
    run_package_tests "internal/resources"
    run_package_tests "internal/gui"
    run_package_tests "gotube-downloader"
    
    print_step "All tests passed!"
}

# Run the main function
main

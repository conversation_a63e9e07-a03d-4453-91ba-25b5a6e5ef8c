#!/bin/bash

# Exit on any error
set -e

# --- Configuration Defaults ---
TARGET="both"       # Options: linux, windows, both
EMBED_BINARIES="true" # Options: true, false
COMPRESSION="best"    # Options: best, fast, none
FFMPEG_VERSION="normal" # Options: normal (latest Gyan essentials), minimal (specific older Gyan build)
BUILD_TAGS=""         # Extra build tags

# --- Helper Functions ---
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_step() { echo -e "\n${GREEN}>>> $1${NC}"; }
print_warn() { echo -e "${YELLOW}Warning: $1${NC}"; }
print_error() { echo -e "${RED}Error: $1${NC}"; exit 1; }

usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --target=<target>    Specify build target: linux, windows, or both (default: both)"
    echo "  --no-embed           Build without embedding yt-dlp, ffmpeg, or python modules"
    echo "  --no-compress        Disable UPX compression"
    echo "  --fast-compress      Use faster UPX compression (--fast)"
    echo "  --minimal-ffmpeg     Use specific older, potentially smaller, FFmpeg build for Windows embedding" # <-- Updated help text
    echo "  --help               Display this help message"
    echo ""
    echo "Examples:"
    echo "  ./build.sh                           # Build both targets, embed latest Gyan essentials ffmpeg"
    echo "  ./build.sh --target=linux            # Build only for Linux"
    echo "  ./build.sh --minimal-ffmpeg          # Build both, embed specific older Gyan ffmpeg for Windows"
    echo "  ./build.sh --no-embed --no-compress  # Build both without embedding or compression"
    exit 0
}

# --- Argument Parsing ---
while [[ $# -gt 0 ]]; do
    case "$1" in
        --target=*)
            TARGET="${1#*=}"
            shift
            ;;
        --no-embed)
            EMBED_BINARIES="false"
            shift
            ;;
        --no-compress)
            COMPRESSION="none"
            shift
            ;;
        --fast-compress)
            COMPRESSION="fast"
            shift
            ;;
        --minimal-ffmpeg)
            FFMPEG_VERSION="minimal" # Set the version flag
            shift
            ;;
        --help)
            usage
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            ;;
    esac
done

# --- Validate Options ---
case "$TARGET" in
    linux|windows|both) ;;
    *) print_error "Invalid target: '$TARGET'. Must be 'linux', 'windows', or 'both'.";;
esac
case "$COMPRESSION" in
    best|fast|none) ;;
    *) print_error "Invalid compression option: '$COMPRESSION'. Must be 'best', 'fast', or 'none'.";;
esac
case "$FFMPEG_VERSION" in
    normal|minimal) ;;
    *) print_error "Invalid ffmpeg version option: '$FFMPEG_VERSION'. Must be 'normal' or 'minimal'.";;
esac


# --- Set Build Tags based on options ---
if [ "$EMBED_BINARIES" = "false" ]; then
    BUILD_TAGS="noembed"
    print_step "Building with 'noembed' tag. Dependencies (yt-dlp, ffmpeg) must be in PATH."
fi

print_step "Build Configuration:"
print_step "  Target:         $TARGET"
print_step "  Embed Binaries: $EMBED_BINARIES"
print_step "  Compression:    $COMPRESSION"
print_step "  FFmpeg Version: $FFMPEG_VERSION (Windows embedding only)"
if [ -n "$BUILD_TAGS" ]; then
    print_step "  Build Tags:     $BUILD_TAGS"
fi

# --- Dependency Checks ---
check_requirements() {
    print_step "Checking requirements..."
    command -v go >/dev/null 2>&1 || print_error "Go is not installed"
    if [ "$EMBED_BINARIES" = "true" ]; then
        command -v wget >/dev/null 2>&1 || print_error "wget is not installed (needed for downloading embedded binaries)"
        command -v unzip >/dev/null 2>&1 || print_error "unzip is not installed (needed for downloading embedded binaries)"
        command -v tar >/dev/null 2>&1 || print_error "tar is not installed (needed for downloading embedded binaries)"
        command -v python3 >/dev/null 2>&1 || print_error "python3 is not installed (needed for preparing embedded modules)"
        python3 -m pip --version >/dev/null 2>&1 || print_error "pip for python3 not found (needed for preparing embedded modules)"
        python3 -m venv --help >/dev/null 2>&1 || print_error "python3 venv module not found (needed for preparing embedded modules)"
    fi
    command -v fyne-cross >/dev/null 2>&1 || print_error "fyne-cross not installed. (go install github.com/fyne-io/fyne-cross@latest)"
    if [ "$COMPRESSION" != "none" ]; then
        command -v upx >/dev/null 2>&1 || print_error "upx is not installed (needed for compression)"
    fi
    if [[ "$TARGET" == "linux" || "$TARGET" == "both" ]]; then
       command -v install >/dev/null 2>&1 || print_error "'install' command not found. (Usually part of coreutils)"
    fi
}

# --- Download Functions (Conditional) ---
download_ytdlp() {
    if [ "$EMBED_BINARIES" = "false" ]; then return; fi
    print_step "Downloading yt-dlp binaries (for embedding)..."
    mkdir -p internal/resources/bin
    find internal/resources/bin -name "yt-dlp*" -mtime +1 -delete 2>/dev/null || true
    [ ! -f internal/resources/bin/yt-dlp ] && wget -q --show-progress -O internal/resources/bin/yt-dlp "https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp"
    chmod +x internal/resources/bin/yt-dlp || true
    [ ! -f internal/resources/bin/yt-dlp.exe ] && wget -q --show-progress -O internal/resources/bin/yt-dlp.exe "https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe"
}

download_ffmpeg_win() {
    if [ "$EMBED_BINARIES" = "false" ]; then return; fi
    if [[ "$TARGET" != "windows" && "$TARGET" != "both" ]]; then return; fi

    print_step "Downloading Windows ffmpeg (for Windows embedding)..."
    mkdir -p internal/resources/bin; local temp_dl_dir="bin"; mkdir -p "$temp_dl_dir"

    # Determine which FFmpeg build to download based on FFMPEG_VERSION
    local ffmpeg_url=""
    local ffmpeg_zip_name=""
    local expected_version_type=""

    if [ "$FFMPEG_VERSION" = "minimal" ]; then
        print_step "  Selected: Minimal FFmpeg (Specific older Gyan essentials build)"
        expected_version_type="minimal (older gyan)"
        ffmpeg_zip_name="ffmpeg-win-small-2023-06-21.zip"
        ffmpeg_url="https://github.com/GyanD/codexffmpeg/releases/download/2023-06-21-git-1bcb8a7338/ffmpeg-2023-06-21-git-1bcb8a7338-essentials_build.zip"
    else
        print_step "  Selected: Normal FFmpeg (Latest Gyan essentials build)"
        expected_version_type="normal (latest gyan)"
        ffmpeg_url="https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
        ffmpeg_zip_name="ffmpeg-gyan-latest-essentials.zip"
    fi

    # --- Force removal of old binary to ensure the correct one is downloaded ---
    print_step "  Ensuring correct FFmpeg version by removing existing..."
    rm -f internal/resources/bin/ffmpeg.exe

    # --- Now, attempt download ONLY IF the file is actually missing ---
    if [ ! -f internal/resources/bin/ffmpeg.exe ]; then
        print_step "  Downloading from $ffmpeg_url..."
        wget -q --show-progress -O "$temp_dl_dir/$ffmpeg_zip_name" "$ffmpeg_url"

        # Add size check for debugging
        echo "  DEBUG: Downloaded $ffmpeg_zip_name size:"
        ls -lh "$temp_dl_dir/$ffmpeg_zip_name" || print_warn "Could not get size of downloaded zip."

        mkdir -p ffmpeg-temp-win; unzip -oq "$temp_dl_dir/$ffmpeg_zip_name" -d ffmpeg-temp-win

        # Find ffmpeg.exe, might be in a subdirectory like bin/
        FFMPEG_EXE_PATH=$(find ffmpeg-temp-win -name ffmpeg.exe | head -n 1)
        if [ -z "$FFMPEG_EXE_PATH" ]; then
             print_error "Could not find ffmpeg.exe in downloaded $expected_version_type archive.";
             rm -rf ffmpeg-temp-win "$temp_dl_dir/$ffmpeg_zip_name"; exit 1;
        fi
        print_step "  Found ffmpeg.exe at $FFMPEG_EXE_PATH"

        # Add size check for debugging
        echo "  DEBUG: Extracted ffmpeg.exe size:"
        ls -lh "$FFMPEG_EXE_PATH" || print_warn "Could not get size of extracted ffmpeg.exe."

        cp "$FFMPEG_EXE_PATH" internal/resources/bin/ffmpeg.exe
        rm -rf ffmpeg-temp-win "$temp_dl_dir/$ffmpeg_zip_name"
        print_step "  Windows ffmpeg ($expected_version_type) placed in internal/resources/bin/ffmpeg.exe"

        # Add size check for debugging
        echo "  DEBUG: Final embedded ffmpeg.exe size:"
        ls -lh internal/resources/bin/ffmpeg.exe || print_warn "Could not get size of final ffmpeg.exe."

    else
        # This case should not be reached due to the rm -f above, but kept for safety
        print_step "  Using existing Windows ffmpeg (already present after check)."
        # Add debug size check even when using existing
        echo "  DEBUG: Existing embedded ffmpeg.exe size:"
        ls -lh internal/resources/bin/ffmpeg.exe || print_warn "Could not get size of existing ffmpeg.exe."
    fi
    rm -rf "$temp_dl_dir" # Clean up temp download dir regardless
}


download_secretstorage() {
    if [ "$EMBED_BINARIES" = "false" ]; then return; fi
    if [[ "$TARGET" != "windows" && "$TARGET" != "both" ]]; then return; fi

    print_step "Downloading/Preparing secretstorage Python module (for Windows embedding)..."
    RES_MODULES="internal/resources/bin/python_modules"
    if [ -d "$RES_MODULES" ] && [ -n "$(ls -A $RES_MODULES)" ]; then
         print_step "  Using existing Python modules."
         return
    fi

    MODULE_TEMP_DIR=$(mktemp -d -t gotube-modules-XXXXXX)
    trap 'echo "Cleaning up temporary module directory $MODULE_TEMP_DIR..."; rm -rf -- "$MODULE_TEMP_DIR"' EXIT
    print_step "  Creating Python virtual environment in $MODULE_TEMP_DIR/venv..."
    python3 -m venv "$MODULE_TEMP_DIR/venv"; source "$MODULE_TEMP_DIR/venv/bin/activate"
    print_step "  Installing secretstorage and dependencies..."; pip install --quiet secretstorage cryptography jeepney
    SITE_PACKAGES=$(python -c "import site; print(site.getsitepackages()[0])"); print_step "  Copying modules from $SITE_PACKAGES..."
    mkdir -p "$RES_MODULES"; rm -rf "$RES_MODULES"/*
    PACKAGES=("secretstorage" "SecretStorage"* "jeepney"* "cryptography"* "cffi"* "pycparser"*)
    for pkg in "${PACKAGES[@]}"; do
        find "$SITE_PACKAGES" -maxdepth 1 -name "$pkg" -exec cp -a {} "$RES_MODULES/" \;
    done
    print_step "  Deactivating virtual environment..."; deactivate
    print_step "  Ensuring Python packages in resources are valid (touching __init__.py)..."; find "$RES_MODULES" -type d -exec touch {}/__init__.py \;
    find "$RES_MODULES" -type d -name "__pycache__" -exec rm -rf {} +
    find "$RES_MODULES" -type f -name "*.pyc" -delete
    print_step "  Python modules prepared for embedding in $RES_MODULES"
}

# --- Build Functions ---
build_linux() {
    print_step "Building for Linux..."
    local package_dir="build/linux_pkg"
    local fyne_dist_dir="fyne-cross/dist/linux-amd64"
    local output_name="gotube-downloader"
    local fyne_archive_path="$fyne_dist_dir/$output_name.tar.xz"
    local final_archive_name="gotube-linux-amd64.tar.gz"
    local main_package="gotube-downloader"
    local fyne_tags_arg=""
    if [ -n "$BUILD_TAGS" ]; then
        fyne_tags_arg="-tags=\"$BUILD_TAGS\""
    fi

    rm -rf "$package_dir" "build/$final_archive_name"
    mkdir -p "$package_dir"

    print_step "  Running fyne-cross for Linux..."
    # shellcheck disable=SC2086 # We want word splitting for fyne_tags_arg
    fyne-cross linux -arch=amd64 -output "$output_name" $fyne_tags_arg "$main_package"

    [ ! -f "$fyne_archive_path" ] && print_error "fyne-cross Linux artifact not found: $fyne_archive_path"

    print_step "  Extracting $fyne_archive_path to $package_dir..."
    tar -xf "$fyne_archive_path" -C "$package_dir/"

    local binary_path="$package_dir/usr/local/bin/$output_name"
    [ ! -f "$binary_path" ] && print_error "Could not find Linux executable at expected path: $binary_path"
    print_step "  Found binary at $binary_path"

    if [ "$COMPRESSION" = "best" ]; then
        print_step "  Compressing binary with UPX (best)..."
        upx --best --lzma "$binary_path"
    elif [ "$COMPRESSION" = "fast" ]; then
        print_step "  Compressing binary with UPX (fast)..."
        upx --fast "$binary_path"
    else
        print_step "  Skipping compression."
    fi

	print_step "  Creating Makefile..."
	cat << EOF > "$package_dir/Makefile"
# If PREFIX isn't provided, we check for \$(DESTDIR)/usr/local and use that if it exists.
# Otherwice we fall back to using /usr.

LOCAL != test -d \$(DESTDIR)/usr/local && echo -n "/local" || echo -n ""
LOCAL ?= \$(shell test -d \$(DESTDIR)/usr/local && echo "/local" || echo "")
PREFIX ?= /usr\$(LOCAL)

Name := "$output_name"
Exec := "$output_name"
Icon := "$output_name.png"

default:
	# User install
	# Run "make user-install" to install in ~/.local/
	# Run "make user-uninstall" to uninstall from ~/.local/
	#
	# System install
	# Run "sudo make install" to install the application.
	# Run "sudo make uninstall" to uninstall the application.

install:
	install -Dm00644 usr/local/share/applications/\$(Name).desktop \$(DESTDIR)\$(PREFIX)/share/applications/\$(Name).desktop
	install -Dm00755 usr/local/bin/\$(Exec) \$(DESTDIR)\$(PREFIX)/bin/\$(Exec)
	install -Dm00644 usr/local/share/pixmaps/\$(Icon) \$(DESTDIR)\$(PREFIX)/share/pixmaps/\$(Icon)
uninstall:
	-rm \$(DESTDIR)\$(PREFIX)/share/applications/\$(Name).desktop
	-rm \$(DESTDIR)\$(PREFIX)/bin/\$(Exec)
	-rm \$(DESTDIR)\$(PREFIX)/share/pixmaps/\$(Icon)

user-install:
	install -Dm00644 usr/local/share/applications/\$(Name).desktop \$(DESTDIR)\$(HOME)/.local/share/applications/\$(Name).desktop
	install -Dm00755 usr/local/bin/\$(Exec) \$(DESTDIR)\$(HOME)/.local/bin/\$(Exec)
	install -Dm00644 usr/local/share/pixmaps/\$(Icon) \$(DESTDIR)\$(HOME)/.local/share/icons/\$(Icon)
	sed -i -e "s,Exec=\$(Exec),Exec=\$(DESTDIR)\$(HOME)/.local/bin/\$(Exec),g" \$(DESTDIR)\$(HOME)/.local/share/applications/\$(Name).desktop

user-uninstall:
	-rm \$(DESTDIR)\$(HOME)/.local/share/applications/\$(Name).desktop
	-rm \$(DESTDIR)\$(HOME)/.local/bin/\$(Exec)
	-rm \$(DESTDIR)\$(HOME)/.local/share/icons/\$(Icon)
EOF

    print_step "  Creating final Linux install archive $final_archive_name..."
    tar -czf "build/$final_archive_name" -C "$package_dir" usr Makefile

    print_step "Linux build complete: build/$final_archive_name"
    rm -rf "$package_dir"
}


build_windows() {
    print_step "Building for Windows..."
    local build_dir="build/windows_pkg"
    local fyne_dist_dir="fyne-cross/dist/windows-amd64"
    local output_name="gotube-downloader"
    local fyne_archive_path="$fyne_dist_dir/$output_name.zip"
    local executable_name="${output_name}.exe"
    local final_archive_name="gotube-windows-amd64.zip"
    local final_binary_dir="build"
    local final_binary_path="$final_binary_dir/$executable_name"
    local main_package="gotube-downloader"
    local fyne_tags_arg=""
    local current_tags="windows"
    if [ -n "$BUILD_TAGS" ]; then
        current_tags="$current_tags,$BUILD_TAGS"
    fi
    fyne_tags_arg="-tags=\"$current_tags\""


    rm -rf "$build_dir" "$final_binary_path" "build/$final_archive_name"
    mkdir -p "$build_dir" "$final_binary_dir"

    print_step "  Running fyne-cross for Windows..."
    # shellcheck disable=SC2086
    fyne-cross windows -arch=amd64 -output "$output_name" $fyne_tags_arg "$main_package"

    [ ! -f "$fyne_archive_path" ] && print_error "fyne-cross Windows artifact not found: $fyne_archive_path"

    print_step "  Extracting $fyne_archive_path to $build_dir..."
    unzip -oq "$fyne_archive_path" -d "$build_dir/"

    local extracted_binary_path="$build_dir/$executable_name"
     if [ ! -f "$extracted_binary_path" ]; then
          print_warn "Could not find Windows executable '$executable_name' directly in $build_dir after extraction. Searching recursively..."
          extracted_binary_path=$(find "$build_dir" -type f -name "$executable_name" | head -n 1)
          [ -z "$extracted_binary_path" ] || [ ! -f "$extracted_binary_path" ] && print_error "Could not find Windows executable '$executable_name' within $build_dir."
     fi
     print_step "  Found main executable at $extracted_binary_path"

    mv "$extracted_binary_path" "$final_binary_path"
    rm -rf "$build_dir"

    if [ "$COMPRESSION" = "best" ]; then
        print_step "  Compressing main executable with UPX (best)..."
        upx --best --lzma "$final_binary_path"
    elif [ "$COMPRESSION" = "fast" ]; then
        print_step "  Compressing main executable with UPX (fast)..."
        upx --fast "$final_binary_path"
    else
        print_step "  Skipping compression."
    fi

    print_step "  Creating final Windows archive $final_archive_name..."
    zip -j "build/$final_archive_name" "$final_binary_path"

    print_step "Windows build complete: build/$final_archive_name"
    rm -f "$final_binary_path"
}


# --- Cleanup ---
cleanup() {
    print_step "Cleaning up temporary build artifacts..."
    rm -rf fyne-cross
    if [ "$EMBED_BINARIES" = "true" ]; then
        rm -rf bin # Clean download temp if it exists
    fi
    # temp dir for python modules is cleaned by trap
}

# --- Main Build Process ---
main() {
    print_step "Starting build process..."
    check_requirements
    [ -d "build" ] && print_step "Cleaning previous build directory..." && rm -rf build
    mkdir -p build

    # Download dependencies for embedding (conditional)
    download_ytdlp
    download_ffmpeg_win # Now uses specific older Gyan build if --minimal-ffmpeg
    download_secretstorage

    # Run builds based on target
    if [[ "$TARGET" == "linux" || "$TARGET" == "both" ]]; then
        build_linux
    fi
    if [[ "$TARGET" == "windows" || "$TARGET" == "both" ]]; then
        build_windows
    fi

    cleanup

    print_step "Build process completed successfully!"
    echo "Output:"
    ls -lh build/ # Use -h for human-readable sizes
}

# --- Script Execution ---
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
cd "$SCRIPT_DIR"
main
trap - EXIT # Disable trap now that script finished ok